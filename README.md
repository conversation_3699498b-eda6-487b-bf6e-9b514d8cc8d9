# NEURO World

This breakthrough app uses cutting-edge research and the latest technology to heal and optimize your brain. Based on the insights of thousands of scientific papers, It helps you identify healthy lifestyle behaviors and design a comprehensive protocol built around your strengths, risks, and opportunities.

## Running the app:

- `dart run build-runner watch`
- VSCode command pallete -> Select Debug Session -> Pick Environment -> Pick Emulator

### Commands:

- `flutter gen-l10n` - re-generate localization

## Branches:

- `dev` - this is the branch that will have the changes from the App in internal state or Dev

- `release/` - this folder contains releases
  - `qa` - this branch contains the latest QA release
  - `staging` - this branch contains the latest Staging release
  - `production` - this branch contains the latest Production (store) release

### New Branching Convention:

- `${first_name}/bugs` - for any new bugfixes
- `${first_name}/${feature_name}` - for any feature implementations

## CI/CD:

### Prerequisites:

- Install Cider for pre-release flow:

```sh
pub global activate cider
```

- Install Fastlane for build automation:

```sh
xcode-select --install
brew install fastlane
```

- Update Apple Developer username in line 8 of `ios/fastlane/Matchfile`

### 1. Maintain Changelog

```sh
cider log <type> <description>
```

- type is one of: `added`, `changed`, `deprecated`, `removed`, `fixed`, or `security`

### 2. Update Versioning

```sh
cider bump <major|minor|patch|pre|release> --build={YYMMDDBB}
```

- bump using any of the presets (major, minor, etc.) or use `cider version` to manually specify a version
- use `--build` option to set build version code as timestamp. Eg. 12 Nov 2024 -> 2024111200
- BB in version code represents the build count for that day, starts at 00

### 3. Commit Changelog

Commit changes to the current version:

```sh
cider release
```

If you are pulling or replacing a previous release due to a bug or security issue, make sure to yank that release first:

```sh
cider yank <version>
```

### 4. Fastlane

```sh
cd ios | cd android
bundle exec fastlane build --env <dev|qa|staging|production>
```

#### Options for `build` lane:

- `--env <dev|qa|staging|production>` -- specifies env **(required)**
- `force:true|false` -- ignores git branch and git status clean check _(default false)_
- `clean:true|false` -- cleans builds and re-generates build\*runner outputs _(default false)_
