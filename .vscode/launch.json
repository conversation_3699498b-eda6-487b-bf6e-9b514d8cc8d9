{"version": "0.2.0", "configurations": [{"name": "<PERSON>", "program": "lib/main.dart", "request": "launch", "type": "dart", "args": ["--dart-define-from-file", ".envs/dev.json"]}, {"name": "QA", "program": "lib/main.dart", "request": "launch", "type": "dart", "args": ["--dart-define-from-file", ".envs/qa.json"]}, {"name": "Staging", "program": "lib/main.dart", "request": "launch", "type": "dart", "args": ["--dart-define-from-file", ".envs/staging.json"]}, {"name": "Production", "program": "lib/main.dart", "request": "launch", "type": "dart", "args": ["--dart-define-from-file", ".envs/production.json"]}]}