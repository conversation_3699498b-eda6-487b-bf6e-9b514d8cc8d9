plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

// re-add dart defines to android gradle env (https://github.com/flutter/flutter/issues/139289)
def env = [:];
if (project.hasProperty('dart-defines')) {
    env = env + project.property('dart-defines')
        .split(',')
        .collectEntries { entry ->
            def pair = new String(entry.decodeBase64(), 'UTF-8').split('=')
            pair.length == 2 ? [(pair.first()): pair.last()] : [:]
        }
}

task copyGoogleServicesJson(type: Copy) {
    from "flavors/${env.ENV}/google-services.json"
    into './'
}

task copyKeystore(type: Copy, dependsOn: 'copyGoogleServicesJson') {
    from "flavors/${env.ENV}/debug.keystore"
    into './'
}

tasks.whenTaskAdded { task ->
    task.dependsOn copyGoogleServicesJson 
    task.dependsOn copyKeystore
}

android {
    namespace = "com.teamsharzai.neuroworld"
    compileSdk = flutter.compileSdkVersion
    // ndkVersion = flutter.ndkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        // Sets Java compatibility to Java 11
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11
    }

    defaultConfig {
        applicationId = "${env.APP_ID}"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        resValue "string", "app_name", "${env.APP_NAME}"
        resValue "string", "host_name", "${env.HOST_NAME}"
        resValue "string", "default_web_client_id", "${env.GOOGLE_SERVER_CLIENT_ID}"
        multiDexEnabled = true
    }

    signingConfigs{
        debug {
            keyAlias "androiddebugkey"
            keyPassword "android"
            storeFile file("$rootDir/app/debug.keystore")
            storePassword "android"
        }
    }

    buildTypes {
        debug {
            signingConfig = signingConfigs.debug
        }
        release {
            // TODO: Add android release signing config
            signingConfig = signingConfigs.debug
        }
    }
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
}

flutter {
    source = "../.."
}
