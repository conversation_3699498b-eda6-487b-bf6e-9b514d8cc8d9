import Flutter
import UI<PERSON>it

@main
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
        }
        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    override func application(
        _ application: UIApplication,
        open url: URL,
        options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
            
            // Check if the URL matches the scheme
            if url.scheme == "https" {
                // We can now extract the path, like 'reset-password'
                if let host = url.host {
                    print("Deep link opened with host: \(host)")  // Example: reset-password
                    
                    // You can pass this deep link data to <PERSON>lutter to handle navigation
                }
            }
            
            return super.application(application, open: url, options: options)
        }
}
