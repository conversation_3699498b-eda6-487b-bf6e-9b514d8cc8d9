import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/app.dart';
import 'package:neuroworld/core/config/api_config.dart';
import 'package:neuroworld/core/config/firebase/dev.dart' as dev;
import 'package:neuroworld/core/config/firebase/production.dart' as prod;
import 'package:neuroworld/core/config/firebase/qa.dart' as qa;
import 'package:neuroworld/core/config/firebase/staging.dart' as staging;
import 'package:neuroworld/core/data/sources/shared_preferences.dart';
import 'package:neuroworld/core/infrastructure/extensions/future_extensions.dart';
import 'package:neuroworld/core/ui/screens/splash_screen.dart';
import 'package:neuroworld/modules/subscriptions/data/services/purchase_service.dart';

part 'core/config/app_init.dart';

void main() async {
  final container = await _mainInitializer();
  runApp(
    UncontrolledProviderScope(
      container: container,
      child: const App(),
    ),
  );
}
