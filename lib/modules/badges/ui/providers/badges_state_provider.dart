
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/badges/data/models/badge.dart';
import 'package:neuroworld/modules/badges/data/models/badge_type.dart';
import 'package:neuroworld/modules/badges/data/models/user_badge.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'badges_state_provider.g.dart';

@riverpod
List<UserBadge> badges(Ref ref) {
  final auth = ref.watch(authStateProvider);

  // If not logged in, return an empty list
  if (auth?.user.badges == null) return [];

  // Transform user.badges into proper Badge model (if needed)
  return auth!.user.badges.toList();
}

@riverpod
Map<BadgeType, UserBadge?> earnedBadgesMap(Ref ref) {
  final auth = ref.watch(authStateProvider);

  // If not logged in, return an empty list
  if (auth?.user.badges == null) return {};
  final userBadgesMap = <BadgeType, UserBadge?>{};
  for (final userBadge in auth!.user.badges) {
    final type = userBadge.badge.type;
    userBadgesMap[type] = userBadge;
  }
  for (final type in BadgeType.values) {
    if (userBadgesMap.containsKey(type)) {
      userBadgesMap[type] = userBadgesMap[type]!;
    } else {
      userBadgesMap[type] = null; // null means user has not achieved this badge yet
    }
  }
  return userBadgesMap;
}