import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/badges/ui/providers/badges_state_provider.dart';
import 'package:neuroworld/modules/badges/ui/widgets/badge_item.dart';

class BadgesScreen extends ConsumerWidget {
  const BadgesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final badges = ref.watch(earnedBadgesMapProvider);
    final entries = badges.entries.toList();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        title: Text(
          context.L.badges,
          style: TextStyles.subheading1,
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(0.5),
          child: Container(
            color: AppColors.stroke,
            height: 0.5,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Assets.svgs.backArrow.svg(),
          onPressed: () => context.pop(),
        ),
        backgroundColor: Colors.white,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          return ConstrainedBox(
            constraints: BoxConstraints.loose(Size(600, double.infinity)),
            child: GridView.builder(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                mainAxisSpacing: 40,
                mainAxisExtent: constraints.maxWidth / 3,
              ),
              itemCount: badges.length,
              itemBuilder: (context, index) {
                final entry = entries[index];
                return BadgeItem(
                  assetPath: entry.key.icon.path,
                  title: entry.key.getTitle(context),
                  badgeType: entry.key,
                  isLocked: entry.value == null,
                );
              },
            ),
          );
        },
      ),
    );
  }
}
