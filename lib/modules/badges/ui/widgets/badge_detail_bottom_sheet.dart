import 'package:flutter/material.dart';
import 'package:gpt_markdown/gpt_markdown.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/badges/data/models/badge_type.dart';

class BadgeDetailBottomSheet extends StatelessWidget {
  final BadgeType badgeType;
  final bool isLocked;

  const BadgeDetailBottomSheet({
    super.key,
    required this.badgeType,
    required this.isLocked,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: 20),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: AppColors.stroke,
              width: 0.5,
            ),
          ),
          padding: const EdgeInsets.all(12),
          child: Column(
            spacing: 8,
            children: [
              Image.asset(
                badgeType.icon.path,
                height: 110,
                fit: BoxFit.contain,
              ),
              Text(
                badgeType.getTitle(context),
                style: TextStyles.heading5,
              ),
              Container(
                decoration: BoxDecoration(
                  color:
                      isLocked ? AppColors.error : AppColors.subscriptionFree,
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                child: Text(
                  isLocked ? context.L.locked : context.L.unlocked,
                  style: TextStyles.subtitle3.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
              Divider(
                color: AppColors.stroke,
              ),
              Text(
                context.L.unlockCriteria.toUpperCase(),
                style: TextStyles.body4.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              GptMarkdown(
                context.L.badgeUnlockCriteria(
                  badgeType.tier.getStreakSpan(context),
                  badgeType.getIslandName(context),
                ),
                textAlign: TextAlign.center,
                style: TextStyles.body3.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        PrimaryButton(
          onPressed: () {
            NavService.pop(context);
          },
          child: Text(context.L.errorDialogButton),
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
