import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/badges/data/models/badge_type.dart';

class BadgeAchievedDialog extends StatelessWidget {
  final BadgeType badge;
  const BadgeAchievedDialog({super.key, required this.badge});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Assets.svgs.badges.beam.svg(),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Transform.translate(
            offset: const Offset(0, -20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              spacing: 40,
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        badge.getTitle(context),
                        style: TextStyles.heading2.copyWith(
                          color: AppColors.surfacePrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                Image.asset(
                  badge.icon.path,
                  width: 200,
                  height: 200,
                  fit: BoxFit.contain,
                ),
                Expanded(
                  child: Column(
                    spacing: 24,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "You're making great progress! Keep navigating your way to better nutrition.",
                        style: TextStyles.body1.copyWith(
                          color: AppColors.surfacePrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      PrimaryButton(
                        width: 200,
                        onPressed: () {
                          NavService.popDialog(context);
                        },
                        child: Text("Continue"),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
