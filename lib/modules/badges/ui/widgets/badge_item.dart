import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/drawers.dart';
import 'package:neuroworld/core/ui/widgets/grayscale.dart';
import 'package:neuroworld/modules/badges/data/models/badge_type.dart';
import 'package:neuroworld/modules/badges/ui/widgets/badge_detail_bottom_sheet.dart';

class BadgeItem extends StatelessWidget {
  final String assetPath;
  final String title;
  final bool isLocked;
  final BadgeType badgeType;

  const BadgeItem({
    super.key,
    required this.assetPath,
    required this.title,
    required this.badgeType,
    this.isLocked = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        await Drawers.showCustomBottomSheet<void>(
          context,
          child: BadgeDetailBottomSheet(
            badgeType: badgeType,
            isLocked: isLocked,
          ),
        );
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child:
                isLocked && assetPath != Assets.images.badges.fallbackBadge.path
                    ? Grayscale(
                        child: Image.asset(
                          assetPath,
                          fit: BoxFit.contain,
                        ),
                      )
                    : Opacity(
                        opacity: isLocked ? 0.5 : 1.0,
                        child: Image.asset(
                          assetPath,
                          fit: BoxFit.contain,
                        ),
                      ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Text(
              title.split(' ').join('\n'),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.body4.copyWith(
                  fontWeight: FontWeight.w500,
                  color: isLocked
                      ? AppColors.disabledText
                      : AppColors.textPrimary),
            ),
          ),
        ],
      ),
    );
  }
}
