import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class UnlockedBadgesCard extends StatelessWidget {
  const UnlockedBadgesCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16, left: 22, right: 22),
      width: double.infinity,
      child: OutlinedButton(
        onPressed: () {
          BadgesRoute().push(context);
        },
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.zero,
          side: BorderSide(color: AppColors.unlockedBadgesCardStroke),
          backgroundColor: AppColors.unlockedBadgesCardBg,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          minimumSize: Size.fromHeight(0),
        ),
        child: SizedBox(
          width: double.infinity,
          child: Stack(
            children: [
              Positioned(child: Assets.svgs.badges.beamSmall.svg()),
              Positioned.fill(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Row(
                    spacing: 12,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Assets.svgs.badges.royalStar.svg(),
                      Expanded(
                        child: Column(
                          spacing: 4,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              context.L.unlockedBadges,
                              style: TextStyles.heading5,
                            ),
                            Text(
                              context.L.seeAllBadges,
                              style: TextStyles.body2,
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(6),
                        child: Assets.svgs.chevronRight.svg(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
