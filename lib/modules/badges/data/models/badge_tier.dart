import 'package:flutter/material.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

enum BadgeTier {
  bronze,
  silver,
  gold,
  sapphire,
  diamond,
  platinum;

  String getStreakSpan(BuildContext context) {
    return switch (this) {
      BadgeTier.bronze => context.L.oneWeekStreak,
      BadgeTier.silver => context.L.oneMonthStreak,
      BadgeTier.gold => context.L.threeMonthStreak,
      BadgeTier.sapphire => context.L.sixMonthStreak,
      BadgeTier.diamond => context.L.nineMonthStreak,
      BadgeTier.platinum => context.L.oneYearStreak,
    };
  }
}
