
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/core/data/converters/date_converter.dart';
import 'package:neuroworld/modules/badges/data/models/badge.dart';

part 'user_badge.freezed.dart';
part 'user_badge.g.dart';

@freezed
sealed class UserBadge with _$UserBadge {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory UserBadge({
    required String id,
    required Badge badge,
    @DateConverter() required DateTime earnedAt,
  }) = _UserBadge;

  factory UserBadge.fromJson(Map<String, dynamic> json) => _$UserBadgeFromJson(json);
}