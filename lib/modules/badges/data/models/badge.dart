import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/core/data/converters/date_converter.dart';
import 'package:neuroworld/modules/badges/data/models/badge_type.dart';

part 'badge.freezed.dart';
part 'badge.g.dart';

@freezed
sealed class Badge with _$Badge {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory Badge({
    required String id,
    required String title,
    required String description,
    required String imageUrl,
    required String badgeType,
    required String level,
    required String category,
    String? cityArea,
    required bool isEarned,
    @DateConverter() DateTime? earnedAt,
  }) = _Badge;

  factory Badge.fromJson(Map<String, dynamic> json) => _$BadgeFromJson(json);
}

extension BadgeTypeFromImage on Badge {
  BadgeType get type => BadgeType.byName(imageUrl);
}
