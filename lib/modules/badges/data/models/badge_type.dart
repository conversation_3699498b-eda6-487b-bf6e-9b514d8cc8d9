import 'package:flutter/widgets.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/modules/badges/data/models/badge_tier.dart';

enum BadgeType {
  // Bronze tier
  nutritionBronze('nutrition_bronze'),
  exerciseBronze('exercise_bronze'),
  unwindBronze('unwind_bronze'),
  restoreBronze('restore_bronze'),
  optimizeBronze('optimize_bronze'),

  // Silver tier
  nutritionSilver('nutrition_silver'),
  exerciseSilver('exercise_silver'),
  unwindSilver('unwind_silver'),
  restoreSilver('restore_silver'),
  optimizeSilver('optimize_silver'),

  // Gold tier
  nutritionGold('nutrition_gold'),
  exerciseGold('exercise_gold'),
  unwindGold('unwind_gold'),
  restoreGold('restore_gold'),
  optimizeGold('optimize_gold'),

  // Sapphire tier
  nutritionSapphire('nutrition_sapphire'),
  exerciseSapphire('exercise_sapphire'),
  unwindSapphire('unwind_sapphire'),
  restoreSapphire('restore_sapphire'),
  optimizeSapphire('optimize_sapphire'),

  // Diamond tier
  nutritionDiamond('nutrition_diamond'),
  exerciseDiamond('exercise_diamond'),
  unwindDiamond('unwind_diamond'),
  restoreDiamond('restore_diamond'),
  optimizeDiamond('optimize_diamond'),

  // Platinum tier
  nutritionPlatinum('nutrition_platinum'),
  exercisePlatinum('exercise_platinum'),
  unwindPlatinum('unwind_platinum'),
  restorePlatinum('restore_platinum'),
  optimizePlatinum('optimize_platinum');

  final String name;
  const BadgeType(this.name);

  AssetGenImage get icon => switch (this) {
    BadgeType.nutritionBronze => Assets.images.badges.nutritionBadge,
    BadgeType.exerciseBronze => Assets.images.badges.exerciseBadge,
    BadgeType.unwindBronze => Assets.images.badges.unwindBadge,
    _ => Assets.images.badges.fallbackBadge,
  };

  String getTitle(BuildContext context) => switch (name.split('_')[0]) {
    'nutrition' => context.L.nutritionNavigatorTitle,
    'exercise' => context.L.exerciseExplorerTitle,
    'unwind' => context.L.mindfulnessMapperTitle,
    'restore' => context.L.sleepSurveyorTitle,
    'optimize' => context.L.optimizeObserverTitle,
    _ => throw ArgumentError('Unknown badge category'),
  };

  String getIslandName(BuildContext context) => switch (name.split('_')[0]) {
    'nutrition' => context.L.nutritionTitle,
    'exercise' => context.L.exerciseTitle,
    'unwind' => context.L.unwindTitle,
    'restore' => context.L.restoreTitle,
    'optimize' => context.L.optimizeTitle,
    _ => throw ArgumentError('Unknown badge category'),
  };

  BadgeTier get tier => BadgeTier.values.byName(name.split('_')[1]);

  static BadgeType byName(String name) =>
      BadgeType.values.firstWhere((award) => award.name == name);
    
  static Map<String, BadgeType> get typeMap =>
      Map.fromEntries(BadgeType.values.map((e) => MapEntry(e.name, e)));
}
