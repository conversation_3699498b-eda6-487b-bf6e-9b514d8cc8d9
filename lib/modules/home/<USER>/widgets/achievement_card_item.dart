import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class AchievementCardItem extends StatelessWidget {
  const AchievementCardItem({
    super.key,
    required this.label,
    required this.icon,
    required this.value,
  });

  final String label;
  final Widget icon;
  final int value;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon,
              const SizedBox(width: 8),
              Text(
                value.toString(),
                style: TextStyles.heading4,
              )
            ],
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyles.subtitle3.copyWith(
              color: AppColors.textSecondary,
              fontSize: 13,
            ),
          )
        ],
      ),
    );
  }
}
