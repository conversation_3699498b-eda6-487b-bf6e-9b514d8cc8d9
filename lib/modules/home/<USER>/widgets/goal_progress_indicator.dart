import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class GoalProgressIndicator extends StatelessWidget {
  const GoalProgressIndicator({
    super.key,
    required this.completed,
    required this.target,
  });

  final int completed;
  final int target;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Text(
          '$completed of $target',
          style: TextStyles.subheading2.copyWith(
            fontSize: 13,
            letterSpacing: -0.3,
          ),
        ),
        TweenAnimationBuilder<double>(
          tween: Tween<double>(
            begin: 0,
            end: target == 0 ? 0 : completed / target,
          ),
          curve: Curves.easeOutQuart,
          duration: const Duration(milliseconds: 1250),
          builder: (context, value, _) => SizedBox(
            width: 56,
            height: 56,
            child: CircularProgressIndicator(
              value: value,
              strokeCap: StrokeCap.round,
              backgroundColor: AppColors.unwindBlue,
              color: completed < target
                  ? AppColors.secondaryBlue
                  : AppColors.nutrition,
              strokeWidth: 5,
            ),
          ),
        ),
      ],
    );
  }
}
