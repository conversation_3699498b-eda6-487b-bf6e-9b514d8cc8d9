import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

class HomeGoalsButton extends StatelessWidget {
  const HomeGoalsButton({
    super.key,
    required this.child,
    required this.onPressed,
  });

  final Widget child;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfacePrimary,
        boxShadow: [
          BoxShadow(
            color: AppColors.dropShadow.withAlpha(30),
            spreadRadius: -4,
            blurRadius: 7,
            offset: Offset(2, 2),
          ),
        ],
      ),
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            width: 0.5,
            color: AppColors.stroke,
          ),
          overlayColor: AppColors.textSecondary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: AppColors.surfacePrimary,
          padding: const EdgeInsets.all(12),
          minimumSize: Size.zero,
        ),
        child: child,
      ),
    );
  }
}
