import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:neuroworld/core/constants/app_constants.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/secondary_button.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:neuroworld/modules/goals/data/models/repair_type.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_card_icon.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_card_title.dart';

class RepairStreakBottomSheet extends ConsumerWidget {
  const RepairStreakBottomSheet({super.key, required this.tracking});

  final DailyTracking tracking;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userPoints = ref.watch(authStateProvider)?.user.profile.points ?? 0;
    return Column(
      children: [
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              Text(
                context.L.repairStreakSheetTitle,
                style: TextStyles.heading4,
              ),
              const SizedBox(height: 8),
              Text(
                userPoints >= AppConstants.repairStreakCost
                    ? context.L.repairStreakSheetSubtitle
                    : context.L.repairStreakSheetNotEnoughPoints,
                textAlign: TextAlign.center,
                style: TextStyles.body1,
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.stroke),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(width: 6),
                    GoalCardIcon(dailyTracking: tracking),
                    GoalCardTitle(
                      title: tracking.selectedGoal.goal.title,
                      gracePeriodDays: tracking.gracePeriodDays,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 18),
        Container(height: 0.75, color: AppColors.stroke),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            children: [
              ChatBubbleWithAvatar(
                avatar: Assets.svgs.avatars.mylaWave.svg(width: 70),
                toolTipAlignment: Alignment.centerLeft,
                child: userPoints >= AppConstants.repairStreakCost
                    ? RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: context.L.repairStreakTooltip1,
                              style: TextStyles.tooltipBody.copyWith(
                                color: AppColors.chatBubbleText,
                              ),
                            ),
                            TextSpan(
                              text: "${AppConstants.repairStreakCost} NPs",
                              style: TextStyles.tooltipBody.copyWith(
                                color: AppColors.chatBubbleText,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            TextSpan(
                              text: context.L.repairStreakTooltip2,
                              style: TextStyles.tooltipBody.copyWith(
                                color: AppColors.chatBubbleText,
                              ),
                            ),
                          ],
                        ),
                      )
                    : Text(
                        context.L.repairStreakTooltipNotEnoughPoints,
                        style: TextStyles.tooltipBody.copyWith(
                          color: AppColors.chatBubbleText,
                        ),
                      ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: PrimaryButton(
                  disabled: userPoints < AppConstants.repairStreakCost,
                  onPressed: () => NavService.popDialog<RepairType>(
                    context,
                    result: RepairType.repair,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        context.L.repairStreakSheetRepairButton,
                        style: TextStyles.buttonLarge
                            .copyWith(color: Colors.white, height: 0),
                      ),
                      const SizedBox(width: 8),
                      Assets.svgs.neuroPoint.svg(),
                      const SizedBox(width: 4),
                      Text(
                        AppConstants.repairStreakCost.toString(),
                        style: TextStyles.buttonLarge.copyWith(
                          color: Colors.white,
                          height: 0,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: SecondaryButton(
                  height: 47,
                  onPressed: () => NavService.popDialog<RepairType>(
                    context,
                    result: RepairType.startFresh,
                  ),
                  child: Text(
                    context.L.repairStreakSheetStartFreshButton,
                    style: TextStyles.buttonLarge
                        .copyWith(color: AppColors.textSecondary, height: 0),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 28),
      ],
    );
  }
}
