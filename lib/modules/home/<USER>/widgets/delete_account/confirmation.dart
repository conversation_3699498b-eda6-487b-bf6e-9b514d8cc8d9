import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class ConfirmationWidget extends StatelessWidget {
  final String svgPath;
  final String title;
  final String subtitle;
  final Color svgBackgroundColor;
  final Color svgBorderColor;

  const ConfirmationWidget({
    super.key,
    required this.svgPath,
    required this.title,
    required this.subtitle,
    required this.svgBackgroundColor,
    required this.svgBorderColor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
            width: 64.0,
            height: 64.0,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: svgBackgroundColor,
              border: Border.all(color: svgBorderColor, width: 3),
              boxShadow: [
                BoxShadow(
                  color: AppColors.deleteIconDropShadow.withValues(alpha: 0.21),
                  spreadRadius: 0,
                  blurRadius: 22.65,
                  offset: Offset(0, 8),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(10.0),
              child: SvgPicture.asset(
                svgPath,
                fit: BoxFit.none,
              ),
            )),
        const SizedBox(height: 24),
        Text(title,
            textAlign: TextAlign.center,
            style: TextStyles.heading4.copyWith(color: AppColors.textPrimary)),
        const SizedBox(height: 12),
        Text(subtitle,
            textAlign: TextAlign.center,
            style: TextStyles.body2.copyWith(
              color: AppColors.textSecondary,
            )),
      ],
    );
  }
}
