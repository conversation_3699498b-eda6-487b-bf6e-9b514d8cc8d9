import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/home/<USER>/models/gender.dart';

class GenderList<PERSON>ield extends StatelessWidget {
  const GenderListField({super.key, required this.selectedGender});

  final ValueNotifier<Gender?> selectedGender;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: Gender.values.map((gender) {
        final isSelected = selectedGender.value == gender;
        return GestureDetector(
          onTap: () => selectedGender.value = gender,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              vertical: isSelected ? 9 : 10,
            ),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? AppColors.boldOrange : AppColors.stroke,
                width: isSelected ? 2 : 1,
              ),
            ),
            alignment: Alignment.center,
            child: Text(
              toBeginningOfSentenceCase(gender.name),
              style: isSelected ? TextStyles.buttonLarge : TextStyles.body1,
            ),
          ),
        );
      }).toList(),
    );
  }
}
