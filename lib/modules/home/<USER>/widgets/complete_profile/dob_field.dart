import 'package:cupertino_calendar_picker/cupertino_calendar_picker.dart';
import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/date_extensions.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/calendar_picker_styles.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/utils/date_utils.dart';

class DoBField extends StatelessWidget {
  const DoBField({super.key, required this.selectedDoB, required this.maxDate});

  final ValueNotifier<DateTime?> selectedDoB;
  final DateTime maxDate;

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) => SizedBox(
        width: double.infinity,
        child: OutlinedButton(
          style: OutlinedButton.styleFrom(
            side: BorderSide(
              width: 1,
              color: AppColors.stroke,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            foregroundColor: AppColors.textPrimary,
            padding: const EdgeInsets.all(12),
          ),
          onPressed: () {
            final renderBox = context.findRenderObject() as RenderBox?;
            showCupertinoCalendarPicker(
              context,
              widgetRenderBox: renderBox,
              minimumDateTime: DateTime(1900),
              initialDateTime: selectedDoB.value ?? maxDate.startOfDay,
              maximumDateTime: maxDate,
              containerDecoration: CalendarPickerStyles.containerDecoration,
              headerDecoration: CalendarPickerStyles.headerDecoration,
              mainColor: AppColors.boldOrange,
              actions: [
                CancelCupertinoCalendarAction(
                  label: 'Cancel',
                  decoration: CalendarActionDecoration(
                    labelStyle: TextStyles.body1,
                  ),
                  onPressed: () {},
                ),
                ConfirmCupertinoCalendarAction(
                  label: 'Confirm',
                  decoration: CalendarActionDecoration(
                    labelStyle: TextStyles.heading5,
                  ),
                  isDefaultAction: true,
                  onPressed: (selectedDate) {
                    selectedDoB.value = selectedDate;
                  },
                ),
              ],
            );
          },
          child: Row(
            children: [
              Text(
                selectedDoB.value != null
                    ? dateFormat.format(selectedDoB.value!)
                    : context.L.profileDoBHint,
                style: TextStyles.body2.copyWith(
                  color: selectedDoB.value != null
                      ? AppColors.textPrimary
                      : AppColors.disabledText,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.calendar_today_rounded,
                color: AppColors.textSecondary,
              )
            ],
          ),
        ),
      ),
    );
  }
}
