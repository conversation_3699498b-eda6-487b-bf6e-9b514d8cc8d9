import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class Rating extends StatelessWidget {
  final ValueNotifier<int?> selectedRating;

  const Rating({
    super.key,
    required this.selectedRating,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.stroke,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: EdgeInsets.fromLTRB(16, 20, 16, 24),
          child: Column(
            children: [
              Text(context.L.feedbackSubtitle,
                  textAlign: TextAlign.center,
                  style: TextStyles.body1
                      .copyWith(fontSize: 18, color: AppColors.textPrimary)),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(7, (index) {
                  return GestureDetector(
                    onTap: () {
                      selectedRating.value = index;
                    },
                    child: Container(
                      width: 36,
                      height: 36,
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        color: selectedRating.value == index
                            ? AppColors.ratingRowOne
                            : AppColors.ratingRowOneBackground,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColors.ratingRowOne),
                      ),
                      child: Center(
                        child: Text(
                          '$index',
                          style: TextStyles.body1.copyWith(
                              fontSize: 19,
                              color: selectedRating.value == index
                                  ? Colors.white
                                  : AppColors.ratingRowOne),
                        ),
                      ),
                    ),
                  );
                }),
              ),
              const SizedBox(height: 14),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(4, (index) {
                  return GestureDetector(
                    onTap: () {
                      selectedRating.value =
                          index + 7; // Update the selected value
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        color: selectedRating.value == (index + 7)
                            ? (index + 7) < 9
                                ? AppColors.ratingRowTwoLeft
                                : AppColors.ratingRowTwoRight
                            : (index + 7) < 9
                                ? AppColors.ratingRowTwoLeftBackground
                                : AppColors.ratingRowTwoRightBackground,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: (index + 7) < 9
                                ? AppColors.ratingRowTwoLeft
                                : AppColors.ratingRowTwoRight),
                      ),
                      child: Center(
                        child: Text(
                          '${index + 7}',
                          style: TextStyle(
                            color: selectedRating.value == (index + 7)
                                ? Colors.white
                                : (index + 7) < 9
                                    ? AppColors.ratingRowTwoLeft
                                    : AppColors.ratingRowTwoRight,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
