import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/subscriptions/ui/utils/subscription_helpers.dart';

enum SubscriptionTier {
  free,
  habits,
  summit,
}

class CurrentPlanCard extends ConsumerWidget {
  const CurrentPlanCard({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userTier = SubscriptionHelpers.getUserSubscriptionTier(ref);
    final tier = _getTierFromString(userTier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              context.L.currentPlanCardLabel,
              style: TextStyles.subheading2,
            ),
            const SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                color: _getBadgeColor(tier),
                borderRadius: BorderRadius.circular(4),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                _getBadgeText(tier, context),
                style: TextStyles.buttonSmall.copyWith(
                  fontWeight: FontWeight.w600,
                  letterSpacing: -0.3,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              width: 0.5,
              color: AppColors.stroke,
            ),
            gradient: LinearGradient(
              colors: _getGradientColors(tier),
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.dropShadow.withAlpha(30),
                spreadRadius: -4,
                blurRadius: 7,
                offset: Offset(2, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              Positioned(right: 0, child: Assets.svgs.lightRays.svg()),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.L.upgradeYourPlan,
                      style: TextStyles.heading3.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 14),
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: _getDescriptionBold(tier, context),
                            style: TextStyles.heading5.copyWith(
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                          TextSpan(
                            text: _getDescriptionRegular(tier, context),
                            style:
                                TextStyles.body2.copyWith(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 14),
                    SizedBox(
                      width: double.infinity,
                      child: PrimaryButton(
                        onPressed: () => context.push(SubscriptionRoute().location),
                        disabled: tier == SubscriptionTier.summit,
                        height: 40,
                        child: Text(_getButtonText(tier, context)),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  SubscriptionTier _getTierFromString(String tierString) {
    switch (tierString.toLowerCase()) {
      case 'habits':
        return SubscriptionTier.habits;
      case 'summit':
        return SubscriptionTier.summit;
      default:
        return SubscriptionTier.free;
    }
  }

  Color _getBadgeColor(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return AppColors.subscriptionFree;
      case SubscriptionTier.habits:
        return AppColors.subscriptionFree; // Using same color for now
      case SubscriptionTier.summit:
        return AppColors.subscriptionFree; // Using same color for now
    }
  }

  String _getBadgeText(SubscriptionTier tier, BuildContext context) {
    switch (tier) {
      case SubscriptionTier.free:
        return context.L.freeTier;
      case SubscriptionTier.habits:
        return context.L.habitsTier;
      case SubscriptionTier.summit:
        return context.L.academyTier; // Using Academy Tier for summit
    }
  }

  List<Color> _getGradientColors(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return [
          AppColors.planCardBeginGradient,
          AppColors.planCardMiddleGradient,
          AppColors.planCardEndGradient,
        ];
      case SubscriptionTier.habits:
        return [
          AppColors.habitCardGradientStart,
          AppColors.habitCardGradientEnd,
        ];
      case SubscriptionTier.summit:
        return [
          AppColors.summitCardGradientStart,
          AppColors.summitCardGradientEnd,
        ];
    }
  }

  String _getDescriptionBold(SubscriptionTier tier, BuildContext context) {
    switch (tier) {
      case SubscriptionTier.free:
        return context.L.unlockPremium;
      case SubscriptionTier.habits:
        return context.L.buildingStrongHabits;
      case SubscriptionTier.summit:
        return context.L.readyForNextGenBrainHealth;
    }
  }

  String _getDescriptionRegular(SubscriptionTier tier, BuildContext context) {
    switch (tier) {
      case SubscriptionTier.free:
        return context.L.unlockPremiumDescription;
      case SubscriptionTier.habits:
        return context.L.unlockAICoachingDescription;
      case SubscriptionTier.summit:
        return context.L.clinicalTierDescription;
    }
  }

  String _getButtonText(SubscriptionTier tier, BuildContext context) {
    switch (tier) {
      case SubscriptionTier.free:
        return context.L.upgrade;
      case SubscriptionTier.habits:
        return context.L.upgradeToSummitTier;
      case SubscriptionTier.summit:
        return context.L.comingSoon;
    }
  }
}
