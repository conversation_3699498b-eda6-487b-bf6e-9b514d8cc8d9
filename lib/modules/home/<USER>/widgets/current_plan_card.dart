import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/toasts.dart';

class CurrentPlanCard extends StatelessWidget {
  const CurrentPlanCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              context.L.currentPlanCardLabel,
              style: TextStyles.subheading2,
            ),
            const SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                color: AppColors.subscriptionFree,
                borderRadius: BorderRadius.circular(4),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                // TODO-iap: render using user's subscription status
                "Free",
                style: TextStyles.buttonSmall.copyWith(
                  fontWeight: FontWeight.w600,
                  letterSpacing: -0.3,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              width: 0.5,
              color: AppColors.stroke,
            ),
            gradient: LinearGradient(
              colors: [
                AppColors.planCardBeginGradient,
                AppColors.planCardMiddleGradient,
                AppColors.planCardEndGradient,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.dropShadow.withAlpha(30),
                spreadRadius: -4,
                blurRadius: 7,
                offset: Offset(2, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              Positioned(right: 0, child: Assets.svgs.lightRays.svg()),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      // TODO-iap: render using user's subscription status
                      "Upgrade your plan",
                      style: TextStyles.heading3.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 14),
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            // TODO-iap: render using user's subscription status
                            text: "Unlock Premium: ",
                            style: TextStyles.heading5.copyWith(
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                          TextSpan(
                            text:
                                // TODO-iap: render using user's subscription status
                                "Get personalized insights and exclusive features—upgrade now for the ultimate experience!",
                            style:
                                TextStyles.body2.copyWith(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 14),
                    SizedBox(
                      width: double.infinity,
                      child: PrimaryButton(
                        onPressed: () => Toasts.showComingSoonToast(context),
                        height: 40,
                        child: Text("Upgrade"),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
