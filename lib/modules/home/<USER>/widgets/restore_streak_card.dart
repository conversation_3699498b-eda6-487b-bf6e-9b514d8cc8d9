import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/goals/ui/providers/selected_date_provider.dart';
import 'package:neuroworld/modules/home/<USER>/providers/broken_streaks_provider.dart';
import 'package:neuroworld/modules/home/<USER>/utils/home_tab_utils.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/home_goals_button.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/tap_to_view.dart';

class RestoreStreakCard extends ConsumerWidget {
  const RestoreStreakCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final brokenStreaks = ref.watch(brokenStreaksProvider);

    return brokenStreaks.when(
      error: (_, __) => SizedBox.shrink(),
      loading: () => SizedBox.shrink(),
      data: (data) {
        if (data.brokenStreaksCount == 0 && data.lastDayCount == 0) {
          return SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 14),
            HomeGoalsButton(
              onPressed: () {
                ref.invalidate(selectedDateProvider);
                GoalsRoute().go(context);
              },
              child: Row(
                children: [
                  Container(
                    height: 48,
                    width: 48,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: AppColors.error.withAlpha(18),
                    ),
                    padding: EdgeInsets.all(11),
                    child:
                        data.brokenStreaksCount == 1 && data.lastDayCount == 0
                            ? Assets.svgs.streakWarning.svg()
                            : data.brokenStreaksCount == 1 &&
                                    data.lastDayCount == 1 &&
                                    !data.isLastDayBeforeExpiry
                                ? Assets.svgs.streakWarning.svg()
                                : data.brokenStreaksCount > 0 ||
                                        data.lastDayCount > 0
                                    ? Assets.svgs.streakBrokenHourglass.svg()
                                    : Assets.svgs.streakWarning.svg(),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          getBrokenStreakText(context, data),
                          style: TextStyles.subtitle3,
                        ),
                        const SizedBox(height: 4),
                        TapToView(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
