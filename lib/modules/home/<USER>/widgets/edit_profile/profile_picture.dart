// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/widgets/cached_profile_picture.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';

class ProfilePicture extends ConsumerWidget {
  const ProfilePicture({super.key, required this.size});

  final double size;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final imageUrl =
        ref.watch(authStateProvider)?.user.profile.profilePictureUrl;
    return SizedBox(
      height: size,
      width: size,
      child: CircleAvatar(
        radius: 100,
        backgroundColor:
            imageUrl == null ? AppColors.stroke : Colors.transparent,
        child: imageUrl != null
            ? CachedProfilePicture(imageUrl: imageUrl)
            : Icon(
                Icons.person,
                color: Colors.white,
                size: size / 1.5,
              ),
      ),
    );
  }
}
