import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/rounded_icon.dart';

class UpdatePictureBottomSheet extends StatelessWidget {
  const UpdatePictureBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      shrinkWrap: true,
      children: [
        const SizedBox(height: 20),
        ListTile(
          contentPadding: EdgeInsets.zero,
          onTap: () async {
            final XFile? image =
                await ImagePicker().pickImage(source: ImageSource.camera);
            // ignore: use_build_context_synchronously
            NavService.popDialog<XFile?>(context, result: image);
          },
          leading: RoundedIcon(
            size: 33,
            child: Assets.svgs.camera.svg(),
          ),
          title: Text(
            context.L.editProfilePictureCamera,
            style: TextStyles.body1,
          ),
        ),
        ListTile(
          contentPadding: EdgeInsets.zero,
          onTap: () async {
            final XFile? image =
                await ImagePicker().pickImage(source: ImageSource.gallery);
            // ignore: use_build_context_synchronously
            NavService.popDialog<XFile?>(context, result: image);
          },
          leading: RoundedIcon(
            size: 33,
            child: Assets.svgs.gallery.svg(),
          ),
          title: Text(
            context.L.editProfilePictureGallery,
            style: TextStyles.body1,
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
