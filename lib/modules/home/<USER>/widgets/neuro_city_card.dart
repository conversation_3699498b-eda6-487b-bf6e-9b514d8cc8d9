import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/tap_to_view.dart';

class NeuroCityCard extends StatelessWidget {
  const NeuroCityCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.L.neuroCityCardLabel,
          style: TextStyles.subheading2,
        ),
        const SizedBox(height: 12),
        GestureDetector(
          onTap: () => MapRoute().go(context),
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.unwindBlue,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                width: 0.5,
                color: AppColors.stroke,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.dropShadow.withAlpha(30),
                  spreadRadius: -4,
                  blurRadius: 7,
                  offset: Offset(2, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Flexible(
                        flex: 5,
                        child: Column(
                          children: [
                            Text(
                              context.L.neuroCityCardText,
                              style:
                                  TextStyles.subtitle1.copyWith(fontSize: 17),
                            ),
                            const SizedBox(height: 6),
                            TapToView()
                          ],
                        ),
                      ),
                      const SizedBox(width: 10),
                      Flexible(
                        flex: 4,
                        child: Assets.images.neuroCityCardPlot.image(),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  left: 0,
                  bottom: 0,
                  child: Assets.svgs.neuroCityCardBubbleLeft.svg(),
                ),
                Positioned(
                  right: 12,
                  top: 0,
                  child: Assets.svgs.neuroCityCardBubbleRight.svg(),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
