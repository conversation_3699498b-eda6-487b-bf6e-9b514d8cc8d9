import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class TapToView extends StatelessWidget {
  const TapToView({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          context.L.homeTabCardAction,
          style: TextStyles.subtitle3.copyWith(
            color: AppColors.boldOrange,
            height: 1.2,
            fontSize: 15,
          ),
        ),
        const SizedBox(width: 6),
        Assets.svgs.chevronRight.svg(
          height: 12,
          width: 12,
          colorFilter: ColorFilter.mode(
            AppColors.boldOrange,
            BlendMode.srcIn,
          ),
        ),
      ],
    );
  }
}
