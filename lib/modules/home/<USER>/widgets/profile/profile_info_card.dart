import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/edit_profile/profile_picture.dart';

class ProfileInfoCard extends StatelessWidget {
  final String name;
  final String email;

  const ProfileInfoCard({
    super.key,
    required this.name,
    required this.email,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            ProfilePicture(size: 42),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyles.heading5.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  email,
                  style: TextStyles.subtitle3.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),
        const Divider(
          color: AppColors.stroke,
          thickness: 0.5,
        ),
      ],
    );
  }
}
