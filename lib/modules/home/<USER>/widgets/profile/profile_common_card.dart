import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class ProfileCommonCard extends StatelessWidget {
  final String title;
  final String text;

  const ProfileCommonCard({
    super.key,
    required this.title,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyles.subtitle2.copyWith(
            color: AppColors.textSecondary.withValues(alpha: 0.64),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          text,
          style: TextStyles.body1.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        const Divider(
          color: AppColors.stroke,
          thickness: 0.5,
        ),
      ],
    );
  }
}
