import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class ProfileCompletionCard extends StatelessWidget {
  final double progress;
  final String title;
  final String description;
  final VoidCallback onTap;

  const ProfileCompletionCard({
    super.key,
    required this.progress,
    required this.title,
    required this.description,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            width: 0.5,
            color: AppColors.stroke,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 56,
              height: 56,
              child: Stack(
                fit: StackFit.expand,
                children: [
                  TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0, end: progress),
                    curve: Curves.easeOutQuart,
                    duration: const Duration(milliseconds: 1250),
                    builder: (context, value, _) => CircularProgressIndicator(
                      value: value,
                      strokeCap: StrokeCap.round,
                      backgroundColor: AppColors.unwindBlue,
                      color: AppColors.secondaryBlue,
                      strokeWidth: 6,
                    ),
                  ),
                  Center(
                    child: ShaderMask(
                      shaderCallback: (bounds) => LinearGradient(
                        colors: [
                          AppColors.profileBeginGradient,
                          AppColors.profileEndGradient,
                        ],
                        tileMode: TileMode.mirror,
                      ).createShader(bounds),
                      child: Text(
                        '${(progress * 100).toInt()}%',
                        style: TextStyles.heading5.copyWith(
                          color: Colors.white,
                          fontSize: 17,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyles.subheading2.copyWith(
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyles.body2.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Padding(
              padding: const EdgeInsets.only(top: 8, right: 8),
              child: SvgPicture.asset(
                Assets.svgs.rightArrow.path,
                width: 13,
                height: 22,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
