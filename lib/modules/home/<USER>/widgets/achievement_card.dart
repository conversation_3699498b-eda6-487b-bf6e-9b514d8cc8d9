import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/home/<USER>/utils/home_tab_utils.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/achievement_card_item.dart';

class AchievementCard extends ConsumerWidget {
  const AchievementCard({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(authStateProvider)?.user;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 14),
      decoration: BoxDecoration(
        color: AppColors.surfaceSecondary,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          width: 0.5,
          color: AppColors.stroke,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.dropShadow.withAlpha(30),
            spreadRadius: -4,
            blurRadius: 7,
            offset: Offset(2, 2),
          ),
        ],
      ),
      child: IntrinsicHeight(
        child: Row(
          children: [
            AchievementCardItem(
              icon: Assets.svgs.fire.svg(width: 24, height: 24),
              label: context.L.streaksAchievementLabel,
              value: getActiveStreaks(user?.selectedGoals),
            ),
            Container(
              width: 0.5,
              color: AppColors.textTertiary,
              height: double.infinity,
            ),
            AchievementCardItem(
              icon: Assets.svgs.badge.svg(),
              label: context.L.badgesAchievementLabel,
              value: 0,
            ),
            Container(
              width: 0.5,
              color: AppColors.textTertiary,
              height: double.infinity,
            ),
            AchievementCardItem(
              icon: Assets.svgs.neuroPoint.svg(),
              label: context.L.pointsAchievementLabel,
              value: user?.profile.points ?? 0,
            ),
          ],
        ),
      ),
    );
  }
}
