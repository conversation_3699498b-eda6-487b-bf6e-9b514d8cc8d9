import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/badges/data/models/badge_type.dart';
import 'package:neuroworld/modules/badges/ui/providers/badges_state_provider.dart';
import 'package:neuroworld/modules/badges/ui/widgets/badge_item.dart';

class BadgesCard extends ConsumerWidget {
  const BadgesCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final badges = ref.watch(badgesProvider);
    final earnedBadges = ref.watch(earnedBadgesMapProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "${context.L.badges} (",
                    style: TextStyles.subheading2,
                  ),
                  TextSpan(
                    text: badges.length.toString(),
                    style: TextStyles.subheading2.copyWith(
                      color: AppColors.secondaryBlue,
                    ),
                  ),
                  TextSpan(
                    text: "/${BadgeType.values.length})",
                    style: TextStyles.subheading2,
                  ),
                ],
              ),
            ),
            const Spacer(),
            InkWell(
              onTap: () {
                BadgesRoute().push(context);
              },
              child: Text(
                "View all",
                style: TextStyles.body2.copyWith(
                  color: AppColors.secondaryBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            )
          ],
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(12),
          height: 140,
          decoration: BoxDecoration(
            color: AppColors.surfacePrimary,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              width: 0.5,
              color: AppColors.stroke,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.dropShadow.withAlpha(30),
                spreadRadius: -4,
                blurRadius: 7,
                offset: Offset(2, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: earnedBadges.entries
                .slices(3)
                .first
                .map(
                  (entry) => Expanded(
                    child: BadgeItem(
                      assetPath: entry.key.icon.path,
                      title: entry.key.getTitle(context),
                      badgeType: entry.key,
                      isLocked: entry.value == null,
                    ),
                  ),
                )
                .toList(),
          ),
        ),
      ],
    );
  }
}
