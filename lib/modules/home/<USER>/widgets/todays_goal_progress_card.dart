import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/loader.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/data/services/goals_service.dart';
import 'package:neuroworld/modules/goals/ui/providers/selected_date_provider.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/goal_progress_indicator.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/home_goals_button.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/tap_to_view.dart';

class TodaysGoalProgressCard extends ConsumerWidget {
  const TodaysGoalProgressCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final todaysTrackingCount = ref.watch(getTodaysTrackingProvider);
    final userGoalsLength = ref
            .watch(authStateProvider.select(
              (auth) => auth?.user.selectedGoals.where((goal) => goal.isActive),
            ))
            ?.length ??
        0;
    final isLoading =
        todaysTrackingCount.isLoading || !todaysTrackingCount.hasValue;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.L.todaysGoalProgressCardLabel,
          style: TextStyles.subheading2,
        ),
        const SizedBox(height: 12),
        HomeGoalsButton(
          onPressed: () {
            ref.invalidate(selectedDateProvider);
            GoalsRoute().go(context);
          },
          child: Row(
            children: [
              Assets.svgs.avatars.mylaWave.svg(height: 60),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isLoading ||
                              todaysTrackingCount.requireValue.length <
                                  userGoalsLength
                          ? context.L.todaysGoalProgressCardIncompleteLabel
                          : context.L.todaysGoalProgressCardCompleteLabel,
                      style: TextStyles.subtitle3,
                    ),
                    const SizedBox(height: 4),
                    TapToView(),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              isLoading
                  ? Loader(width: 56, height: 56)
                  : GoalProgressIndicator(
                      completed: todaysTrackingCount.requireValue.length,
                      target: userGoalsLength,
                    ),
            ],
          ),
        ),
      ],
    );
  }
}
