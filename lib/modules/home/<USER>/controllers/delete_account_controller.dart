import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'delete_account_controller.g.dart';

@riverpod
class DeleteAccountController extends _$DeleteAccountController {
  @override
  FutureOr<int?> build() => null;

  Future<int?> deleteUser() async {
    state = const AsyncLoading();
    try {
      final statusCode = await ref.read(authServiceProvider).deleteUser();
      state = AsyncData(statusCode);
      return statusCode;
    } catch (e, s) {
      state = AsyncError(e, s);
      return null;
    }
  }
}
