import 'package:neuroworld/modules/auth/data/models/request/update_profile_request.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/home/<USER>/models/gender.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'complete_profile_controller.g.dart';

@riverpod
class CompleteProfileController extends _$CompleteProfileController {
  @override
  FutureOr<bool?> build() => null;

  Future<void> completeProfile(DateTime birthDate, Gender gender) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      final newAuth = await ref.read(authServiceProvider).updateUserProfile(
            UpdateProfileRequest(birthDate: birthDate, gender: gender),
          );
      ref.read(authStateProvider.notifier).login(newAuth);
      return true;
    });
  }
}
