import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/home/<USER>/models/response/feedback_response.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'feedback_controller.g.dart';

@riverpod
class FeedbackController extends _$FeedbackController {
  @override
  FutureOr<FeedbackResponse?> build() => null;

  Future<FeedbackResponse?> postFeedback(String? comment, int score) async {
    state = const AsyncLoading();
    try {
      final feedback =
          await ref.read(authServiceProvider).postFeedback(comment, score);
      state = AsyncData(feedback);
      return state.value!;
    } catch (e, s) {
      state = AsyncError(e, s);
      return state.valueOrNull;
    }
  }
}
