import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:neuroworld/core/data/validators/name_validator.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/home/<USER>/form/edit_profile_form.dart';
import 'package:neuroworld/modules/home/<USER>/models/gender.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'edit_profile_form_controller.g.dart';

@riverpod
class EditProfileFormController extends _$EditProfileFormController {
  @override
  EditProfileForm build() {
    final profile = ref.watch(authStateProvider)?.user.profile;
    return EditProfileForm(
      name: profile != null
          ? NameInput.pure(profile.fullName!)
          : NameInput.pure(),
      birthDate: profile?.birthDate,
      gender: profile?.gender,
      key: GlobalKey<FormState>(),
    );
  }

  set name(String name) => state = state.copyWith(name: NameInput.dirty(name));

  set birthDate(DateTime birthDate) =>
      state = state.copyWith(birthDate: birthDate);

  set gender(Gender gender) => state = state.copyWith(gender: gender);

  void resetStatus() =>
      state = state.copyWith(status: FormzSubmissionStatus.initial);

  Future<void> submit() async {
    if (state.status != FormzSubmissionStatus.inProgress &&
        state.status != FormzSubmissionStatus.success &&
        state.name.isValid) {
      state = state.copyWith(status: FormzSubmissionStatus.inProgress);
      try {
        final newAuth = await ref
            .read(authServiceProvider)
            .updateUserProfile(state.toRequest());
        ref.read(authStateProvider.notifier).login(newAuth);
        state = state.copyWith(status: FormzSubmissionStatus.success);
      } catch (e) {
        state = state.copyWith(status: FormzSubmissionStatus.failure);
      }
    }
  }
}
