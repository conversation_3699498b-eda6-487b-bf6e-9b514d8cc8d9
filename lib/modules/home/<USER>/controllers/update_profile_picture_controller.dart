import 'dart:ui';

import 'package:croppy/croppy.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'update_profile_picture_controller.g.dart';

@riverpod
class UpdateProfilePictureController extends _$UpdateProfilePictureController {
  @override
  FutureOr<bool?> build() => null;

  Future<void> updatePicture(
    CropImageResult result,
    String fileName,
  ) async {
    state = const AsyncLoading();
    try {
      final data = await result.uiImage.toByteData(format: ImageByteFormat.png);
      final bytes = data!.buffer.asUint8List();

      final compressed = await FlutterImageCompress.compressWithList(
        bytes,
        minHeight: 1536,
        minWidth: 1536,
      );

      final newAuth = await ref
          .read(authServiceProvider)
          .updateUserProfilePicture(compressed, fileName);

      ref.read(authStateProvider.notifier).login(newAuth);
      state = AsyncData(true);
    } catch (e, st) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      );
      state = AsyncError(e, st);
    }
  }
}
