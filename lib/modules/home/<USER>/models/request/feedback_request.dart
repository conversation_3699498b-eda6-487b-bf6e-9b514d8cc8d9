import 'package:freezed_annotation/freezed_annotation.dart';

part 'feedback_request.freezed.dart';
part 'feedback_request.g.dart';

@freezed
abstract class FeedbackRequest with _$FeedbackRequest {
  const factory FeedbackRequest({
    required String comment,
    required int score,
  }) = _FeedbackRequest;

  factory FeedbackRequest.fromJson(Map<String, dynamic> json) =>
      _$FeedbackRequestFromJson(json);
}
