import 'package:freezed_annotation/freezed_annotation.dart';

part 'feedback_response.freezed.dart';
part 'feedback_response.g.dart';

@freezed
sealed class FeedbackResponse with _$FeedbackResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory FeedbackResponse({
    required String message,
  }) = _FeedbackResponse;

  factory FeedbackResponse.fromJson(Map<String, dynamic> json) =>
      _$FeedbackResponseFromJson(json);
}
