import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:flutter/material.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';
import 'package:neuroworld/modules/home/<USER>/models/broken_streaks_info.dart';

int getActiveStreaks(IList<SelectedGoal>? goals) => goals == null
    ? 0
    : goals.fold(
        0,
        (previousStreakSum, goal) =>
            !goal.isActive || previousStreakSum > goal.streak
                ? previousStreakSum
                : goal.streak,
      );

String getBrokenStreakText(BuildContext context, BrokenStreaksInfo info) {
  if (info.isLastDayBeforeExpiry) {
    return "${context.L.restoreStreakCardBrokenStreaks1} 1 ${context.L.restoreStreakCardBrokenStreaks2Single}";
  } else if (info.isLastDayBeforeExpiry || info.lastDayCount == 0) {
    return "${context.L.restoreStreakCardBrokenStreaks1} ${info.brokenStreaksCount} ${info.brokenStreaksCount == 1 ? context.L.restoreStreakCardBrokenStreaks2Single : context.L.restoreStreakCardBrokenStreaks2Multiple}";
  } else {
    return info.lastDayCount == 1
        ? context.L.restoreStreakCardLastDaySingle
        : "${info.lastDayCount} ${context.L.restoreStreakCardLastDayMultiple}";
  }
}
