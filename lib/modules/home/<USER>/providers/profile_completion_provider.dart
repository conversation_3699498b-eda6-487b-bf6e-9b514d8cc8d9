import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'profile_completion_provider.g.dart';

@Riverpod(keepAlive: true)
double profileCompletion(Ref ref) {
  final userProfile = ref.watch(authStateProvider)?.user.profile;
  // email and name should always be complete so assume 1/2 complete
  double completion = 0.5;

  if (userProfile?.birthDate != null) completion += 0.25;
  if (userProfile?.gender != null) completion += 0.25;

  return completion;
}
