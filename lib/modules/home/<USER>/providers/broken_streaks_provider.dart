import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';
import 'package:neuroworld/modules/home/<USER>/models/broken_streaks_info.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'broken_streaks_provider.g.dart';

@riverpod
Future<BrokenStreaksInfo> brokenStreaks(Ref ref) async {
  final selectedGoals = ref.watch(authStateProvider)?.user.selectedGoals ??
      const IList<SelectedGoal>.empty();
  final today = DateTime.now();
  final selectedIndex = today.weekday % 7;

  int brokenStreaksCount = 0;
  int lastDayCount = 0;
  bool isLastDayBeforeExpiry = false;
  for (final goal in selectedGoals) {
    if (goal.firstTrackedDate == null ||
        goal.firstTrackedDate!.isAfter(today)) {
      continue;
    }
    final offset = goal.startDate!.weekday % 7;
    final currentIndex = (selectedIndex - offset) % 7;

    final streakInfo = goal.getStreakInfo(today);

    // last day or second-last day as per product
    if (currentIndex >= 5 &&
        !goal.streakAwarded &&
        !streakInfo.isStreakBroken) {
      lastDayCount++;
    }
    if (streakInfo.gracePeriodDays > 0) brokenStreaksCount++;
    if (streakInfo.gracePeriodDays == 1) isLastDayBeforeExpiry = true;
  }

  return BrokenStreaksInfo(
    brokenStreaksCount: brokenStreaksCount,
    lastDayCount: lastDayCount,
    isLastDayBeforeExpiry: isLastDayBeforeExpiry,
  );
}
