import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/input_decorations.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/labeled_input_field.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/toasts.dart';
import 'package:neuroworld/modules/home/<USER>/controllers/feedback_controller.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/feedback/rating.dart';

class FeedbackScreen extends HookConsumerWidget {
  const FeedbackScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedRating = useState<int>(-1);
    final feedbackTextEditingController = useTextEditingController();
    final wordCount = useState<int>(0);
    final isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;

    ref.easyListen(
      feedbackControllerProvider,
      loadingText: context.L.submittingFeedbackLoadingText,
      whenData: (data) {
        if (data != null) {
          NavService.pop(context);
          Toasts.showSuccessToast(context, data.message);
        }
      },
    );

    feedbackTextEditingController.addListener(() {
      final wordCountValue = feedbackTextEditingController.text
          .split(RegExp(r'\s+'))
          .where((word) => word.isNotEmpty)
          .length;
      wordCount.value = wordCountValue > 150 ? 150 : wordCountValue;
    });

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size(0, 80),
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: AppBar(
            scrolledUnderElevation: 0,
            title: Text(
              context.L.feedbackScreenTitle,
              style: TextStyles.subheading1,
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(0.5),
              child: Container(
                color: AppColors.stroke,
                height: 0.5,
              ),
            ),
            centerTitle: true,
            leading: IconButton(
              icon: Assets.svgs.backArrow.svg(),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.white,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(19, 16, 19, 24),
        child: CustomScrollView(
          physics: isKeyboardVisible
              ? AlwaysScrollableScrollPhysics()
              : NeverScrollableScrollPhysics(), // Control scrolling based on keyboard visibility
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                children: [
                  Rating(
                    selectedRating: selectedRating,
                  ),
                  const SizedBox(height: 20),
                  LabeledInputField(
                    label: context.L.feedbackScreenSubtitle2,
                    inputField: TextField(
                      controller: feedbackTextEditingController,
                      onChanged: (value) {
                        List<String> words = value.split(RegExp(r'\s+'));
                        if (words.length > 10) {
                          feedbackTextEditingController.text =
                              value.substring(0, value.lastIndexOf(' '));
                          feedbackTextEditingController.selection =
                              TextSelection.fromPosition(TextPosition(
                                  offset: feedbackTextEditingController
                                      .text.length));
                        }
                      },
                      style: TextStyles.inputValue,
                      cursorColor: Colors.black,
                      maxLines: 5,
                      decoration: InputDecorations.textFieldDecoration.copyWith(
                        hintText: context.L.feedbackShareExperience,
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.stroke),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.stroke),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        '${wordCount.value}/150',
                        style: TextStyles.subheading2.copyWith(
                          fontSize: 14,
                          color: AppColors.textTertiary,
                        ),
                      )),
                  const SizedBox(height: 43),
                  SizedBox(
                    width: double.infinity,
                    child: PrimaryButton(
                      onPressed: () {
                        ref
                            .watch(feedbackControllerProvider.notifier)
                            .postFeedback(
                              feedbackTextEditingController.text,
                              selectedRating.value,
                            );
                      },
                      disabled: !(selectedRating.value >= 0),
                      child: Text(context.L.feedbackButtonText),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
