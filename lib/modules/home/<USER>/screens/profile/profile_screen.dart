import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/utils/date_utils.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/home/<USER>/providers/profile_completion_provider.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/profile/profile_common_card.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/profile/profile_completion_card.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/profile/profile_info_card.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(authStateProvider)?.user;
    final profileCompletion = ref.watch(profileCompletionProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size(0, 80),
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: AppBar(
            title: Text(
              context.L.profileScreenTitle,
              style: TextStyles.subheading1,
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(0.5),
              child: Container(
                color: AppColors.stroke,
                height: 0.5,
              ),
            ),
            centerTitle: true,
            leading: IconButton(
              icon: Assets.svgs.backArrow.svg(),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.white,
            actions: [
              IconButton(
                icon: Assets.svgs.edit.svg(),
                onPressed: () => EditProfileRoute().push(context),
              ),
              const SizedBox(width: 8),
            ],
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(24, 16, 24, 24),
        child: Column(
          children: [
            if (profileCompletion < 1)
              ProfileCompletionCard(
                progress: profileCompletion,
                title: context.L.completeProfileCardTitle,
                description: context.L.completeProfileCardBody,
                onTap: () {
                  context.push(CompleteProfileRoute().location);
                },
              ),
            SizedBox(height: profileCompletion < 1 ? 24 : 12),
            ProfileInfoCard(
              name: user?.profile.fullName ?? "",
              email: user?.email ?? "",
            ),
            const SizedBox(height: 12),
            ProfileCommonCard(
              title: context.L.profileEmail,
              text: user?.email ?? "-",
            ),
            const SizedBox(height: 12),
            ProfileCommonCard(
              title: context.L.profileName,
              text: user?.profile.fullName ?? "-",
            ),
            const SizedBox(height: 12),
            ProfileCommonCard(
              title: context.L.profileDoB,
              text: user?.profile.birthDate != null
                  ? dateFormat.format(user!.profile.birthDate!)
                  : "-",
            ),
            const SizedBox(height: 12),
            ProfileCommonCard(
              title: context.L.profileGender,
              text:
                  toBeginningOfSentenceCase(user?.profile.gender?.name ?? "-"),
            ),
          ],
        ),
      ),
    );
  }
}
