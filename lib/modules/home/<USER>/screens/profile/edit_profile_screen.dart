import 'dart:io';

import 'package:croppy/croppy.dart';
import 'package:cupertino_calendar_picker/cupertino_calendar_picker.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:formz/formz.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/app_error_extension.dart';
import 'package:neuroworld/core/infrastructure/extensions/date_extensions.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/calendar_picker_styles.dart';
import 'package:neuroworld/core/ui/theme/input_decorations.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/utils/date_utils.dart';
import 'package:neuroworld/core/ui/widgets/dialogs.dart';
import 'package:neuroworld/core/ui/widgets/drawers.dart';
import 'package:neuroworld/core/ui/widgets/labeled_input_field.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/toasts.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/home/<USER>/form/edit_profile_form.dart';
import 'package:neuroworld/modules/home/<USER>/models/gender.dart';
import 'package:neuroworld/modules/home/<USER>/controllers/edit_profile_form_controller.dart';
import 'package:neuroworld/modules/home/<USER>/controllers/update_profile_picture_controller.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/edit_profile/profile_picture.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/edit_profile/update_picture_bottom_sheet.dart';

class EditProfileScreen extends HookConsumerWidget {
  const EditProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<EditProfileForm>(editProfileFormControllerProvider, (_, state) {
      switch (state.status) {
        case FormzSubmissionStatus.inProgress:
          Dialogs.showLoadingDialog(
            context,
            message: context.L.updateProfileLoadingText,
          );
        case FormzSubmissionStatus.success:
          NavService.popDialog(context);
          NavService.pop(context);
        case FormzSubmissionStatus.failure:
          NavService.popDialog(context);
          Toasts.showErrorToast(
            context,
            message:
                state.error?.errorMessage(context) ?? context.L.unknownError,
          );
          ref.read(editProfileFormControllerProvider.notifier).resetStatus();
        default:
          break;
      }
    });

    ref.easyListen(
      updateProfilePictureControllerProvider,
      loadingText: context.L.updateProfilePicureLoadingText,
      whenData: (data) => SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
    );

    // add 5 to account for potential leap years (its ok if we overshoot by 1)
    final maxDate = DateTime.now().subtract(Duration(days: 365 * 18 + 5));

    final form = ref.watch(editProfileFormControllerProvider);
    final user = ref.watch(authStateProvider)?.user;

    final selectedDoB = useState<DateTime?>(user?.profile.birthDate);
    final selectedGender = useState<Gender?>(user?.profile.gender);

    // return SizedBox.expand();

    return Scaffold(
      backgroundColor: AppColors.surfacePrimary,
      appBar: PreferredSize(
        preferredSize: const Size(0, 80),
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: AppBar(
            title: Text(
              context.L.editProfileScreenTitle,
              style: TextStyles.subheading2,
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(0.5),
              child: Container(color: AppColors.stroke, height: 0.5),
            ),
            centerTitle: true,
            leading: IconButton(
              icon: Assets.svgs.backArrow.svg(),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.white,
            scrolledUnderElevation: 0,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(24, 0, 24, 0),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                children: [
                  const SizedBox(height: 40),
                  Center(
                    child: ProfilePicture(size: 72),
                  ),
                  const SizedBox(height: 12),
                  GestureDetector(
                    onTap: () => _onUpdateProfilePicture(ref, context),
                    child: Text(
                      user?.profile.profilePictureUrl == null
                          ? context.L.editProfileScreenAddPicture
                          : context.L.editProfileScreenUpdatePicture,
                      style: TextStyle(
                        color: AppColors.secondaryBlue,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  LabeledInputField(
                    label: context.L.profileEmail,
                    inputField: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppColors.stroke,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        user?.email ?? "-",
                        style: TextStyles.body2
                            .copyWith(color: AppColors.disabledText),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  LabeledInputField(
                    label: context.L.profileName,
                    inputField: TextFormField(
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      initialValue: user?.profile.fullName,
                      onChanged: (value) => ref
                          .read(editProfileFormControllerProvider.notifier)
                          .name = value,
                      validator: (value) =>
                          form.name.validator(value ?? '')?.message(context),
                      style: TextStyles.inputValue,
                      cursorColor: Colors.black,
                      keyboardType: TextInputType.name,
                      textInputAction: TextInputAction.next,
                      enabled: !form.status.isInProgressOrSuccess,
                      decoration: InputDecorations.textFieldDecoration.copyWith(
                        hintText: context.L.profileNameHint,
                        contentPadding: EdgeInsets.all(12),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  LabeledInputField(
                    label: context.L.profileDoBField,
                    inputField: Builder(
                      builder: (context) => SizedBox(
                        width: double.infinity,
                        child: OutlinedButton(
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(
                              width: 1,
                              color: AppColors.stroke,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            foregroundColor: AppColors.textPrimary,
                            padding: const EdgeInsets.all(12),
                          ),
                          onPressed: () {
                            final renderBox =
                                context.findRenderObject() as RenderBox?;
                            showCupertinoCalendarPicker(
                              context,
                              widgetRenderBox: renderBox,
                              minimumDateTime: DateTime(1900),
                              initialDateTime:
                                  selectedDoB.value ?? maxDate.startOfDay,
                              maximumDateTime: maxDate,
                              containerDecoration:
                                  CalendarPickerStyles.containerDecoration,
                              headerDecoration:
                                  CalendarPickerStyles.headerDecoration,
                              mainColor: AppColors.boldOrange,
                              actions: [
                                CancelCupertinoCalendarAction(
                                  label: 'Cancel',
                                  decoration: CalendarActionDecoration(
                                    labelStyle: TextStyles.body1,
                                  ),
                                  onPressed: () {},
                                ),
                                ConfirmCupertinoCalendarAction(
                                  label: 'Confirm',
                                  decoration: CalendarActionDecoration(
                                    labelStyle: TextStyles.heading5,
                                  ),
                                  isDefaultAction: true,
                                  onPressed: (selectedDate) {
                                    selectedDoB.value = selectedDate;
                                    ref
                                        .read(editProfileFormControllerProvider
                                            .notifier)
                                        .birthDate = selectedDate;
                                  },
                                ),
                              ],
                            );
                          },
                          child: Row(
                            children: [
                              Text(
                                selectedDoB.value != null
                                    ? dateFormat.format(selectedDoB.value!)
                                    : context.L.profileDoBHint,
                                style: TextStyles.body2.copyWith(
                                  color: selectedDoB.value != null
                                      ? AppColors.textPrimary
                                      : AppColors.disabledText,
                                ),
                              ),
                              const Spacer(),
                              Icon(
                                Icons.calendar_today_rounded,
                                color: AppColors.textSecondary,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  LabeledInputField(
                    label: context.L.profileGenderField,
                    inputField: DropdownButton2<Gender>(
                      value: selectedGender.value,
                      onChanged: (value) {
                        if (value != null) {
                          selectedGender.value = value;
                          ref
                              .read(editProfileFormControllerProvider.notifier)
                              .gender = value;
                        }
                      },
                      items: Gender.values
                          .map(
                            (gender) => DropdownMenuItem(
                              value: gender,
                              child: Text(
                                toBeginningOfSentenceCase(gender.name),
                              ),
                            ),
                          )
                          .toList(),
                      isExpanded: true,
                      buttonStyleData: ButtonStyleData(
                        padding: const EdgeInsets.only(right: 12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: AppColors.stroke),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      hint: Text(
                        context.L.profileGenderHint,
                        style: TextStyles.body2
                            .copyWith(color: AppColors.disabledText),
                      ),
                      iconStyleData: IconStyleData(
                        icon: Assets.svgs.chevronDown.svg(),
                      ),
                      underline: SizedBox.shrink(),
                      dropdownStyleData: DropdownStyleData(
                        offset: Offset(0, -8),
                        padding: const EdgeInsets.symmetric(
                          vertical: 10,
                          horizontal: 12,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: AppColors.stroke),
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(20),
                              spreadRadius: 0,
                              blurRadius: 8,
                              offset: Offset(-4, 4),
                            ),
                          ],
                        ),
                      ),
                      menuItemStyleData: MenuItemStyleData(
                        padding: EdgeInsets.zero,
                        selectedMenuItemBuilder: (context, child) => Container(
                          decoration: BoxDecoration(
                            color: AppColors.surfaceSecondary,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            children: [
                              const SizedBox(width: 12),
                              Expanded(child: child),
                              Assets.svgs.checkRounded.svg(),
                              const SizedBox(width: 16),
                            ],
                          ),
                        ),
                      ),
                      style: TextStyles.inputValue.copyWith(
                        height: 0,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
            SliverFillRemaining(
              hasScrollBody: false,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Spacer(),
                  SizedBox(
                    width: double.infinity,
                    child: PrimaryButton(
                      onPressed: () => ref
                          .read(editProfileFormControllerProvider.notifier)
                          .submit(),
                      disabled: form.name.isNotValid,
                      child: Text(context.L.editProfileScreenButton),
                    ),
                  ),
                  AnimatedSize(
                    duration: Duration(milliseconds: 250),
                    curve: Curves.easeOutQuad,
                    child: SizedBox(
                      height: MediaQuery.of(context).viewInsets.bottom == 0
                          ? 48
                          : 10,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onUpdateProfilePicture(
    WidgetRef ref,
    BuildContext context,
  ) async {
    {
      final image = await Drawers.showCustomBottomSheet<XFile?>(
        context,
        child: UpdatePictureBottomSheet(),
      );
      if (image != null) {
        // ignore: use_build_context_synchronously
        CropImageResult? result = await _showNativeImageCropper(context, image);
        if (result != null) {
          ref
              .read(updateProfilePictureControllerProvider.notifier)
              .updatePicture(result, image.name);
        }
        // if cropper is cancelled, wait a bit to re-set theme so it doesn't accidentally set cropper's theme again
        Future.delayed(
          Duration(milliseconds: 250),
          () => SystemChrome.setSystemUIOverlayStyle(
            SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.dark,
            ),
          ),
        );
      }
    }
  }

  Future<CropImageResult?> _showNativeImageCropper(
    BuildContext context,
    XFile image,
  ) async {
    late CropImageResult? result;
    final file = File(image.path);
    if (Platform.isAndroid) {
      result = await showMaterialImageCropper(
        context,
        imageProvider: FileImage(file),
        allowedAspectRatios: [CropAspectRatio(width: 1, height: 1)],
        // cropPathFn: (builder, size) =>
        //     ellipseCropShapeFn(builder, size),
      );
    } else {
      result = await showCupertinoImageCropper(
        context,
        imageProvider: FileImage(file),
        allowedAspectRatios: [CropAspectRatio(width: 1, height: 1)],
        cropPathFn: (builder, size) => ellipseCropShapeFn(builder, size),
      );
    }

    return result;
  }
}
