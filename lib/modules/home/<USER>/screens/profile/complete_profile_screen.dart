import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/labeled_input_field.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/home/<USER>/models/gender.dart';
import 'package:neuroworld/modules/home/<USER>/controllers/complete_profile_controller.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/complete_profile/dob_field.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/complete_profile/gender_list_field.dart';

class CompleteProfileScreen extends HookConsumerWidget {
  const CompleteProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.easyListen(
      completeProfileControllerProvider,
      loadingText: context.L.updateProfileLoadingText,
      whenData: (data) {
        if (data != null && data == true) {
          NavService.pop(context);
        }
      },
    );

    // add 5 to account for potential leap years (its ok if we overshoot by 1)
    final maxDate = DateTime.now().subtract(Duration(days: 365 * 18 + 5));

    final user = ref.watch(authStateProvider)?.user;
    final selectedDoB = useState<DateTime?>(user?.profile.birthDate);
    final selectedGender = useState<Gender?>(user?.profile.gender);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size(0, 80),
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: AppBar(
            title: Text(
              context.L.completeProfileScreenTitle,
              style: TextStyles.subheading2,
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(0.5),
              child: Container(color: AppColors.stroke, height: 0.5),
            ),
            centerTitle: true,
            leading: IconButton(
              icon: Assets.svgs.backArrow.svg(),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.white,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(24, 24, 24, 0),
        child: Column(
          children: [
            Expanded(
              child: Column(
                children: [
                  if (user?.profile.birthDate == null)
                    LabeledInputField(
                      label: context.L.profileDoBField,
                      inputField:
                          DoBField(selectedDoB: selectedDoB, maxDate: maxDate),
                    ),
                  if (user?.profile.birthDate == null &&
                      user?.profile.gender == null)
                    const SizedBox(height: 24),
                  if (user?.profile.gender == null)
                    LabeledInputField(
                      label: context.L.profileGenderField,
                      inputField:
                          GenderListField(selectedGender: selectedGender),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 15),
            Padding(
              padding: const EdgeInsets.only(bottom: 64),
              child: SizedBox(
                width: double.infinity,
                child: PrimaryButton(
                  onPressed: () => ref
                      .watch(completeProfileControllerProvider.notifier)
                      .completeProfile(
                        selectedDoB.value!,
                        selectedGender.value!,
                      ),
                  disabled:
                      selectedDoB.value == null || selectedGender.value == null,
                  child: Text(context.L.completeProfileScreenButton),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
