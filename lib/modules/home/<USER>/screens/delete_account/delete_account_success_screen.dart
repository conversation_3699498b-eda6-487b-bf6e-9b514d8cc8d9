import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/delete_account/confirmation.dart';

class DeleteAccountSuccessScreen extends HookConsumerWidget {
  const DeleteAccountSuccessScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Padding(
        padding: EdgeInsets.fromLTRB(28, 270, 28, 0),
        child: Column(
          children: [
            ConfirmationWidget(
              svgBackgroundColor: AppColors.secondaryBlue,
              svgBorderColor: AppColors.deleteAccountSuccessIconBorder,
              svgPath: Assets.svgs.checkIcon.path,
              title: context.L.deleteAccountConfirmationTitle,
              subtitle: context.L.deleteAccountConfirmSubtitle,
            ),
            Spacer(),
            Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: SafeArea(
                child: SizedBox(
                  width: double.infinity,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: PrimaryButton(
                          onPressed: () {
                            LandingRoute().go(context);
                          },
                          child: Text(context.L.deleteAccountSuccessButton),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
