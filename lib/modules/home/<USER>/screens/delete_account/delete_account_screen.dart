import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/secondary_button.dart';
import 'package:neuroworld/modules/auth/ui/providers/sign_out_provider.dart';
import 'package:neuroworld/modules/home/<USER>/controllers/delete_account_controller.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/delete_account/confirmation.dart';

class DeleteAccountScreen extends HookConsumerWidget {
  const DeleteAccountScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.easyListen(
      deleteAccountControllerProvider,
      loadingText: context.L.deleteAccountLoading,
      whenData: (data) {
        if (data != null && data == 204) {
          ref.read(signOutProvider.notifier).signOut();
          context.go(DeleteAccountSuccessRoute().location);
        }
      },
    );

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size(0, 80),
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: AppBar(
            scrolledUnderElevation: 0,
            title: Text(
              context.L.deleteAccountScreenTitle,
              style: TextStyles.subheading1,
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(0.5),
              child: Container(
                color: AppColors.stroke,
                height: 0.5,
              ),
            ),
            centerTitle: true,
            leading: IconButton(
              icon: Assets.svgs.backArrow.svg(),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.white,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(28, 136, 28, 0),
        child: Column(
          children: [
            ConfirmationWidget(
              svgBackgroundColor: AppColors.error,
              svgBorderColor: AppColors.deleteIconBorder,
              svgPath: Assets.svgs.deleteIcon.path,
              title: context.L.deleteAccountConfirmationTitle,
              subtitle: context.L.deleteAccountConfirmationSubtitle,
            ),
            Spacer(),
            Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: SafeArea(
                child: SizedBox(
                  width: double.infinity,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: PrimaryButton(
                          borderColor: AppColors.deleteAccountBorder,
                          backgroundColor: AppColors.error,
                          onPressed: () {
                            ref
                                .watch(deleteAccountControllerProvider.notifier)
                                .deleteUser();
                          },
                          child: Text(context.L.deleteAccountButtonTitle),
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: SecondaryButton(
                          onPressed: () {
                            context.pop();
                          },
                          child:
                              Text(context.L.confirmationDialogDeclineButton),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
