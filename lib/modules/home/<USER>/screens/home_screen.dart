import 'package:flutter/material.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/achievement_card.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/badges_card.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/current_plan_card.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/neuro_city_card.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/restore_streak_card.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/todays_goal_progress_card.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverFillRemaining(
          hasScrollBody: false,
          child: Container(
            decoration: BoxDecoration(color: Colors.white),
            padding: const EdgeInsets.all(20),
            child: <PERSON><PERSON>n(
              children: [
                AchievementC<PERSON>(),
                const SizedBox(height: 20),
                TodaysGoalProgressCard(),
                RestoreStreakCard(),
                const SizedBox(height: 20),
                NeuroCityCard(),
                const SizedBox(height: 20),
                BadgesCard(),
                const SizedBox(height: 20),
                CurrentPlanCard(),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
