import 'package:formz/formz.dart';
import 'package:neuroworld/core/data/models/form_model.dart';
import 'package:neuroworld/core/data/validators/name_validator.dart';
import 'package:neuroworld/modules/auth/data/models/request/update_profile_request.dart';
import 'package:neuroworld/modules/home/<USER>/models/gender.dart';

class EditProfileForm extends FormModel with FormzMixin {
  EditProfileForm({
    required super.key,
    super.status,
    this.name = const NameInput.pure(),
    this.birthDate,
    this.gender,
    this.error,
  });

  @override
  List<FormzInput> get inputs => [name];

  final NameInput name;
  final DateTime? birthDate;
  final Gender? gender;
  final Object? error;

  @override
  EditProfileForm copyWith({
    NameInput? name,
    DateTime? birthDate,
    Gender? gender,
    FormzSubmissionStatus? status,
    Object? error,
  }) {
    return EditProfileForm(
      key: key,
      name: name ?? this.name,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      status: status ?? super.status,
      error: error ?? this.error,
    );
  }

  UpdateProfileRequest toRequest() => UpdateProfileRequest(
        fullName: name.value,
        birthDate: birthDate,
        gender: gender,
      );
}
