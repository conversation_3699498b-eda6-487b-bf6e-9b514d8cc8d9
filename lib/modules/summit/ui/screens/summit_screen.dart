import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';

class SummitScreen extends StatelessWidget {
  const SummitScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverFillRemaining(
          hasScrollBody: false,
          child: Container(
            decoration: BoxDecoration(color: Colors.white),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.images.comingSoon.image(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
