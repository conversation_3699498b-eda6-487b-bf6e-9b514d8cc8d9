import 'package:neuroworld/modules/notification/data/services/notification_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'notification_controller.g.dart';

@riverpod
class NotificationController extends _$NotificationController {
  DateTime? markReadTime;

  @override
  Set<String> build() {
    markReadTime = ref.read(notificationServiceProvider).getMarkReadTime();
    return {};
  }

  Future<void> markAsRead(String id) async {
    state = {...state, id};
    await ref.read(notificationServiceProvider).markAsRead(id);
  }

  Future<void> markAllAsRead() async {
    markReadTime = DateTime.now();
    state = {};
    await ref.read(notificationServiceProvider).markAllAsRead();
  }

  Future<Set<String>> getReadNotificationIds() async {
    return ref.read(notificationServiceProvider).getReadNotificationIds();
  }

  DateTime? getMarkReadTime() {
    return markReadTime;
  }
}
