import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/rounded_icon.dart';
import 'package:neuroworld/modules/notification/data/services/messaging_service.dart';

class NotificationPermissionDialog extends ConsumerWidget {
  const NotificationPermissionDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.surfaceSecondary,
        borderRadius: BorderRadius.circular(24),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          RoundedIcon(
            size: 48,
            borderRadius: 100,
            child: Assets.svgs.bell.svg(),
          ),
          const SizedBox(height: 12),
          Text(
            context.L.notificationPermissionDialogTitle,
            style: TextStyles.heading4,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            context.L.notificationPermissionDialogDescription,
            style: TextStyles.body1.copyWith(fontSize: 18),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: PrimaryButton(
              onPressed: () async {
                await ref.read(requestNotificationPermissionsProvider.future);
                // ignore: use_build_context_synchronously
                NavService.popDialog(context);
              },
              child: Text(context.L.notificationPermissionDialogAcept),
            ),
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: () => NavService.popDialog(context),
            child: Text(
              context.L.notificationPermissionDialogLater,
              style: TextStyles.buttonMedium.copyWith(
                color: AppColors.secondaryBlue,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
