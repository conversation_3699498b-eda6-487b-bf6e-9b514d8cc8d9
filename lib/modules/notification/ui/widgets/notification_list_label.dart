import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class NotificationListLabel extends StatelessWidget {
  const NotificationListLabel({super.key, required this.label});

  final String label;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 8),
      child: Text(
        label,
        style: TextStyles.subtitle3.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
    );
  }
}
