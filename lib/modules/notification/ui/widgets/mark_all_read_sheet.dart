import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/rounded_icon.dart';
import 'package:neuroworld/modules/notification/ui/controllers/notification_controller.dart';

class MarkAllReadSheet extends ConsumerWidget {
  const MarkAllReadSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView(
      shrinkWrap: true,
      children: [
        const SizedBox(height: 16),
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: RoundedIcon(
            size: 33,
            child: Assets.svgs.notification.markAllRead.svg(),
          ),
          title: Text(
            context.L.notificationMarkRead,
            style: TextStyles.body1,
          ),
          onTap: () {
            Navigator.pop(context);
            ref.watch(notificationControllerProvider.notifier).markAllAsRead();
          },
        ),
        SizedBox(height: 12),
      ],
    );
  }
}
