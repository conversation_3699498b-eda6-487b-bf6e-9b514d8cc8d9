import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/app_constants.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/dialogs.dart';
import 'package:neuroworld/core/ui/widgets/drawers.dart';
import 'package:neuroworld/modules/notification/data/services/notification_service.dart';
import 'package:neuroworld/modules/notification/ui/controllers/notification_controller.dart';
import 'package:neuroworld/modules/notification/ui/controllers/notification_permission_controller.dart';
import 'package:neuroworld/modules/notification/ui/widgets/mark_all_read_sheet.dart';
import 'package:neuroworld/modules/notification/ui/widgets/notification_card.dart';
import 'package:neuroworld/modules/notification/ui/widgets/notification_list_label.dart';
import 'package:neuroworld/modules/notification/ui/widgets/notification_loading_skeleton.dart';
import 'package:neuroworld/modules/notification/ui/widgets/notification_permission_dialog.dart';
import 'package:permission_handler/permission_handler.dart';

class NotificationScreen extends HookConsumerWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.easyListen(
      notificationPermissionControllerProvider,
      handleLoading: false,
      whenData: (status) {
        // TODO: settings redirect if permanently denied
        if (status == PermissionStatus.denied) {
          Dialogs.showCustomDialog(
            context,
            content: const NotificationPermissionDialog(),
          );
        }
      },
    );

    final now = DateTime.now();
    final notificationController =
        ref.watch(notificationControllerProvider.notifier);

    return PopScope(
      canPop: context.canPop(),
      onPopInvokedWithResult: (canPop, _) {
        // this will prevent popping when notification screen is only screen on stack
        // such as after clicking on push notification
        if (!canPop) HomeRoute().replace(context);
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          toolbarHeight: 65,
          forceMaterialTransparency: true,
          backgroundColor: Colors.white,
          centerTitle: true,
          title: Text(
            context.L.notificationTitle,
            style: TextStyles.subheading1,
          ),
          leading: IconButton(
            onPressed: () {
              if (context.canPop()) {
                context.pop();
              } else {
                HomeRoute().replace(context);
              }
            },
            icon: Assets.svgs.backArrow.svg(),
          ),
          actions: [
            IconButton(
              icon: Icon(Icons.more_horiz_rounded),
              onPressed: () {
                Drawers.showCustomBottomSheet(
                  context,
                  child: MarkAllReadSheet(),
                );
              },
            ),
            const SizedBox(width: 8),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(0.5),
            child: Container(
              color: AppColors.stroke,
              height: 0.5,
            ),
          ),
        ),
        body: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(child: SizedBox(height: 8)),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final page = index ~/ AppConstants.notificationPageSize + 1;
                  final indexInPage = index % AppConstants.notificationPageSize;

                  final notificationResponse =
                      ref.watch(fetchNotificationsProvider(page));

                  return notificationResponse.when(
                    loading: () => NotificationLoadingSkeleton(),
                    error: (err, stackTrace) => const SizedBox.shrink(),
                    data: (response) {
                      if (indexInPage >= response.results.length) {
                        return null;
                      }

                      final notification = response.results[indexInPage];
                      final diff =
                          now.difference(notification.timestamp).inDays;
                      Widget? earlierCard;

                      if ((index == 0 && diff >= 1) ||
                          (index > 0 &&
                              diff >= 1 &&
                              now
                                      .difference(response
                                          .results[indexInPage - 1].timestamp)
                                      .inDays <
                                  1)) {
                        earlierCard = NotificationListLabel(
                            label: context.L.notificationEarlierLabel);
                      }

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (index == 0 && diff < 1)
                            NotificationListLabel(
                              label: context.L.notificationNewLabel,
                            ),
                          earlierCard ?? const SizedBox.shrink(),
                          NotificationCard(
                            notification: notification,
                            isUnread:
                                (notificationController.markReadTime) != null
                                    ? (notificationController.markReadTime)!
                                        .difference(notification.timestamp)
                                        .isNegative
                                    : !ref
                                        .watch(notificationControllerProvider)
                                        .contains(notification.id),
                            onTap: (id) async {
                              notificationController.markAsRead(id);
                              NavService.pop(context);
                            },
                          ),
                        ],
                      );
                    },
                  );
                },
              ),
            ),
            SliverToBoxAdapter(child: SizedBox(height: 16)),
          ],
        ),
      ),
    );
  }
}
