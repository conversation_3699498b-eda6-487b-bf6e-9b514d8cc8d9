import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/shared_prefs_keys.dart';
import 'package:neuroworld/core/data/sources/shared_preferences.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'notification_shared_prefs.g.dart';

@Riverpod(keepAlive: true)
NotificationSharedPrefs notificationSharedPrefs(Ref ref) {
  return NotificationSharedPrefs(ref);
}

class NotificationSharedPrefs {
  NotificationSharedPrefs(this.ref);

  final Ref ref;
  SharedPreferencesWithCache get sharedPreferences =>
      ref.read(sharedPreferencesProvider).requireValue;

  Future<void> markAsRead(String id) async {
    final existing =
        sharedPreferences.getStringList(SharedPrefsKeys.readNotifications) ??
            [];
    if (!existing.contains(id)) {
      final updated = [...existing, id];
      await sharedPreferences.setStringList(
          SharedPrefsKeys.readNotifications, updated);
    }
  }

  Set<String> getReadNotificationIds() {
    return sharedPreferences
            .getStringList(SharedPrefsKeys.readNotifications)
            ?.toSet() ??
        {};
  }

  Future<void> clearReadNotifications() async {
    await sharedPreferences.remove(SharedPrefsKeys.readNotifications);
  }

  Future<void> markAllAsRead() async {
    await sharedPreferences.setString(SharedPrefsKeys.markAllReadNotifications,
        DateTime.now().toIso8601String());
  }

  DateTime? getMarkedReadTime() {
    final timestamp =
        sharedPreferences.getString(SharedPrefsKeys.markAllReadNotifications);
    return timestamp != null ? DateTime.parse(timestamp) : null;
  }
}
