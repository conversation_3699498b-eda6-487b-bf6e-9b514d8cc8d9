import 'package:dio/dio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/data/sources/network/api/api.dart';
import 'package:neuroworld/core/data/sources/network/api/api_endpoints.dart';
import 'package:neuroworld/modules/notification/data/models/request/notification_request.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'notification_api.g.dart';

@riverpod
NotificationApi notificationApi(Ref ref) =>
    NotificationApi(api: ref.watch(apiProvider));

class NotificationApi {
  NotificationApi({required this.api});

  final Api api;

  Future<Response<dynamic>> getNotifications(NotificationRequest request) =>
      api.get(
        path: ApiEndpoints.notification,
        queryParameters: request.toJson(),
      );
}
