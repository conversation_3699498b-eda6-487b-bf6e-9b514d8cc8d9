import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_request.freezed.dart';
part 'notification_request.g.dart';

@freezed
abstract class NotificationRequest with _$NotificationRequest {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory NotificationRequest({
    required int page,
    required int limit,
  }) = _NotificationRequest;

  factory NotificationRequest.fromJson(Map<String, dynamic> json) =>
      _$NotificationRequestFromJson(json);
}
