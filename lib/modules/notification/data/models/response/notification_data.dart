import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/notification/data/models/response/notification_details.dart';

part 'notification_data.freezed.dart';
part 'notification_data.g.dart';

@freezed
sealed class NotificationData with _$NotificationData {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory NotificationData({
    required String id,
    required String title,
    required String body,
    required DateTime timestamp,
    required NotificationDetails notification,
  }) = _NotificationData;

  factory NotificationData.fromJson(Map<String, dynamic> json) =>
      _$NotificationDataFromJson(json);
}
