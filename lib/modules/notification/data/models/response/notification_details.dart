import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_details.freezed.dart';
part 'notification_details.g.dart';

@freezed
sealed class NotificationDetails with _$NotificationDetails {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory NotificationDetails({
    required String type,
    required String subType,
    required String icon,
  }) = _NotificationDetails;

  factory NotificationDetails.fromJson(Map<String, dynamic> json) =>
      _$NotificationDetailsFromJson(json);
}
