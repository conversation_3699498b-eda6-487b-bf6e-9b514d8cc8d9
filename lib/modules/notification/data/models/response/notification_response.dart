import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/notification/data/models/response/notification_data.dart';

part 'notification_response.freezed.dart';
part 'notification_response.g.dart';

@freezed
sealed class NotificationResponse with _$NotificationResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory NotificationResponse({
    required int count,
    required String? next,
    required String? previous,
    required List<NotificationData> results,
  }) = _NotificationResponse;

  factory NotificationResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationResponseFromJson(json);
}
