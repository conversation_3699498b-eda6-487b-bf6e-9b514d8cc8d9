import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../providers/fcm_providers.dart';
import '../services/messaging_service.dart';

part 'notification.freezed.dart';
part 'notification.g.dart';

@freezed
abstract class NotificationPayload with _$NotificationPayload {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory NotificationPayload({
    required String? routeLocation,
    required Map<String, dynamic>? data,
  }) = _NotificationPayload;

  factory NotificationPayload.fromJson(Map<String, dynamic> json) =>
      _$NotificationPayloadFromJson(json);
}

String _lastMessageId = "";

@riverpod
class TappedNotification extends _$TappedNotification {
  @override
  NotificationPayload? build() {
    // handles onTap for terminated app state FCM messages
    ref.listen(getInitialMessageProvider, (previous, next) {
      next.whenData((message) {
        if (message != null &&
            message.data.isNotEmpty &&
            message.messageId != _lastMessageId) {
          _lastMessageId = message.messageId!;
          state = NotificationPayload.fromJson(message.data);
        }
      });
    });

    // handles onTap for background app state FCM messages
    ref.listen(onMessageOpenedAppProvider, (previous, next) {
      next.whenData(
        (message) {
          if (message.data.isNotEmpty && message.messageId != _lastMessageId) {
            _lastMessageId = message.messageId!;
            state = NotificationPayload.fromJson(message.data);
          }
        },
      );
    });

    // handles onTap or onNotificationAction (note we havent configured any ios actions)
    // that should show the application/ui
    ref.listen(notificationResponseEventProvider, (previous, next) {
      if (next != null) {
        final payload = next.payload;
        if (payload == null || payload.isEmpty) return;
        final decodedPayload = jsonDecode(payload) as Map<String, dynamic>;
        state = NotificationPayload.fromJson(decodedPayload);
      }
    });
    return null;
  }
}
