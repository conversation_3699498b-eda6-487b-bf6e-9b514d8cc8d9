import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/app_constants.dart';
import 'package:neuroworld/modules/notification/data/models/request/notification_request.dart';
import 'package:neuroworld/modules/notification/data/models/response/notification_response.dart';
import 'package:neuroworld/modules/notification/data/sources/notification_api.dart';
import 'package:neuroworld/modules/notification/data/sources/notification_shared_prefs.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'notification_service.g.dart';

@riverpod
NotificationService notificationService(Ref ref) => NotificationService(
      notificationApi: ref.watch(notificationApiProvider),
      notificationSharedPrefs: ref.watch(notificationSharedPrefsProvider),
    );

class NotificationService {
  NotificationService(
      {required this.notificationApi, required this.notificationSharedPrefs});

  final NotificationApi notificationApi;
  final NotificationSharedPrefs notificationSharedPrefs;

  Future<NotificationResponse> getNotifications(int page, int limit) async {
    final response = await notificationApi.getNotifications(
      NotificationRequest(page: page, limit: limit),
    );
    return NotificationResponse.fromJson(response.data);
  }

  Future<void> markAsRead(String id) async {
    await notificationSharedPrefs.markAsRead(id);
  }

  Future<void> markAllAsRead() async {
    await notificationSharedPrefs.markAllAsRead();
  }

  Future<Set<String>> getReadNotificationIds() async {
    return notificationSharedPrefs.getReadNotificationIds();
  }

  DateTime? getMarkReadTime() {
    return notificationSharedPrefs.getMarkedReadTime();
  }
}

@riverpod
Future<NotificationResponse> fetchNotifications(Ref ref, int page) async => ref
    .watch(notificationServiceProvider)
    .getNotifications(page, AppConstants.notificationPageSize);
