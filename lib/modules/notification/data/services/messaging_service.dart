import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/utils/provider_utils.dart';
import 'package:neuroworld/modules/notification/data/models/notification.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'messaging_service.g.dart';

const AndroidNotificationChannel _channel = AndroidNotificationChannel(
  'high_importance_channel',
  '${const String.fromEnvironment('APP_NAME')} High Importance Notifications',
  description: 'This channel is used for important notifications.',
  importance: Importance.max,
);

final _notificationDetails = NotificationDetails(
  android: AndroidNotificationDetails(
    _channel.id,
    _channel.name,
    channelDescription: _channel.description,
    importance: _channel.importance,
    priority: Priority.high,
    color: AppColors.boldOrange,
    enableLights: true,
  ),
  iOS: const DarwinNotificationDetails(
    presentAlert: true,
    presentSound: true,
    presentBadge: true,
  ),
);

@riverpod
Future<void> setupFlutterNotifications(Ref ref) async {
  final messaingService = ref.watch(messagingServiceProvider);
  await messaingService._setupFlutterNotifications();
}

@riverpod
Future<AuthorizationStatus> requestNotificationPermissions(
  Ref ref,
) async {
  final messaingService = ref.watch(messagingServiceProvider);
  return messaingService._requestPermissions();
}

@Riverpod(keepAlive: true)
MessagingService messagingService(Ref ref) => MessagingService(
      ref,
      FlutterLocalNotificationsPlugin(),
      FirebaseMessaging.instance,
    );

class MessagingService {
  const MessagingService(
    this.ref,
    this._flutterLocalNotifications,
    this._fcm,
  );

  final Ref ref;
  final FlutterLocalNotificationsPlugin _flutterLocalNotifications;
  final FirebaseMessaging _fcm;

  static const _androidIcon = '@mipmap/launcher_icon';

  Future<void> _setupFlutterNotifications() async {
    if (kIsWeb) return;
    await _initializeLocalNotifications();
    if (Platform.isAndroid) {
      await _setupAndroidHeadsUp();
    } else {
      await _setupIOSHeadsUp();
    }
  }

  Future<void> _initializeLocalNotifications() async {
    // Android setting
    const androidSettings = AndroidInitializationSettings(_androidIcon);
    // iOS setting
    const darwinSettings = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    const settings =
        InitializationSettings(android: androidSettings, iOS: darwinSettings);

    await _flutterLocalNotifications.initialize(
      settings,
      onDidReceiveNotificationResponse:
          (NotificationResponse notificationResponse) {
        ref.read(notificationResponseEventProvider.notifier).update(
              (_) => notificationResponse,
            );
      },
    );
  }

  /// Create an Android Notification Channel.
  /// We use this channel in the `AndroidManifest.xml` file to override the
  /// default FCM channel to enable heads up notifications.
  Future<void> _setupAndroidHeadsUp() async {
    return await _flutterLocalNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(_channel);
  }

  /// Update the iOS foreground notification presentation options to allow heads up notifications.
  /// Avoid this if you're using flutterLocalNotification to handle foreground notifications
  /// instead of FCM, otherwise, you'll receive duplicate heads-up notifications.
  Future<void> _setupIOSHeadsUp() async {
    /* await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true,
      sound: true,
      badge: true,
    ); */
  }

  Future<AuthorizationStatus> _requestPermissions() async {
    /* //Add when adding integration tests.
    if (isIntegrationTest) {
      // Firebase Messaging is not available in integration tests.
      log('Skipping to request Firebase Messaging access because integration test is running.');
      return AuthorizationStatus.denied;
    } */

    // On iOS, macOS & web, before FCM payloads can be received on your device
    // you must first ask the user's permission.
    // Older Androids are not required to request permission.
    final settings = await _fcm.requestPermission(
      announcement: true,
      criticalAlert: true,
    );
    return settings.authorizationStatus;
  }

  Future<void> showRemoteNotification(RemoteMessage message) async {
    final notification = message.notification;
    final messageData = message.data;

    if (notification != null && !kIsWeb) {
      await _flutterLocalNotifications.show(
        notification.hashCode,
        notification.title,
        notification.body,
        _notificationDetails,
        payload: jsonEncode(messageData),
      );
    }
  }

  Future<void> showLocalNotification({
    required String title,
    required String body,
    NotificationPayload? payload,
  }) async {
    return _flutterLocalNotifications.show(
      0,
      title,
      body,
      _notificationDetails,
      payload: payload != null ? jsonEncode(payload.toJson()) : null,
    );
  }

  Future<RemoteMessage?> getInitialMessage() => _fcm.getInitialMessage();

  Future<void> subscribeToTopic(String topic) => _fcm.subscribeToTopic(topic);

  Future<void> unsubscribeFromTopic(String topic) =>
      _fcm.unsubscribeFromTopic(topic);
}

@Riverpod(keepAlive: true)
class NotificationResponseEvent extends _$NotificationResponseEvent
    with NotifierUpdate {
  @override
  NotificationResponse? build() => null;
}
