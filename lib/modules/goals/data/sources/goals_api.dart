import 'package:dio/dio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/data/sources/network/api/api.dart';
import 'package:neuroworld/core/data/sources/network/api/api_endpoints.dart';
import 'package:neuroworld/modules/goals/data/models/request/check_in_request.dart';
import 'package:neuroworld/modules/goals/data/models/request/edit_goal_request.dart';
import 'package:neuroworld/modules/goals/data/models/request/get_goal_request.dart';
import 'package:neuroworld/modules/goals/data/models/request/get_tracking_request.dart';
import 'package:neuroworld/modules/goals/data/models/request/repair_streak_request.dart';
import 'package:neuroworld/modules/goals/data/models/request/set_goal_request.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'goals_api.g.dart';

@Riverpod(keepAlive: true)
GoalsApi goalsApi(Ref ref) => GoalsApi(api: ref.watch(apiProvider));

class GoalsApi {
  GoalsApi({required this.api});

  final Api api;

  Future<Response<dynamic>> getTracking(GetTrackingRequest request) => api.get(
        path: ApiEndpoints.tracking,
        queryParameters: request.toJson(),
      );

  Future<Response<dynamic>> checkIn(CheckInRequest request) => api.post(
        path: ApiEndpoints.tracking,
        data: request.toJson(),
      );

  Future<Response<dynamic>> repairStreak(RepairStreakRequest request) =>
      api.post(
        path: ApiEndpoints.userGoalRestore,
        data: request.toJson(),
      );

  Future<Response<dynamic>> editGoal(EditGoalRequest request) => api.patch(
        path: ApiEndpoints.userGoalByid(request.goalId),
        data: request.toJson(),
      );

  Future<Response<dynamic>> getGoal(GetGoalRequest? request) => api.get(
        path: ApiEndpoints.goals,
        queryParameters: request?.toJson(),
      );

  Future<Response<dynamic>> setGoal(SetGoalRequest request) => api.post(
        path: ApiEndpoints.userGoal,
        data: request.toJson(),
      );
}
