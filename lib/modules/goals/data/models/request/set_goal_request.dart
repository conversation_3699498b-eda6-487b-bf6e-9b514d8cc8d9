import 'package:freezed_annotation/freezed_annotation.dart';

part 'set_goal_request.freezed.dart';
part 'set_goal_request.g.dart';

@freezed
abstract class SetGoalRequest with _$SetGoalRequest {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory SetGoalRequest({
    required String goalVersionId,
    required int target,
  }) = _SetGoalRequest;

  factory SetGoalRequest.fromJson(Map<String, dynamic> json) =>
      _$SetGoalRequestFromJson(json);
}
