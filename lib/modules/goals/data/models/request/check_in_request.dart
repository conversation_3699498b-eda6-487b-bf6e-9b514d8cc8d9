import 'package:freezed_annotation/freezed_annotation.dart';

part 'check_in_request.freezed.dart';
part 'check_in_request.g.dart';

@freezed
abstract class CheckInRequest with _$CheckInRequest {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory CheckInRequest({
    required String selectedGoalId,
    required String trackingDate,
    required bool isNewWeek,
    required bool shouldResetGoal,
  }) = _CheckInRequest;

  factory CheckInRequest.fromJson(Map<String, dynamic> json) =>
      _$CheckInRequestFromJson(json);
}
