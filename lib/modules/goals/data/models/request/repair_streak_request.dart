import 'package:freezed_annotation/freezed_annotation.dart';

part 'repair_streak_request.freezed.dart';
part 'repair_streak_request.g.dart';

@freezed
abstract class RepairStreakRequest with _$RepairStreakRequest {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory RepairStreakRequest({
    required String selectedGoalId,
    required String restoreDate,
  }) = _RepairStreakRequest;

  factory RepairStreakRequest.fromJson(Map<String, dynamic> json) =>
      _$RepairStreakRequestFromJson(json);
}
