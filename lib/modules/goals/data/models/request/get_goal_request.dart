import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_goal_request.freezed.dart';
part 'get_goal_request.g.dart';

@freezed
abstract class GetGoalRequest with _$GetGoalRequest {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory GetGoalRequest({
    String? id,
    String? category,
    bool? all,
  }) = _GetGoalRequest;

  factory GetGoalRequest.fromJson(Map<String, dynamic> json) =>
      _$GetGoalRequestFromJson(json);
}
