import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_goal_request.freezed.dart';
part 'edit_goal_request.g.dart';

@freezed
abstract class EditGoalRequest with _$EditGoalRequest {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory EditGoalRequest({
    required String goalId,
    required int target,
  }) = _EditGoalRequest;

  factory EditGoalRequest.fromJson(Map<String, dynamic> json) =>
      _$EditGoalRequestFromJson(json);
}
