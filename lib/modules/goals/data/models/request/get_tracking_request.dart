import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_tracking_request.freezed.dart';
part 'get_tracking_request.g.dart';

@freezed
abstract class GetTrackingRequest with _$GetTrackingRequest {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory GetTrackingRequest({
    required String startDate,
    required String endDate,
  }) = _GetTrackingRequest;

  factory GetTrackingRequest.fromJson(Map<String, dynamic> json) =>
      _$GetTrackingRequestFromJson(json);
}
