import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

enum AwardType {
  // goals related
  dailyCheckin('daily_checkin'),
  extraCheckin('extra_checkin_bonus'),
  weeklyStreak('weekly_streak_bonus'),
  monthlyStreak('monthly_streak_bonus'),
  threeMonthsStreak('three_months_streak_bonus'),
  sixMonthsStreak('six_months_streak'),
  restoreStreakCost('restore_streak_cost'),

  dailyAppOpen('daily_app_open'),
  oneOffBigActions('one_off_big_actions'),
  assesmentCompletion('assesment_completion'),
  communityEngagementBonus('community_engagement_bonus');

  const AwardType(this.name);

  final String name;

  static AwardType bySnakeCaseName(String name) =>
      AwardType.values.firstWhere((award) => award.name == name);

  String getDisplayText(BuildContext context) {
    return switch (this) {
      AwardType.dailyCheckin => context.L.awardDaily,
      AwardType.extraCheckin => context.L.awardExtra,
      AwardType.weeklyStreak => context.L.awardWeekStreak,
      AwardType.monthlyStreak => context.L.awardMonthStreak,
      AwardType.threeMonthsStreak => context.L.award3MonthStreak,
      AwardType.sixMonthsStreak => context.L.award6MonthStreak,
      AwardType.restoreStreakCost => throw UnimplementedError(),
      AwardType.dailyAppOpen => throw UnimplementedError(),
      AwardType.oneOffBigActions => throw UnimplementedError(),
      AwardType.assesmentCompletion => throw UnimplementedError(),
      AwardType.communityEngagementBonus => throw UnimplementedError(),
    };
  }

  Widget get icon => SvgPicture.asset(
        _getStreakIconPath(),
      );

  String _getStreakIconPath() {
    return switch (this) {
      AwardType.dailyCheckin => Assets.svgs.goals.checkInCalendar.path,
      AwardType.extraCheckin => Assets.svgs.goals.checkInCalendar.path,
      AwardType.weeklyStreak => Assets.svgs.goals.streakFire.path,
      AwardType.monthlyStreak => Assets.svgs.goals.streakFire.path,
      AwardType.threeMonthsStreak => Assets.svgs.goals.streakFire.path,
      AwardType.sixMonthsStreak => Assets.svgs.goals.streakFire.path,
      AwardType.restoreStreakCost => throw UnimplementedError(),
      AwardType.dailyAppOpen => throw UnimplementedError(),
      AwardType.oneOffBigActions => throw UnimplementedError(),
      AwardType.assesmentCompletion => throw UnimplementedError(),
      AwardType.communityEngagementBonus => throw UnimplementedError(),
    };
  }
}
