import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';

part 'daily_tracking.freezed.dart';

@Freezed(makeCollectionsUnmodifiable: false)
abstract class DailyTracking with _$DailyTracking {
  const factory DailyTracking({
    required SelectedGoal selectedGoal,
    required DateTime? startDate,
    @Default(null) DateTime? endDate,
    @Default(0) int currentIndex,
    @Default([]) List<DateTime> trackedDates,
    @Default([]) List<bool> weeklyCheckInTracking,
    @Default(false) bool isNewWeek,
    @Default(false) bool shouldResetGoal,
    @Default(0) int gracePeriodDays,
  }) = _DailyTracking;
}
