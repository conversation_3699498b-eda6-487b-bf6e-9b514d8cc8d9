import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/core/constants/neuro.dart';
import 'package:neuroworld/core/data/converters/neuro_converter.dart';
import 'package:neuroworld/modules/goals/data/models/goal_version.dart';

part 'goal.freezed.dart';
part 'goal.g.dart';

@freezed
sealed class Goal with _$Goal {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory Goal({
    required String id,
    required String title,
    required String description,
    @NeuroConverter() required Neuro category,
    required int? maxLimit,
    required String verb,
    required String imageUrl,
    required List<GoalVersion>? goalVersions,
  }) = _Goal;

  factory Goal.fromJson(Map<String, dynamic> json) => _$GoalFrom<PERSON>son(json);
}
