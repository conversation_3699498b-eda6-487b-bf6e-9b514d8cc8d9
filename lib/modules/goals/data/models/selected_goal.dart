import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/core/data/converters/date_converter.dart';
import 'package:neuroworld/modules/goals/data/models/goal.dart';
import 'package:neuroworld/modules/goals/data/models/streak_info.dart';

part 'selected_goal.freezed.dart';
part 'selected_goal.g.dart';

@freezed
abstract class SelectedGoal with _$SelectedGoal {
  const SelectedGoal._();

  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory SelectedGoal({
    required String id,
    required DateTime createdOn,
    @DateConverter() required DateTime? startDate,
    @DateConverter() required DateTime? endDate,
    @DateConverter() required DateTime? restoreDate,
    @DateConverter() required DateTime? firstTrackedDate,
    @DateConverter() required DateTime? lastTrackingDate,
    required int target,
    required int currentWeek,
    required int currentCheckins,
    required int streak,
    required bool streakAwarded,
    required bool isActive,
    required int restoreWeekOffset,
    required String user,
    required String goalVersion,
    required String ruleText,
    required Goal goal,
  }) = _SelectedGoal;

  factory SelectedGoal.fromJson(Map<String, dynamic> json) =>
      _$SelectedGoalFromJson(json);

  StreakInfo getStreakInfo(DateTime dt) {
    // calculate number of dates from goal's start date to currentWeek + 1 week extra
    // (1 week extra to see if user has spent a whole week without marking a goal, then we must expire streak)
    final expiryThresholdDays = 7 * (currentWeek + 1) - 1;
    final daysSinceStart = dt.difference(startDate!).inDays;

    final isNewWeek = ((daysSinceStart + 1) / 7).ceil() != currentWeek;
    final isStreakBroken =
        isNewWeek && (!streakAwarded || daysSinceStart > expiryThresholdDays);
    final gracePeriodDays = !isNewWeek || !isStreakBroken
        ? -1
        : (currentWeek * 7 + 14) - daysSinceStart;

    return StreakInfo(
      isNewWeek: isNewWeek,
      isStreakBroken: isStreakBroken,
      gracePeriodDays: gracePeriodDays,
    );
  }
}
