import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/goals/data/models/goal.dart';

part 'get_goal_response.freezed.dart';
part 'get_goal_response.g.dart';

@freezed
abstract class GetGoalsResponse with _$GetGoalsResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory GetGoalsResponse({
    required List<Goal> goals,
  }) = _GetGoalsResponse;

  factory GetGoalsResponse.fromJson(Map<String, dynamic> json) =>
      _$GetGoalsResponseFromJson(json);
}
