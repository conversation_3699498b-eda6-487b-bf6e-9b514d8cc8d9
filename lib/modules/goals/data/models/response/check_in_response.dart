import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/auth/data/models/profile.dart';
import 'package:neuroworld/modules/badges/data/models/user_badge.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';

part 'check_in_response.freezed.dart';
part 'check_in_response.g.dart';

@freezed
abstract class CheckInResponse with _$CheckInResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory CheckInResponse({
    required Map<String, int> awardedPoints,
    required Profile profile,
    required SelectedGoal selectedGoal,
    required List<UserBadge>? badgesAwarded,
  }) = _CheckInResponse;

  factory CheckInResponse.fromJson(Map<String, dynamic> json) =>
      _$CheckInResponseFromJson(json);
}
