import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/auth/data/models/user.dart';

part 'set_goal_response.freezed.dart';
part 'set_goal_response.g.dart';

@freezed
abstract class SetGoalResponse with _$SetGoalResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory SetGoalResponse({
    required User user,
  }) = _SetGoalResponse;

  factory SetGoalResponse.fromJson(Map<String, dynamic> json) =>
      _$SetGoalResponseFromJson(json);
}
