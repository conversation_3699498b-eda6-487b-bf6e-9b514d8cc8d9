import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/goals/data/models/goal_tracking.dart';

part 'get_tracking_response.freezed.dart';
part 'get_tracking_response.g.dart';

@freezed
sealed class GetTrackingResponse with _$GetTrackingResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory GetTrackingResponse({
    required String startDate,
    required String endDate,
    required List<GoalTracking> goalTracking,
  }) = _GetTrackingResponse;

  factory GetTrackingResponse.fromJson(Map<String, dynamic> json) =>
      _$GetTrackingResponseFromJson(json);
}
