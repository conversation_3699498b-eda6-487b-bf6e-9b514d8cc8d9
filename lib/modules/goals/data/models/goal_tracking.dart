import 'package:freezed_annotation/freezed_annotation.dart';

part 'goal_tracking.freezed.dart';
part 'goal_tracking.g.dart';

@freezed
sealed class GoalTracking with _$GoalTracking {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory GoalTracking({
    required String selectedGoal,
    required DateTime date,
    required String goalVersion,
  }) = _GoalTracking;

  factory GoalTracking.fromJson(Map<String, dynamic> json) =>
      _$GoalTrackingFromJson(json);
}
