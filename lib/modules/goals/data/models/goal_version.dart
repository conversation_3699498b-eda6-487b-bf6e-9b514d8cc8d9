import 'package:freezed_annotation/freezed_annotation.dart';

part 'goal_version.freezed.dart';
part 'goal_version.g.dart';

@freezed
sealed class GoalVersion with _$GoalVersion {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory GoalVersion({
    required String id,
    required String description,
    required String level,
    required String goal,
  }) = _GoalVersion;

  factory GoalVersion.fromJson(Map<String, dynamic> json) =>
      _$GoalVersionFromJson(json);
}
