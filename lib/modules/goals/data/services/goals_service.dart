import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/app_constants.dart';
import 'package:neuroworld/core/data/converters/date_converter.dart';
import 'package:neuroworld/modules/auth/data/models/response/auth.dart';
import 'package:neuroworld/modules/auth/data/sources/auth_shared_prefs.dart';
import 'package:neuroworld/modules/goals/data/models/check_in.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:neuroworld/modules/goals/data/models/goal_tracking.dart';
import 'package:neuroworld/modules/goals/data/models/repair_streak.dart';
import 'package:neuroworld/modules/goals/data/models/request/check_in_request.dart';
import 'package:neuroworld/modules/goals/data/models/request/edit_goal_request.dart';
import 'package:neuroworld/modules/goals/data/models/request/get_goal_request.dart';
import 'package:neuroworld/modules/goals/data/models/request/get_tracking_request.dart';
import 'package:neuroworld/modules/goals/data/models/request/repair_streak_request.dart';
import 'package:neuroworld/modules/goals/data/models/request/set_goal_request.dart';
import 'package:neuroworld/modules/goals/data/models/response/check_in_response.dart';
import 'package:neuroworld/modules/goals/data/models/response/get_goal_response.dart';
import 'package:neuroworld/modules/goals/data/models/response/get_tracking_response.dart';
import 'package:neuroworld/modules/goals/data/models/response/set_goal_response.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';
import 'package:neuroworld/modules/goals/data/sources/goals_api.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'goals_service.g.dart';

@Riverpod(keepAlive: true)
GoalsService goalsService(Ref ref) => GoalsService(
      goalsApi: ref.watch(goalsApiProvider),
      authSharedPrefs: ref.watch(authSharedPrefsProvider),
    );

class GoalsService {
  GoalsService({required this.goalsApi, required this.authSharedPrefs});

  final GoalsApi goalsApi;
  final AuthSharedPrefs authSharedPrefs;

  Future<List<GoalTracking>> getTracking(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final res = await goalsApi.getTracking(
      GetTrackingRequest(
        startDate: requestDateFormat.format(startDate),
        endDate: requestDateFormat.format(endDate),
      ),
    );
    return GetTrackingResponse.fromJson(res.data).goalTracking;
  }

  Future<CheckIn> checkIn(DailyTracking dailyTracking) async {
    final res = await goalsApi.checkIn(
      CheckInRequest(
        selectedGoalId: dailyTracking.selectedGoal.id,
        trackingDate: requestDateFormat.format(DateTime.now()),
        isNewWeek: dailyTracking.isNewWeek,
        shouldResetGoal: dailyTracking.shouldResetGoal,
      ),
    );
    final trackingResponse = CheckInResponse.fromJson(res.data);
    final newAuth = await _handleCheckInResponse(trackingResponse);

    return CheckIn(
      auth: newAuth,
      awardedPoints: trackingResponse.awardedPoints,
      selectedGoal: trackingResponse.selectedGoal,
      badgesAwarded: trackingResponse.badgesAwarded,
    );
  }

  Future<RepairStreak> repairStreak(String selectedGoalId) async {
    final res = await goalsApi.repairStreak(
      RepairStreakRequest(
        selectedGoalId: selectedGoalId,
        restoreDate: requestDateFormat.format(DateTime.now()),
      ),
    );
    final newGoal = SelectedGoal.fromJson(res.data);
    final newAuth = await _handleRepairStreak(newGoal);
    return RepairStreak(auth: newAuth, goal: newGoal.goal);
  }

  Future<GetGoalsResponse> getGoal(GetGoalRequest? request) async {
    final res = await goalsApi.getGoal(request);
    final goalResponse = GetGoalsResponse.fromJson(res.data);
    return goalResponse;
  }

  Future<SetGoalResponse> setGoal(SetGoalRequest request) async {
    final res = await goalsApi.setGoal(request);
    final setGoalResponse = SetGoalResponse.fromJson(res.data);
    final authData = authSharedPrefs.getAuthData();
    final updatedAuthData = authData.copyWith(user: setGoalResponse.user);
    await authSharedPrefs.setAuthData(updatedAuthData);
    return setGoalResponse;
  }

  Future<CheckIn> editGoal(String goalId, int newTarget) async {
    final res = await goalsApi.editGoal(
      EditGoalRequest(goalId: goalId, target: newTarget),
    );
    final editGoalResponse = CheckInResponse.fromJson(res.data);
    final newAuth = await _handleCheckInResponse(editGoalResponse);

    return CheckIn(
      auth: newAuth,
      awardedPoints: editGoalResponse.awardedPoints,
      selectedGoal: editGoalResponse.selectedGoal,
      badgesAwarded: editGoalResponse.badgesAwarded,
    );
  }

  Future<Auth> _handleCheckInResponse(CheckInResponse response) async {
    final auth = authSharedPrefs.getAuthData();
    final newAuth = auth.copyWith.user(
      profile: response.profile,
      selectedGoals: auth.user.selectedGoals.updateById(
        [response.selectedGoal],
        (item) => item.id,
      ),
    );
    await authSharedPrefs.setAuthData(newAuth);
    return newAuth;
  }

  Future<Auth> _handleRepairStreak(SelectedGoal goal) async {
    final auth = authSharedPrefs.getAuthData();
    final newGoals = auth.user.selectedGoals.unlock;

    final indexToReplace = newGoals.indexWhere((g) => g.id == goal.id);
    newGoals[indexToReplace] = goal;

    final newAuth = auth.copyWith.user(
      selectedGoals: newGoals.lock,
      profile: auth.user.profile.copyWith(
        points: auth.user.profile.points - AppConstants.repairStreakCost,
      ),
    );
    await authSharedPrefs.setAuthData(newAuth);
    return newAuth;
  }
}

@riverpod
Future<List<GoalTracking>> getMonthlyTracking(Ref ref, int month, int year) {
  final startDate = DateTime(year, month, 1).subtract(Duration(days: 6));
  final endDate = DateTime(year, month + 1, 0).add(Duration(days: 6));
  return ref.watch(goalsServiceProvider).getTracking(startDate, endDate);
}

@riverpod
Future<List<GoalTracking>> getTodaysTracking(Ref ref) {
  final today = DateTime.now();
  return ref.watch(goalsServiceProvider).getTracking(today, today);
}
