import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

class IslandCategory {
  final String key;
  final String title;
  final String subtitle;
  final String iconPath;
  final Color color;

  IslandCategory({
    required this.key,
    required this.title,
    required this.subtitle,
    required this.iconPath,
    required this.color,
  });
}

List<IslandCategory> getIslandCategories(BuildContext context) {
  return [
    IslandCategory(
      key: 'nutrition',
      title: context.L.nutritionTitle,
      subtitle: context.L.nutritionSubtitle,
      iconPath: Assets.svgs.nuriList.path,
      color: AppColors.nutritionBackground,
    ),
    IslandCategory(
      key: 'exercise',
      title: context.L.exerciseTitle,
      subtitle: context.L.exerciseSubtitle,
      iconPath: Assets.svgs.sparkList.path,
      color: AppColors.exerciseBackground,
    ),
    IslandCategory(
      key: 'unwind',
      title: context.L.unwindTitle,
      subtitle: context.L.unwindSubtitle,
      iconPath: Assets.svgs.floList.path,
      color: AppColors.unwindBackground,
    ),
    IslandCategory(
      key: 'restore',
      title: context.L.restoreTitle,
      subtitle: context.L.restoreSubtitle,
      iconPath: Assets.svgs.lunaList.path,
      color: AppColors.restoreBackground,
    ),
    IslandCategory(
      key: 'optimize',
      title: context.L.optimizeTitle,
      subtitle: context.L.optimizeSubtitle,
      iconPath: Assets.svgs.sophiList.path,
      color: AppColors.optimizeBackground,
    ),
  ];
}
