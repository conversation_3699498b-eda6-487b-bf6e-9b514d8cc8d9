import 'package:flutter/material.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

String getGoalText(
  BuildContext context, {
  required int weekday,
  required int checkins,
  required int target,
  required bool isUnstarted,
}) {
  if (isUnstarted) {
    return context.L.goalCardUnstarted;
  }
  if (checkins < target) {
    // TODO-goal: case where we check if user has any missed days
    // and return "A little push and you're there!"

    // user is on last day of week and can save goal
    if (weekday == 6 && checkins == target - 1) {
      return context.L.goalCardFinalChance;
    }
    if (checkins == 0) {
      return context.L.goalCardNoCheckin;
    }
    if (checkins == 1) {
      return context.L.goalCardOneCheckin;
    }
    if (checkins == target - 1) {
      return context.L.goalCardOneAway;
    }
    if (checkins == target - 2 && weekday < 6) {
      return context.L.goalCardTwoAway;
    }
    // half-way there
    if (checkins >= ((target + 1) ~/ 2)) {
      return context.L.goalCardHalfWay;
    }
  } else {
    return checkins == target
        ? context.L.goalCardDone
        : context.L.goalCardExtra;
  }
  return context.L.goalCardDefault;
}
