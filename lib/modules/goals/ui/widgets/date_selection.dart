import 'package:cupertino_calendar_picker/cupertino_calendar_picker.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/date_extensions.dart';
import 'package:neuroworld/core/ui/theme/calendar_picker_styles.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/utils/date_utils.dart';
import 'package:neuroworld/modules/goals/ui/providers/selected_date_provider.dart';

class DateSelection extends ConsumerWidget {
  const DateSelection({
    super.key,
    required this.date,
    required this.minDate,
    required this.isExpanded,
    required this.toggleExpanded,
  });

  final DateTime date;
  final DateTime? minDate;
  final bool isExpanded;
  final Function() toggleExpanded;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(left: 22, right: 11, top: 12),
      child: Row(
        children: [
          Builder(
            builder: (context) => OutlinedButton(
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  width: 1,
                  color: AppColors.stroke,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                foregroundColor: AppColors.textPrimary,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                minimumSize: Size.zero,
              ),
              onPressed: () {
                final renderBox = context.findRenderObject() as RenderBox?;
                showCupertinoCalendarPicker(
                  context,
                  widgetRenderBox: renderBox,
                  minimumDateTime: minDate ?? DateTime.now().startOfDay,
                  initialDateTime: date,
                  maximumDateTime: DateTime.now(),
                  containerDecoration: CalendarPickerStyles.containerDecoration,
                  headerDecoration: CalendarPickerStyles.headerDecoration,
                  mainColor: AppColors.boldOrange,
                  actions: [
                    CancelCupertinoCalendarAction(
                      label: 'Cancel',
                      decoration: CalendarActionDecoration(
                        labelStyle: TextStyles.body1,
                      ),
                      onPressed: () {},
                    ),
                    ConfirmCupertinoCalendarAction(
                      label: 'Confirm',
                      decoration: CalendarActionDecoration(
                        labelStyle: TextStyles.heading5,
                      ),
                      isDefaultAction: true,
                      onPressed: (selectedDate) async {
                        if (selectedDate != date) {
                          await ref
                              .read(selectedDateProvider.notifier)
                              .selectDate(selectedDate);
                        }
                      },
                    ),
                  ],
                );
              },
              child: Row(
                children: [
                  Text(
                    goalsDateFormat.format(date),
                    style: TextStyles.heading5.copyWith(),
                  ),
                  const SizedBox(width: 10),
                  Container(
                    height: 36,
                    width: 1,
                    color: AppColors.stroke,
                  ),
                  const SizedBox(width: 10),
                  Icon(
                    Icons.edit_calendar,
                    size: 22,
                    color: AppColors.textPrimary,
                  ),
                ],
              ),
            ),
          ),
          const Spacer(),
          AnimatedRotation(
            turns: isExpanded ? 0.5 : 0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.linearToEaseOut,
            child: IconButton(
              onPressed: toggleExpanded,
              icon: Assets.svgs.chevronDown.svg(height: 11),
            ),
          )
        ],
      ),
    );
  }
}
