import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/infrastructure/extensions/app_error_extension.dart';
import 'package:neuroworld/core/infrastructure/extensions/date_extensions.dart';
import 'package:neuroworld/core/ui/widgets/loader.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:neuroworld/modules/goals/ui/providers/daily_tracking_list_provider.dart';
import 'package:neuroworld/modules/goals/ui/providers/recently_tracked_goals_provider.dart';
import 'package:neuroworld/modules/goals/ui/providers/selected_date_provider.dart';
import 'package:neuroworld/modules/goals/ui/widgets/empty_goals.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_tracking_card.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goals_plan_card.dart';
import 'package:neuroworld/modules/goals/ui/widgets/save_your_streaks_card.dart';
import 'package:neuroworld/modules/badges/ui/widgets/unlocked_badges_card.dart';

class GoalsList extends ConsumerWidget {
  const GoalsList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dailyTracking = ref.watch(dailyTrackingListProvider);
    final unStartedGoals = ref.watch(authStateProvider.select(
      (auth) =>
          auth?.user.selectedGoals.where((goal) => goal.startDate == null) ??
          [],
    ));
    final isToday = ref.watch(selectedDateProvider).isSameDate(DateTime.now());
    final recentlyTrackedGoals = ref.watch(recentlyTrackedGoalsProvider);

    return Expanded(
      child: dailyTracking.when(
        loading: () => Loader(),
        // TODO-goal proper error widget
        error: (error, stackTrace) => Center(
          child: Text(error.errorMessage(context)),
        ),
        data: (data) => data.isEmpty && (!isToday || unStartedGoals.isEmpty)
            ? EmptyGoals(isToday: isToday)
            : SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: Column(
                    children: [
                      if (isToday)
                        for (final goal in unStartedGoals)
                          GoalTrackingCard(
                            isRecentlyTracked:
                                recentlyTrackedGoals.contains(goal.id),
                            dailyTracking: DailyTracking(
                              selectedGoal: goal,
                              startDate: null,
                            ),
                            isToday: isToday,
                          ),
                      if (isToday &&
                          data.any((tracking) => tracking.gracePeriodDays > 0))
                        SaveYourStreaksCard(),
                      for (final tracking in data)
                        GoalTrackingCard(
                          isRecentlyTracked: recentlyTrackedGoals
                              .contains(tracking.selectedGoal.id),
                          dailyTracking: tracking,
                          isToday: isToday,
                        ),
                      UnlockedBadgesCard(),
                      GoalsPlanCard(),
                    ],
                  ),
                ),
              ),
      ),
    );
  }
}
