// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/custom_bottom_sheet.dart';
import 'package:neuroworld/core/ui/widgets/dialogs.dart';
import 'package:neuroworld/core/ui/widgets/rounded_icon.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:neuroworld/modules/goals/data/models/repair_type.dart';
import 'package:neuroworld/modules/goals/ui/controllers/check_in_controller.dart';
import 'package:neuroworld/modules/goals/ui/controllers/repair_streak_controller.dart';
import 'package:neuroworld/modules/goals/ui/widgets/save_your_streaks_bottom_sheet.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/repair_streak_bottom_sheet.dart';

class SaveYourStreaksCard extends ConsumerWidget {
  const SaveYourStreaksCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 0, 20, 16),
      child: OutlinedButton(
        onPressed: () async {
          final tracking =
              await CustomBottomSheet.showBottomSheet<DailyTracking?>(
            context,
            padding: EdgeInsets.zero,
            child: SaveYourStreaksBottomSheet(),
          );

          if (tracking != null) {
            final repairType =
                await CustomBottomSheet.showBottomSheet<RepairType?>(
              context,
              padding: EdgeInsets.zero,
              child: RepairStreakBottomSheet(tracking: tracking),
            );
            if (repairType == null) return;
            switch (repairType) {
              case RepairType.repair:
                ref
                    .read(repairStreakControllerProvider.notifier)
                    .repairStreak(tracking.selectedGoal.id);

              case RepairType.startFresh:
                final confirm = await Dialogs.showConfirmationDialog(
                      context,
                      title: context.L.startFreshConfirmationTitle,
                      description: context.L.startFreshConfirmationBody,
                      acceptButtonText: context.L.startFreshConfirmationAccept,
                      cancelButtonText: context.L.startFreshConfirmationCancel,
                      icon: RoundedIcon(
                        size: 48,
                        borderRadius: 100,
                        child: Assets.svgs.refresh.svg(),
                      ),
                    ) ??
                    false;
                if (confirm) {
                  ref
                      .read(checkInControllerProvider.notifier)
                      .checkIn(tracking);
                }
            }
          }
        },
        style: OutlinedButton.styleFrom(
          minimumSize: Size.zero,
          padding: const EdgeInsets.all(12),
          foregroundColor: AppColors.textSecondary,
          side: BorderSide(color: AppColors.error, width: 0.75),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: TextStyles.buttonLarge.copyWith(height: 1, fontSize: 17.5),
        ),
        child: Row(
          children: [
            Container(
              height: 48,
              width: 48,
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.error,
                  width: 2.67,
                ),
                borderRadius: BorderRadius.circular(100),
              ),
              padding: const EdgeInsets.all(8),
              child: Assets.svgs.restoreStopwatch.svg(),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.L.repairStreaksCardTitle,
                    style: TextStyles.heading5,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    context.L.repairStreaksCardBody,
                    style: TextStyles.body1,
                  ),
                ],
              ),
            ),
            Assets.svgs.chevronRight.svg(
              height: 16,
              width: 16,
              colorFilter: ColorFilter.mode(
                AppColors.textSecondary,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 6),
          ],
        ),
      ),
    );
  }
}
