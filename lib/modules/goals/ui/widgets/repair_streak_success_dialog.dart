import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/goals/data/models/goal.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_card_title.dart';

class RepairStreakSuccessDialog extends StatelessWidget {
  const RepairStreakSuccessDialog({super.key, required this.goal});

  final Goal goal;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.symmetric(horizontal: 24),
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: AppColors.surfaceSecondary,
          ),
          child: Column(
            children: [
              Assets.svgs.fire.svg(height: 42),
              const SizedBox(height: 12),
              Text(
                context.L.repairStreakSuccessDialogTitle,
                style: TextStyles.heading4,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                context.L.repairStreakSuccessDialogBody1,
                style: TextStyles.body1,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Text(
                context.L.repairStreakSuccessDialogBody2,
                style: TextStyles.body1,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.stroke),
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 2,
                          color: goal.category.color,
                        ),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          SvgPicture.asset(
                            '${Assets.svgs.goals.path}/${goal.imageUrl}.svg',
                          ),
                          if (goal.imageUrl.startsWith('x_'))
                            Transform.rotate(
                              angle: -pi / 4,
                              child: Container(
                                height: double.infinity,
                                width: 3,
                                color: goal.category.color,
                              ),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    GoalCardTitle(title: goal.title, gracePeriodDays: -1),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: PrimaryButton(
                  onPressed: () => NavService.popDialog(context),
                  child: Text(context.L.repairStreakSuccessDialogButton),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }
}
