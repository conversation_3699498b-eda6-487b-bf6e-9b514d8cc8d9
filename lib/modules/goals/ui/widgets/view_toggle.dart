import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/goals/ui/providers/view_mode_provider.dart';

class ViewToggle extends ConsumerWidget {
  const ViewToggle({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMode = ref.watch(viewModeProvider);
    final notifier = ref.read(viewModeProvider.notifier);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
            selectedMode == ViewModeEnum.list
                ? context.L.listView
                : context.L.mapView,
            style: TextStyles.subtitle1
                .copyWith(fontSize: 18, color: AppColors.textPrimary)),
        Container(
          height: 48,
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.stroke),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            children: [
              _buildToggleButton(
                isSelected: selectedMode == ViewModeEnum.list,
                iconPath: selectedMode == ViewModeEnum.list
                    ? Assets.svgs.listSelected.path
                    : Assets.svgs.listUnselected.path,
                onTap: () => notifier.set(ViewModeEnum.list),
              ),
              _buildToggleButton(
                isSelected: selectedMode == ViewModeEnum.map,
                iconPath: selectedMode == ViewModeEnum.map
                    ? Assets.svgs.mapSelected.path
                    : Assets.svgs.mapUnselected.path,
                onTap: () => notifier.set(ViewModeEnum.map),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildToggleButton({
    required bool isSelected,
    required String iconPath,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        alignment: Alignment.center,
        child: SvgPicture.asset(
          iconPath,
          width: 40,
          height: 40,
        ),
      ),
    );
  }
}
