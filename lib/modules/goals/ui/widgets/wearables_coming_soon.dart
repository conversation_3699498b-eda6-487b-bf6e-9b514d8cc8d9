import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class WearablesComingSoon extends StatelessWidget {
  const WearablesComingSoon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 3),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.wearablesLabelBeginGradient,
            AppColors.wearablesLabelEndGradient,
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(100),
      ),
      child: Text(
        context.L.wearablesComingSoonLabel,
        style: TextStyles.subheading2.copyWith(
          fontSize: 12,
          color: Colors.white,
        ),
      ),
    );
  }
}
