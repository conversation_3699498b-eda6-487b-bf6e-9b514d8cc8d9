import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

class GoalsAppBarBackgroundExtension extends StatelessWidget {
  const GoalsAppBarBackgroundExtension({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 20,
      width: double.infinity,
      child: Row(
        children: [
          Expanded(child: Container(color: AppColors.appBarOrangeBegin)),
          Expanded(child: Container(color: AppColors.appBarOrangeEnd))
        ],
      ),
    );
  }
}
