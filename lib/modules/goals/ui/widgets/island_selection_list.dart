import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/auth/ui/providers/selected_island_provider.dart';
import 'package:neuroworld/modules/goals/data/island_category_data.dart';

class IslandSelectionList extends ConsumerWidget {
  final List<String> disabledKeys;
  final List<IslandCategory> allGoals;

  const IslandSelectionList({
    super.key,
    required this.disabledKeys,
    required this.allGoals,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selected = ref.watch(selectedIslandProvider);

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      itemCount: allGoals.length,
      itemBuilder: (context, index) {
        final goal = allGoals[index];
        final isDisabled = disabledKeys.contains(goal.key);
        final isSelected = selected == goal.key;

        return GestureDetector(
          onTap: isDisabled
              ? null
              : () {
                  ref.read(selectedIslandProvider.notifier).state =
                      isSelected ? null : goal.key;
                },
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14),
              border: Border.all(color: AppColors.stroke),
            ),
            child: Row(
              children: [
                SvgPicture.asset(
                  goal.iconPath,
                  fit: BoxFit.cover,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(8, 0, 8, 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(goal.title,
                            style: TextStyles.subheading2.copyWith(
                              fontSize: 18,
                              color: AppColors.textPrimary,
                            )),
                        const SizedBox(height: 2),
                        Text(goal.subtitle,
                            style: TextStyles.buttonMedium.copyWith(
                              color: AppColors.textSecondary,
                              height: 1.2,
                            )),
                      ],
                    ),
                  ),
                ),
                if (isDisabled)
                  SvgPicture.asset(
                    Assets.svgs.disabledIslandsSelected.path,
                    width: 32,
                    height: 40,
                  )
                else
                  SvgPicture.asset(
                    isSelected
                        ? Assets.svgs.selected.path
                        : Assets.svgs.deselected.path,
                    width: 24,
                    height: 24,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
