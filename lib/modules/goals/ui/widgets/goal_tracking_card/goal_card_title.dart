import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class GoalCardTitle extends StatelessWidget {
  const GoalCardTitle({
    super.key,
    required this.title,
    required this.gracePeriodDays,
  });

  final String title;
  final int gracePeriodDays;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(top: 8, left: 6, bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                title,
                style: TextStyles.heading5,
              ),
            ),
            if (gracePeriodDays > 0)
              Container(
                decoration: BoxDecoration(
                  color: AppColors.restoreLabel,
                  borderRadius: BorderRadius.circular(40),
                ),
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                margin: const EdgeInsets.only(top: 6),
                child: Text(
                  "$gracePeriodDays ${gracePeriodDays == 1 ? 'day' : 'days'} left to repair streak",
                  style: TextStyles.subheading2.copyWith(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
