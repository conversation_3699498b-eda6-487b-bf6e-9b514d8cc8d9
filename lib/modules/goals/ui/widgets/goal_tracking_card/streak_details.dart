import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:neuroworld/modules/goals/ui/utils/goal_text_util.dart';

class StreakDetails extends StatelessWidget {
  const StreakDetails({
    super.key,
    required this.dailyTracking,
  });

  final DailyTracking dailyTracking;

  @override
  Widget build(BuildContext context) {
    // since backend wont have updated currentWeek until we check-in for a new week,
    // we have to manually set check-ins to 0 if device time indicates a new week has started
    final checkins = dailyTracking.isNewWeek
        ? 0
        : dailyTracking.selectedGoal.currentCheckins;
    return AnimatedSize(
      duration: const Duration(milliseconds: 150),
      curve: Curves.easeOutCubic,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 14),
        child: Row(
          children: [
            Text(
              getGoalText(
                context,
                weekday: dailyTracking.currentIndex,
                checkins: checkins,
                target: dailyTracking.selectedGoal.target,
                isUnstarted: dailyTracking.selectedGoal.startDate == null ||
                    dailyTracking.shouldResetGoal,
              ),
              style: TextStyles.body1,
            ),
            const Spacer(),
            Text(
              checkins.toString(),
              style: TextStyles.heading5.copyWith(
                color: checkins < dailyTracking.selectedGoal.target
                    ? AppColors.disabledText
                    : AppColors.nutrition,
                fontSize: 17,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 2),
              child: Text("/", style: TextStyles.body1),
            ),
            Text(
              dailyTracking.selectedGoal.target.toString(),
              style: TextStyles.body1,
            )
          ],
        ),
      ),
    );
  }
}
