import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';

class GoalCardIcon extends StatelessWidget {
  const GoalCardIcon({
    super.key,
    required this.dailyTracking,
  });

  final DailyTracking dailyTracking;

  @override
  Widget build(BuildContext context) {
    final categoryColor = dailyTracking.selectedGoal.goal.category.color;
    final week =
        dailyTracking.shouldResetGoal && dailyTracking.gracePeriodDays <= 0
            ? 0
            : dailyTracking.selectedGoal.currentWeek +
                dailyTracking.selectedGoal.restoreWeekOffset;
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                border: Border.all(
                  width: 2,
                  color: dailyTracking.gracePeriodDays > 0
                      ? AppColors.goalStreakEmpty
                      : categoryColor,
                ),
                borderRadius: BorderRadius.circular(30),
              ),
              margin: const EdgeInsets.only(
                left: 14,
                top: 14,
                right: 14,
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  SvgPicture.asset(
                    '${Assets.svgs.goals.path}/${dailyTracking.selectedGoal.goal.imageUrl}.svg',
                  ),
                  if (dailyTracking.selectedGoal.goal.imageUrl.startsWith('x_'))
                    Transform.rotate(
                      angle: -pi / 4,
                      child: Container(
                        height: double.infinity,
                        width: 3,
                        color: dailyTracking.gracePeriodDays > 0
                            ? AppColors.goalStreakEmpty
                            : categoryColor,
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
        Container(
          decoration: BoxDecoration(
            color: dailyTracking.selectedGoal.startDate == null ||
                    dailyTracking.shouldResetGoal
                ? AppColors.goalStreakEmpty
                : AppColors.boldOrange,
            borderRadius: BorderRadius.circular(50),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 7,
            vertical: 5,
          ),
          margin: const EdgeInsets.only(bottom: 12),
          child: Text(
            'Week $week',
            style: TextStyles.inputLabel.copyWith(
              color: Colors.white,
              fontSize: 12.5,
            ),
          ),
        )
      ],
    );
  }
}
