import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/frequency_slider.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/secondary_button.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';
import 'package:neuroworld/modules/goals/ui/controllers/edit_goal_controller.dart';

class EditGoalSheet extends HookConsumerWidget {
  const EditGoalSheet({
    super.key,
    required this.initialValue,
    required this.selectedGoal,
  });

  final int initialValue;
  final SelectedGoal selectedGoal;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedValue = useState(initialValue);
    return Column(
      children: [
        const SizedBox(height: 26),
        Text(
          context.L.editGoalSheetTitle,
          style: TextStyles.heading4,
        ),
        const SizedBox(height: 12),
        Text(
          context.L.editGoalSheetSubtitle,
          style: TextStyles.body1.copyWith(
            fontSize: 18,
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        Container(
          padding: const EdgeInsets.all(0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.stroke,
            ),
          ),
          child: FrequencySlider(
            selectedValue: selectedValue.value,
            onUpdate: (newValue) => selectedValue.value = newValue,
          ),
        ),
        const SizedBox(height: 24),
        SizedBox(
          width: double.infinity,
          child: PrimaryButton(
            onPressed: () {
              if (selectedValue.value != initialValue) {
                NavService.popDialog(context);
                ref
                    .read(editGoalControllerProvider.notifier)
                    .editGoal(selectedGoal.id, selectedValue.value);
              }
            },
            disabled: selectedValue.value == initialValue,
            child: Text("Save"),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: SecondaryButton(
            height: 47,
            onPressed: () => NavService.popDialog(context),
            child: Text("Cancel"),
          ),
        ),
        const SizedBox(height: 28),
      ],
    );
  }
}
