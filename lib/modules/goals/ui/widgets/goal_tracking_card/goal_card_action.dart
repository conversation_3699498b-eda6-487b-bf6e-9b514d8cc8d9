import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/widgets/drawers.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_details_sheet.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goals_more_sheet.dart';

class GoalCardAction extends StatelessWidget {
  const GoalCardAction({
    super.key,
    required this.isToday,
    required this.dailyTracking,
  });

  final bool isToday;
  final DailyTracking dailyTracking;

  @override
  Widget build(BuildContext context) {
    if (isToday && dailyTracking.gracePeriodDays > 0) return SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.only(top: 5, right: 2),
      child: IconButton(
        onPressed: () {
          Drawers.showCustomBottomSheet(
            context,
            child: isToday
                ? GoalsMoreSheet(
                    initialValue: dailyTracking.selectedGoal.target,
                    selectedGoal: dailyTracking.selectedGoal,
                  )
                : GoalDetailsSheet(
                    goal: dailyTracking.selectedGoal.goal,
                  ),
          );
        },
        icon: isToday
            ? Icon(
                Icons.more_vert_rounded,
                color: AppColors.textSecondary,
              )
            : Assets.svgs.info.svg(
                width: 22,
                height: 22,
                colorFilter: ColorFilter.mode(
                  AppColors.textSecondary,
                  BlendMode.srcIn,
                ),
              ),
      ),
    );
  }
}
