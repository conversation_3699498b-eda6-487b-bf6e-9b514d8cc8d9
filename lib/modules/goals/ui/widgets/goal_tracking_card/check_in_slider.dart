import 'package:action_slider/action_slider.dart';
import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:shimmer/shimmer.dart';

class CheckInSlider extends StatelessWidget {
  const CheckInSlider({
    super.key,
    required this.isRecentlyTracked,
    required this.isToday,
    required this.isMarkedComplete,
    required this.dailyTracking,
    required this.onSlideComplete,
  });

  final bool isRecentlyTracked;
  final bool isToday;
  final bool isMarkedComplete;
  final DailyTracking dailyTracking;
  final Function(ActionSliderController)? onSlideComplete;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50),
        color: AppColors.sliderGradientEnd,
      ),
      margin: EdgeInsets.all(10),
      child: !isToday || isMarkedComplete
          ? AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: 50,
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(50),
                color: isMarkedComplete && !isRecentlyTracked
                    ? AppColors.nutrition
                    : isRecentlyTracked
                        ? AppColors.sliderRecentlyTracked
                        : AppColors.sliderGradientBegin,
              ),
              child: Stack(
                alignment: isMarkedComplete
                    ? Alignment.centerRight
                    : Alignment.centerLeft,
                children: [
                  Container(
                    padding: const EdgeInsets.all(11),
                    height: 50,
                    width: 38,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(50),
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.sliderButtonDropShadow,
                          spreadRadius: 3,
                          blurRadius: 5,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: isMarkedComplete
                        ? Assets.svgs.checkGreen.svg()
                        : Assets.svgs.chevronRight.svg(
                            height: 16,
                            width: 16,
                            colorFilter: ColorFilter.mode(
                              AppColors.disabledText,
                              BlendMode.srcIn,
                            ),
                          ),
                  ),
                  Center(
                    child: Text(
                      isMarkedComplete ? "Done!" : "Missed",
                      style: TextStyles.body1.copyWith(
                        color: isMarkedComplete
                            ? Colors.white
                            : AppColors.disabledText,
                        fontSize: 18,
                      ),
                    ),
                  ),
                ],
              ),
            )
          : ActionSlider.standard(
              height: 50,
              rolling: false,
              successIcon: Assets.svgs.checkGreen.svg(width: 11, height: 11),
              foregroundBorderRadius: BorderRadius.circular(30),
              action: onSlideComplete,
              toggleColor: Colors.white,
              customOuterBackgroundBuilder: (context, sliderState, p2) =>
                  Opacity(
                opacity: sliderState.position,
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(50),
                    boxShadow: [],
                    gradient: LinearGradient(
                      colors: [
                        AppColors.sliderEnableGradientBegin,
                        AppColors.sliderEnableGradientEnd,
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                  ),
                ),
              ),
              icon: Assets.svgs.chevronRight.svg(height: 16, width: 16),
              failureIcon: Icon(Icons.close_rounded, color: AppColors.error),
              child: Shimmer.fromColors(
                baseColor: AppColors.textPrimary,
                highlightColor: AppColors.disabledText,
                period: Duration(milliseconds: 1500),
                child: Text(
                  dailyTracking.gracePeriodDays > 0
                      ? context.L.repairStreakSlider
                      : context.L.checkInSlider,
                  style: TextStyles.body1.copyWith(fontSize: 18),
                ),
              ),
            ),
    );
  }
}
