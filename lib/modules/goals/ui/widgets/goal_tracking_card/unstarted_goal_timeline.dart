import 'package:collection/collection.dart';
import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

class UnstartedGoalTimeline extends StatelessWidget {
  const UnstartedGoalTimeline({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(7, (_) => true)
          .mapIndexed(
            (index, isMarked) => Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.goalStreakEmpty,
                  border: Border(
                    right: index == 6
                        ? BorderSide.none
                        : BorderSide(
                            color: AppColors.goalStreakOutline,
                            width: 2,
                          ),
                  ),
                  borderRadius: BorderRadius.horizontal(
                    left: Radius.circular(index == 0 ? 30 : 0),
                    right: Radius.circular(index == 6 ? 30 : 0),
                  ),
                ),
              ),
            ),
          )
          .asList(),
    );
  }
}
