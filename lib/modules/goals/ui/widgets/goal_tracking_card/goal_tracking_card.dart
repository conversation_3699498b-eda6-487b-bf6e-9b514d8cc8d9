// ignore_for_file: use_build_context_synchronously

import 'package:action_slider/action_slider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/widgets/custom_bottom_sheet.dart';
import 'package:neuroworld/core/ui/widgets/dialogs.dart';
import 'package:neuroworld/core/ui/widgets/rounded_icon.dart';
import 'package:neuroworld/modules/badges/data/models/badge.dart';
import 'package:neuroworld/modules/badges/ui/widgets/badge_achieved_dialog.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:neuroworld/modules/goals/data/models/repair_type.dart';
import 'package:neuroworld/modules/goals/ui/controllers/check_in_controller.dart';
import 'package:neuroworld/modules/goals/ui/controllers/repair_streak_controller.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/check_in_slider.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_card_action.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_card_icon.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_card_title.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_timeline.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/streak_details.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/unstarted_goal_timeline.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/repair_streak_bottom_sheet.dart';

// TODO-goal: refactor so dailyTracking isn't passed as param
// but instead ref.watched inside child widgets
class GoalTrackingCard extends ConsumerWidget {
  const GoalTrackingCard({
    super.key,
    required this.dailyTracking,
    required this.isToday,
    required this.isRecentlyTracked,
  });
  final DailyTracking dailyTracking;
  final bool isToday;
  final bool isRecentlyTracked;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isMarkedComplete = dailyTracking.startDate != null &&
        dailyTracking.weeklyCheckInTracking[dailyTracking.currentIndex];
    final isRepairable = dailyTracking.gracePeriodDays > 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16, left: 22, right: 22),
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.stroke),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(width: 6),
                GoalCardIcon(dailyTracking: dailyTracking),
                GoalCardTitle(
                  title: dailyTracking.selectedGoal.goal.title,
                  gracePeriodDays: isToday ? dailyTracking.gracePeriodDays : -1,
                ),
                GoalCardAction(isToday: isToday, dailyTracking: dailyTracking)
              ],
            ),
          ),
          if (!isToday || !isRepairable)
            Container(
              height: 14,
              decoration: BoxDecoration(
                border: Border.all(
                  width: 2,
                  color: AppColors.goalStreakOutline,
                ),
                borderRadius: BorderRadius.circular(30),
              ),
              margin: const EdgeInsets.symmetric(horizontal: 14),
              child: dailyTracking.selectedGoal.startDate == null ||
                      (isToday && dailyTracking.shouldResetGoal)
                  ? UnstartedGoalTimeline()
                  : GoalTimeline(
                      weeklyCheckins: dailyTracking.weeklyCheckInTracking,
                      currentIndex: dailyTracking.currentIndex,
                      target: dailyTracking.selectedGoal.target,
                    ),
            ),
          if (isToday && !isRepairable)
            Column(
              children: [
                const SizedBox(height: 8),
                StreakDetails(dailyTracking: dailyTracking),
              ],
            ),
          if (!isRepairable) const SizedBox(height: 4),
          CheckInSlider(
            isRecentlyTracked: isRecentlyTracked,
            isToday: isToday,
            isMarkedComplete: isMarkedComplete,
            dailyTracking: dailyTracking,
            onSlideComplete: (controller) => _onSlideComplete(
              context,
              ref,
              controller,
              dailyTracking,
            ),
          ),
        ],
      ),
    );
  }

  void _resetController(ActionSliderController controller) {
    try {
      controller.reset();
    } catch (e) {
      // silence errors if controller cant be reset due to stale state
      return;
    }
  }

  void _handleCheckIn(BuildContext context, ActionSliderController controller, WidgetRef ref) async {
    controller.loading();
    final (:success, :checkIn) = await ref
        .read(checkInControllerProvider.notifier)
        .checkIn(dailyTracking);
    if (checkIn?.badgesAwarded != null && checkIn!.badgesAwarded!.isNotEmpty) {
        Dialogs.showFullScreenDialog(
          context,
          barrierDismissible: true,
          content: BadgeAchievedDialog(
            badge: checkIn.badgesAwarded!.first.badge.type,
          ),
        );
    }
    if (success) {
      controller.success();
    } else {
      controller.failure();
      Future.delayed(Duration(seconds: 4), () => _resetController(controller));
    }
  }

  void _onSlideComplete(
    BuildContext context,
    WidgetRef ref,
    ActionSliderController controller,
    DailyTracking tracking,
  ) async {
    if (tracking.gracePeriodDays > 0) {
      final repairType = await CustomBottomSheet.showBottomSheet<RepairType?>(
        context,
        padding: EdgeInsets.zero,
        child: RepairStreakBottomSheet(tracking: tracking),
      );

      if (repairType == null) {
        controller.reset();
        return;
      }

      switch (repairType) {
        case RepairType.repair:
          controller.loading();
          final success = await ref
              .read(repairStreakControllerProvider.notifier)
              .repairStreak(tracking.selectedGoal.id);
          if (!success) {
            controller.failure();
          }

        case RepairType.startFresh:
          final confirm = await Dialogs.showConfirmationDialog(
                context,
                title: context.L.startFreshConfirmationTitle,
                description: context.L.startFreshConfirmationBody,
                acceptButtonText: context.L.startFreshConfirmationAccept,
                cancelButtonText: context.L.startFreshConfirmationCancel,
                icon: RoundedIcon(
                  size: 48,
                  borderRadius: 100,
                  child: Assets.svgs.refresh.svg(),
                ),
              ) ??
              false;
          if (confirm) _handleCheckIn(context, controller, ref);
      }
    } else {
      _handleCheckIn(context, controller, ref);
    }
  }
}
