import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_info_header.dart';

class GoalRulesSheet extends StatelessWidget {
  const GoalRulesSheet({super.key, required this.goal});

  final SelectedGoal goal;

  @override
  Widget build(BuildContext context) {
    print('SELECTED GOAL:');
    inspect(goal);
    return Column(
      children: [
        const SizedBox(height: 16),
        ChatBubbleWithAvatar(
          avatar: Assets.svgs.avatars.mylaWave.svg(width: 70),
          toolTipAlignment: Alignment.centerLeft,
          child: Text(
            context.L.goalRulesMylaTooltip,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: AppColors.stroke),
          ),
          child: Column(
            children: [
              const SizedBox(height: 12),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: GoalInfoHeader(goal: goal.goal),
              ),
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: double.infinity,
                  maxHeight: MediaQuery.of(context).size.height * 0.45,
                ),
                child: RawScrollbar(
                  radius: Radius.circular(100),
                  thumbColor: AppColors.stroke,
                  trackColor: AppColors.appBarOrangeBegin,
                  padding: const EdgeInsets.only(right: 4, bottom: 12, top: 10),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          const SizedBox(height: 10),
                          SizedBox(
                            width: double.infinity,
                            child: Text(
                              goal.ruleText.isEmpty
                                  ? context.L.goalsRulesEmpty
                                  : goal.ruleText,
                              style: TextStyles.body2,
                            ),
                          ),
                          const SizedBox(height: 12),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: PrimaryButton(
            onPressed: () => NavService.popDialog(context),
            child: Text('Close'),
          ),
        ),
        const SizedBox(height: 28),
      ],
    );
  }
}
