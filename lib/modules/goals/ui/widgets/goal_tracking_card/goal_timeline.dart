import 'package:collection/collection.dart';
import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

class GoalTimeline extends StatelessWidget {
  const GoalTimeline({
    super.key,
    required this.weeklyCheckins,
    required this.currentIndex,
    required this.target,
  });

  final List<bool> weeklyCheckins;
  final int currentIndex;
  final int target;

  @override
  Widget build(BuildContext context) {
    int weeklyCheckinCount = 0;
    return Row(
      children: weeklyCheckins.mapIndexed(
        (index, isMarked) {
          weeklyCheckinCount += isMarked ? 1 : 0;
          return Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: index == currentIndex
                    ? AppColors.secondaryBlue
                    : !isMarked
                        ? AppColors.goalStreakEmpty
                        : weeklyCheckinCount > target
                            ? AppColors.boldOrange
                            : AppColors.nutrition,
                border: Border(
                  right: index == 6
                      ? BorderSide.none
                      : BorderSide(
                          color: AppColors.goalStreakOutline,
                          width: 2,
                        ),
                ),
                borderRadius: BorderRadius.horizontal(
                  left: Radius.circular(index == 0 ? 30 : 0),
                  right: Radius.circular(index == 6 ? 30 : 0),
                ),
              ),
            ),
          );
        },
      ).asList(),
    );
  }
}
