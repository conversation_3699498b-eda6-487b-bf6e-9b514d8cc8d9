import 'package:collection/collection.dart';
import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/date_extensions.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/goals/ui/providers/selected_date_provider.dart';

class WeeklyTrackingDates extends ConsumerWidget {
  const WeeklyTrackingDates({
    super.key,
    required this.date,
    required this.minDate,
  });

  final DateTime date;
  final DateTime? minDate;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = date.weekday % 7;
    final startOfWeek = date.subtract(Duration(days: selectedIndex));
    final week =
        List.generate(7, (i) => startOfWeek.add(Duration(days: i))).lock;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: week.mapIndexed(
          (index, date) {
            final isSelected = index == selectedIndex;
            final isToday = date.isSameDate(DateTime.now());
            final disabled = !isToday &&
                (date.isAfter(DateTime.now().startOfDay) ||
                    (minDate == null || date.isBefore(minDate!)));
            return GestureDetector(
              onTap: () {
                if (!isSelected && !disabled) {
                  ref.read(selectedDateProvider.notifier).selectDate(date);
                }
              },
              child: Container(
                height: 46,
                width: 46,
                // little hack to make sure dates line up underneath day labels
                margin: EdgeInsets.only(
                  left: index != 0 && index != 6 ? 3 : 0,
                ),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.secondaryBlue
                      : isToday
                          ? AppColors.unwindBlue
                          : Colors.white,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Text(
                  date.day.toString(),
                  style: TextStyles.buttonLarge.copyWith(
                    fontSize: 20,
                    color: isSelected
                        ? Colors.white
                        : disabled
                            ? AppColors.disabledText
                            : isToday
                                ? AppColors.secondaryBlue
                                : AppColors.textPrimary,
                    fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                  ),
                ),
              ),
            );
          },
        ).asList(),
      ),
    );
  }
}
