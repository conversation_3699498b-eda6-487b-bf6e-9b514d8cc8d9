import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/ui/providers/selected_date_provider.dart';
import 'package:neuroworld/modules/goals/ui/widgets/date_selection.dart';
import 'package:neuroworld/modules/goals/ui/widgets/weekly_tracking_dates.dart';
import 'package:neuroworld/modules/goals/ui/widgets/weekly_tracking_labels.dart';

class GoalsTabHeader extends HookConsumerWidget {
  const GoalsTabHeader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final date = ref.watch(selectedDateProvider);
    final minDate = ref.watch(
      authStateProvider.select((auth) => auth?.user.profile.firstTrackedDate),
    );
    final isExpanded = useState(false);

    return Column(
      children: [
        DateSelection(
          date: date,
          minDate: minDate,
          isExpanded: isExpanded.value,
          toggleExpanded: () {
            isExpanded.value = !isExpanded.value;
          },
        ),
        AnimatedSize(
          duration: Duration(milliseconds: 200),
          curve: Curves.easeOutQuart,
          child: !isExpanded.value
              ? const SizedBox(height: 10)
              : Column(
                  children: [
                    const SizedBox(height: 10),
                    WeeklyTrackingLabels(),
                    const SizedBox(height: 6),
                    WeeklyTrackingDates(
                      date: date,
                      minDate: minDate,
                    ),
                    const SizedBox(height: 18),
                  ],
                ),
        ),
        Container(height: 0.75, color: AppColors.stroke),
      ],
    );
  }
}
