import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:neuroworld/modules/goals/ui/providers/daily_tracking_list_provider.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/check_in_slider.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_card_icon.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_card_title.dart';

class SaveYourStreaksBottomSheet extends HookConsumerWidget {
  const SaveYourStreaksBottomSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = useScrollController();
    final isScrolledToEnd = useState(false);

    final trackingList = ref
        .watch(dailyTrackingListProvider)
        .requireValue
        .where((tracking) => tracking.gracePeriodDays > 0);
    scrollController.addListener(() {
      isScrolledToEnd.value = scrollController.position.extentAfter < 10;
    });
    final isScrollable = trackingList.length > 1;

    return Column(
      children: [
        const SizedBox(height: 16),
        Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    context.L.saveYourStreaksTitle,
                    style: TextStyles.heading4,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.L.saveYourStreaksSubtitle,
                    textAlign: TextAlign.center,
                    style: TextStyles.body1,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            AnimatedContainer(
              duration: Duration(milliseconds: 100),
              constraints: BoxConstraints(
                maxWidth: double.infinity,
                maxHeight: MediaQuery.of(context).size.height * 0.39,
              ),
              decoration: BoxDecoration(
                gradient: isScrollable && !isScrolledToEnd.value
                    ? LinearGradient(
                        colors: [
                          Colors.transparent,
                          AppColors.dropShadow,
                        ],
                        stops: [0.97, 1],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      )
                    : null,
              ),
              child: RawScrollbar(
                controller: scrollController,
                radius: Radius.circular(100),
                thumbColor: AppColors.stroke,
                trackColor: AppColors.appBarOrangeBegin,
                padding: const EdgeInsets.only(
                  right: 8,
                  bottom: 2,
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: SingleChildScrollView(
                    controller: scrollController,
                    physics: isScrollable
                        ? ScrollPhysics()
                        : NeverScrollableScrollPhysics(),
                    child: Column(
                      children: [
                        for (final tracking in trackingList)
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.stroke),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            margin: const EdgeInsets.only(bottom: 16),
                            child: Column(
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const SizedBox(width: 6),
                                    GoalCardIcon(dailyTracking: tracking),
                                    GoalCardTitle(
                                      title: tracking.selectedGoal.goal.title,
                                      gracePeriodDays: tracking.gracePeriodDays,
                                    ),
                                  ],
                                ),
                                CheckInSlider(
                                  isRecentlyTracked: false,
                                  isToday: true,
                                  isMarkedComplete: false,
                                  dailyTracking: tracking,
                                  onSlideComplete: (controller) =>
                                      NavService.pop<DailyTracking>(
                                    context,
                                    result: tracking,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        Container(height: 0.75, color: AppColors.stroke),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            children: [
              ChatBubbleWithAvatar(
                avatar: Assets.svgs.avatars.mylaWave.svg(width: 70),
                toolTipAlignment: Alignment.centerLeft,
                child: Text(
                  context.L.saveYourStreaksTooltip,
                  style: TextStyles.tooltipBody.copyWith(
                    color: AppColors.chatBubbleText,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: PrimaryButton(
                  onPressed: () => NavService.pop(context),
                  child: Text(context.L.saveYourStreaksButton),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 28),
      ],
    );
  }
}
