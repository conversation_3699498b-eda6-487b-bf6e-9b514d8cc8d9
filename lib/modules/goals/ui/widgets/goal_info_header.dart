import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/goals/data/models/goal.dart';
import 'package:neuroworld/modules/goals/ui/widgets/wearables_coming_soon.dart';

class GoalInfoHeader extends StatelessWidget {
  const GoalInfoHeader({super.key, required this.goal});

  final Goal goal;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 50,
              width: 50,
              decoration: BoxDecoration(
                border: Border.all(
                  width: 2,
                  color: goal.category.color,
                ),
                borderRadius: BorderRadius.circular(30),
              ),
              child: <PERSON>ack(
                alignment: Alignment.center,
                children: [
                  SvgPicture.asset(
                    '${Assets.svgs.goals.path}/${goal.imageUrl}.svg',
                  ),
                  if (goal.imageUrl.startsWith('x_'))
                    Transform.rotate(
                      angle: -pi / 4,
                      child: Container(
                        height: double.infinity,
                        width: 3,
                        color: goal.category.color,
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  WearablesComingSoon(),
                  const SizedBox(height: 6),
                  Text(
                    goal.title,
                    style: TextStyles.heading5.copyWith(fontSize: 18.5),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 6),
                  Text(
                    goal.category.name.toUpperCase(),
                    style: TextStyles.heading5.copyWith(
                      color: goal.category.color,
                      fontSize: 14.5,
                    ),
                  )
                ],
              ),
            )
          ],
        ),
        const SizedBox(height: 12),
        Container(color: AppColors.stroke, height: 1),
      ],
    );
  }
}
