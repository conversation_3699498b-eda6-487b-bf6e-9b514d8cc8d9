import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class WeeklyTrackingLabels extends StatelessWidget {
  const WeeklyTrackingLabels({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 44),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          for (final day in weekDays)
            Text(
              day,
              style: TextStyles.heading5.copyWith(
                fontSize: 13,
                color: AppColors.textTertiary,
              ),
            )
        ],
      ),
    );
  }
}

final weekDays = ["S", "M", "T", "W", "T", "F", "S"];
