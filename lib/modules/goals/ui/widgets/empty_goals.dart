import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';

class EmptyGoals extends StatelessWidget {
  const EmptyGoals({super.key, required this.isToday});

  final bool isToday;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 22),
      margin: const EdgeInsets.only(bottom: 50),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Assets.svgs.avatars.mylaWave.svg(height: 100),
          const SizedBox(height: 25),
          Text(
            context.L.emptyGoalsTitle,
            style: TextStyles.heading4,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            isToday ? context.L.emptyGoalsText : context.L.noTrackingFound,
            style: TextStyles.body2,
            textAlign: TextAlign.center,
          ),
          if (isToday) const SizedBox(height: 28),
          if (isToday)
            SizedBox(
              width: double.infinity,
              child: PrimaryButton(
                onPressed: () => AddGoalRoute().go(context),
                child: Text(context.L.emptyGoalsAction),
              ),
            ),
        ],
      ),
    );
  }
}
