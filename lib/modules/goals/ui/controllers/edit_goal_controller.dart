import 'package:neuroworld/core/infrastructure/error/app_exceptions.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/data/models/check_in.dart';
import 'package:neuroworld/modules/goals/data/services/goals_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'edit_goal_controller.g.dart';

@riverpod
class EditGoalController extends _$EditGoalController {
  @override
  Future<CheckIn?> build() async => null;

  Future<bool> editGoal(String goalId, int newTarget) async {
    state = const AsyncLoading();
    try {
      state = AsyncData(
        await ref.watch(goalsServiceProvider).editGoal(goalId, newTarget),
      );
      return true;
    } catch (err, stack) {
      if (err is ServerException && err.code == 403) {
        // 403 on edit goal indicates user has stale info, re-fetch from /me api
        final auth = await ref.watch(authServiceProvider).refreshUser();
        ref.read(authStateProvider.notifier).login(auth);
        ref.invalidate(getMonthlyTrackingProvider);
        ref.invalidate(getTodaysTrackingProvider);
      }
      state = AsyncValue.error(err, stack);
      return false;
    }
  }

  void resetState() => state = AsyncData(null);
}
