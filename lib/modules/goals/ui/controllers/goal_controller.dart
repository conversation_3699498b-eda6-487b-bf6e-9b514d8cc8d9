import 'dart:async';

import 'package:neuroworld/modules/goals/data/models/request/get_goal_request.dart';
import 'package:neuroworld/modules/goals/data/models/response/get_goal_response.dart';
import 'package:neuroworld/modules/goals/data/services/goals_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'goal_controller.g.dart';

@riverpod
class GoalController extends _$GoalController {
  @override
  FutureOr<GetGoalsResponse?> build() => null;

  Future<GetGoalsResponse?> getGoals({
    GetGoalRequest? request,
  }) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(goalsServiceProvider).getGoal(request);
      state = AsyncData(result);
    } catch (e, s) {
      state = AsyncError(e, s);
    }
    return state.valueOrNull;
  }
}
