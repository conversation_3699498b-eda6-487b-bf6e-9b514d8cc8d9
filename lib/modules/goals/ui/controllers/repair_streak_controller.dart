import 'package:neuroworld/core/infrastructure/error/app_exceptions.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/data/models/repair_streak.dart';
import 'package:neuroworld/modules/goals/data/services/goals_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'repair_streak_controller.g.dart';

@riverpod
class RepairStreakController extends _$RepairStreakController {
  @override
  FutureOr<RepairStreak?> build() => null;

  Future<bool> repairStreak(String selectedGoalId) async {
    state = const AsyncLoading();
    try {
      await Future.delayed(Duration(seconds: 2));
      state = AsyncData(
        await ref.watch(goalsServiceProvider).repairStreak(selectedGoalId),
      );
      return true;
    } catch (err, stack) {
      if (err is ServerException && err.code == 403) {
        // 403 on check-in indicates user has stale info, re-fetch from /me api
        final auth = await ref.watch(authServiceProvider).refreshUser();
        ref.read(authStateProvider.notifier).login(auth);
        ref.invalidate(getMonthlyTrackingProvider);
        ref.invalidate(getTodaysTrackingProvider);
      }
      state = AsyncValue.error(err, stack);
      return false;
    }
  }

  void resetState() => state = AsyncData(null);
}
