import 'package:neuroworld/core/infrastructure/extensions/date_extensions.dart';
import 'package:neuroworld/modules/goals/data/models/goal_tracking.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'selected_date_provider.g.dart';

@riverpod
class SelectedDate extends _$SelectedDate {
  @override
  DateTime build() => DateTime.now().startOfDay;

  selectDate(DateTime newDate) => state = newDate.startOfDay;

  Future<bool> markComplete(GoalTracking goal) async {
    return true;
  }
}
