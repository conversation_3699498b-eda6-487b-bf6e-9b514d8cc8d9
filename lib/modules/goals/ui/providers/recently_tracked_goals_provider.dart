import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'recently_tracked_goals_provider.g.dart';

@riverpod
class RecentlyTrackedGoals extends _$RecentlyTrackedGoals {
  @override
  List<String> build() {
    return [];
  }

  void addGoalId(String goalId) {
    final newList = [...state, goalId];
    state = newList;

    Future.delayed(Duration(seconds: 30), () {
      removeGoalId(goalId);
    });
  }

  void removeGoalId(String goalId) {
    final newList = state.where((id) => id != goalId).toList();
    state = newList;
  }

  void resetGoals() {
    state = [];
  }
}