import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/data/models/check_in.dart';
import 'package:neuroworld/modules/goals/data/models/repair_streak.dart';
import 'package:neuroworld/modules/goals/data/services/goals_service.dart';
import 'package:neuroworld/modules/goals/ui/controllers/check_in_controller.dart';
import 'package:neuroworld/modules/goals/ui/controllers/edit_goal_controller.dart';
import 'package:neuroworld/modules/goals/ui/controllers/repair_streak_controller.dart';
import 'package:neuroworld/modules/goals/ui/widgets/check_in_success_dialog.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goals_app_bar_background_extension.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goals_header.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goals_list.dart';
import 'package:neuroworld/modules/goals/ui/widgets/repair_streak_success_dialog.dart';

class GoalsScreen extends HookConsumerWidget {
  const GoalsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _goalsTabListeners(ref, context);

    return Stack(
      children: [
        const GoalsAppBarBackgroundExtension(),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            children: [GoalsTabHeader(), GoalsList()],
          ),
        ),
      ],
    );
  }

  void _goalsTabListeners(WidgetRef ref, BuildContext context) {
    ref.easyListen(
      checkInControllerProvider,
      handleLoading: false,
      whenData: (state) {
        if (state != null) {
          ref.read(checkInControllerProvider.notifier).resetState();
          _handleCheckInSuccess(
            context,
            ref,
            dialogWidget: CheckInSuccessDialog(
              selectedGoal: state.selectedGoal,
              awardedPoints: state.awardedPoints,
              totalPoints: state.auth.user.profile.points,
            ),
            checkInState: state,
          );
        }
      },
    );

    ref.easyListen(
      repairStreakControllerProvider,
      loadingText: "Repairing Streak...",
      whenData: (state) {
        if (state != null) {
          ref.read(repairStreakControllerProvider.notifier).resetState();
          _handleCheckInSuccess(
            context,
            ref,
            dialogWidget: RepairStreakSuccessDialog(goal: state.goal),
            repairStreakState: state,
          );
        }
      },
    );

    ref.easyListen(
      editGoalControllerProvider,
      loadingText: context.L.editGoalLoading,
      whenData: (state) {
        if (state != null) {
          ref.read(editGoalControllerProvider.notifier).resetState();
          _handleCheckInSuccess(
            context,
            ref,
            dialogWidget: state.awardedPoints.values.isNotEmpty
                ? CheckInSuccessDialog(
                    selectedGoal: state.selectedGoal,
                    awardedPoints: state.awardedPoints,
                    totalPoints: state.auth.user.profile.points,
                  )
                : null,
            checkInState: state,
          );
        }
      },
    );
  }

  void _handleCheckInSuccess(
    BuildContext context,
    WidgetRef ref, {
    Widget? dialogWidget,
    CheckIn? checkInState,
    RepairStreak? repairStreakState,
  }) {
    if (checkInState == null && repairStreakState == null) return;
    ref.read(authStateProvider.notifier).login(
        checkInState != null ? checkInState.auth : repairStreakState!.auth);
    ref.invalidate(getMonthlyTrackingProvider);
    ref.invalidate(getTodaysTrackingProvider);

    if (dialogWidget != null) {
      showDialog(context: context, builder: (context) => dialogWidget);
    }
  }
}
