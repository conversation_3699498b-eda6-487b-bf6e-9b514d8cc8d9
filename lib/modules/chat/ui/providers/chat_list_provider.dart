import 'package:neuroworld/modules/chat/data/models/chat.dart';
import 'package:neuroworld/modules/chat/data/models/chat_message.dart';
import 'package:neuroworld/modules/chat/data/models/chat_role.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'chat_list_provider.g.dart';

@riverpod
class ChatList extends _$ChatList {
  @override
  List<Chat> build() => [];
  void clear() => state = [];
  void append(String message) => state = [
        Chat(
          message: ChatMessage(text: message),
          role: ChatRole.user,
          date: DateTime.now(),
        ),
        ...state,
      ];

  void appendChat(Chat chat) => state = [chat, ...state];
  void removeLast() => state = state.sublist(0, state.length - 1);
}
