import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'stream_text_provider.g.dart';

enum CapturingState { waiting, capturing, complete }

@riverpod
class StreamText extends _$StreamText {
  @override
  String build() => "";
  CapturingState _capturingState = CapturingState.waiting;
  String _suffixBuffer = "";

  void clear() {
    state = "";
    _capturingState = CapturingState.waiting;
    _suffixBuffer = "";
  }

  void append(String s) {
    final oldSuffix = _suffixBuffer;
    final combined = oldSuffix + s;
    const textMatch = r'"text": "';
    const avatarMatch = r'"avatar": ';

    if (_capturingState == CapturingState.waiting) {
      final idx = combined.indexOf(textMatch);
      if (idx != -1) {
        _capturingState = CapturingState.capturing;
        final matchEnd = idx + textMatch.length;
        var startInS = matchEnd - oldSuffix.length;
        if (startInS < 0) startInS = 0;
        state += s.substring(startInS);
      }
    } else if (_capturingState == CapturingState.capturing) {
      final idx = combined.indexOf(avatarMatch);
      if (idx != -1) {
        // plus 4 because ,\n" appears prior to avatar key in json
        // so we should trim that as well
        if (state.length > avatarMatch.length + 4) {
          state = state.substring(0, state.length - avatarMatch.length - 4);
        }
        _capturingState = CapturingState.complete;
      } else {
        state += s;
      }
    }

    // update suffix buffer
    _suffixBuffer = combined.length <= 20
        ? combined
        : combined.substring(combined.length - 20);
  }
}
