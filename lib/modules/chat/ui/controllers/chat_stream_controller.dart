import 'dart:convert';

import 'package:http/http.dart';
import 'package:neuroworld/core/infrastructure/error/app_exceptions.dart';
import 'package:neuroworld/modules/chat/data/models/chat.dart';
import 'package:neuroworld/modules/chat/data/services/chat_service.dart';
import 'package:neuroworld/modules/chat/ui/providers/chat_list_provider.dart';
import 'package:neuroworld/modules/chat/ui/providers/stream_text_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'chat_stream_controller.g.dart';

@Riverpod(keepAlive: true)
class ChatStreamController extends _$ChatStreamController {
  @override
  FutureOr<Chat?> build() async => null;

  bool _shouldResetThread = false;

  Future<void> stream(String message, {bool newThread = false}) async {
    state = const AsyncLoading();
    ref.read(chatListProvider.notifier).append(message);
    try {
      final StreamedResponse streamResponse = await ref
          .read(chatServiceProvider)
          .sendMessage(message, newThread: _shouldResetThread || newThread);

      _shouldResetThread = false;

      final stream = streamResponse.stream
          .transform(utf8.decoder)
          .transform(const LineSplitter());

      stream.listen((line) {
        if (line.isNotEmpty) {
          final accumulatedText = ref.watch(streamTextProvider.notifier);
          try {
            if (line.startsWith('data:')) {
              Map<String, dynamic> data = jsonDecode(line.substring(6));
              bool isLastMessage = data['last_message'];
              if (!isLastMessage) {
                // chunk is string
                accumulatedText.append(data['chunk']);
              } else {
                // chunk is Chat json
                state = AsyncData(Chat.fromJson(data['chunk']));
                ref
                    .read(chatListProvider.notifier)
                    .appendChat(Chat.fromJson(data['chunk']));
                accumulatedText.clear();
              }
            } else {
              accumulatedText.clear();
              throw ChatException(type: ChatExceptionType.invalidFormat);
            }
          } catch (e, s) {
            // reset thread for next request in case myla starts to hallucinate
            _shouldResetThread = true;
            accumulatedText.clear();
            state = AsyncError(e, s);
          }
        }
      });
    } catch (e, s) {
      ref.read(chatListProvider.notifier).removeLast();
      state = AsyncError(e, s);
    }
  }
}
