import 'package:neuroworld/modules/auth/data/models/request/update_profile_request.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'accept_consent_controller.g.dart';

@Riverpod(keepAlive: true)
class AcceptConsentController extends _$AcceptConsentController {
  @override
  FutureOr<bool?> build() => null;

  Future<void> acceptConsent() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      final newAuth = await ref
          .read(authServiceProvider)
          .updateUserProfile(UpdateProfileRequest(isChatConsent: true));
      ref.read(authStateProvider.notifier).login(newAuth);
      return true;
    });
  }
}
