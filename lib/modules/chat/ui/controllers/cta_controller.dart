import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/chat/data/models/action_type.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'cta_controller.g.dart';

@Riverpod(keepAlive: true)
class CTAController extends _$CTAController {
  @override
  FutureOr<ActionType?> build() => null;

  Future<void> refresh(ActionType actionType) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      final auth = await ref.watch(authServiceProvider).refreshUser();
      ref.read(authStateProvider.notifier).login(auth);
      return actionType;
    });
  }
}
