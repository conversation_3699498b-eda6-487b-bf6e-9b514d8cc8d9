import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/chat/data/models/action_type.dart';
import 'package:neuroworld/modules/chat/data/services/chat_service.dart';
import 'package:neuroworld/modules/chat/ui/controllers/accept_consent_controller.dart';
import 'package:neuroworld/modules/chat/ui/controllers/chat_stream_controller.dart';
import 'package:neuroworld/modules/chat/ui/controllers/cta_controller.dart';
import 'package:neuroworld/modules/chat/ui/providers/chat_list_provider.dart';
import 'package:neuroworld/modules/chat/ui/widgets/chat_header.dart';
import 'package:neuroworld/modules/chat/ui/widgets/chat_input.dart';
import 'package:neuroworld/modules/chat/ui/widgets/chat_introduction.dart';
import 'package:neuroworld/modules/chat/ui/widgets/consent_card.dart';
import 'package:neuroworld/modules/chat/ui/widgets/new_chats_list.dart';
import 'package:neuroworld/modules/chat/ui/widgets/paginated_chat_list.dart';
import 'package:neuroworld/modules/chat/ui/widgets/text_streaming.dart';

class ChatScreen extends HookConsumerWidget {
  const ChatScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final inputController = useTextEditingController();
    final focusNode = useFocusNode();

    final chatList = ref.watch(chatListProvider);
    final isUserConsent =
        ref.watch(authStateProvider)?.user.profile.isChatConsent ?? false;
    final initialChats = ref.watch(getChatsProvider(1));

    final chatStreamController = ref.watch(chatStreamControllerProvider);

    ref.easyListen(acceptConsentControllerProvider);
    ref.easyListen(chatStreamControllerProvider, handleLoading: false);
    ref.easyListen(
      cTAControllerProvider,
      whenData: (data) {
        if (data == ActionType.cta) {
          GoalsRoute().go(context);
        }
        // can handle other CTA ActionTypes here,
        // for now there is only one CTA type and its for add goal
      },
    );

    List<Widget> buildChatSlivers() {
      if (!isUserConsent) {
        return [ConsentCard()];
      }

      return [
        // show introduction only if no existing chats and no new chats
        // note: no existing chat check is inside widget
        if (chatList.isEmpty)
          ChatIntroduction(
            onTapExample: (exampleText) {
              FocusScope.of(context).requestFocus(focusNode);
              inputController.text = exampleText;
            },
          ),
        // fetched old chats (paginated)
        PaginatedChatList(
          allowActions: chatList.isEmpty && !chatStreamController.isLoading,
        ),
        // new chats that user added this session
        if (chatList.isNotEmpty)
          NewChatsList(
            chatList: chatList,
            allowActions: !chatStreamController.isLoading,
          ),
        // show currently streaming prompt (if any)
        TextStreaming(),
      ];
    }

    return Scaffold(
      backgroundColor: AppColors.surfacePrimary,
      body: Column(
        children: [
          ChatHeader(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: CustomScrollView(
                reverse: true,
                slivers: [
                  SliverToBoxAdapter(child: SizedBox(height: 12)),
                  ...buildChatSlivers().reversed,
                  SliverToBoxAdapter(child: SizedBox(height: 8)),
                ],
              ),
            ),
          ),
          ChatInput(
            controller: inputController,
            focusNode: focusNode,
            enabled: isUserConsent &&
                !initialChats.isLoading &&
                !chatStreamController.isLoading,
          ),
        ],
      ),
    );
  }
}
