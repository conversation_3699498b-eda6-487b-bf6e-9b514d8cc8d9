import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/secondary_button.dart';

class ChatExampleRow extends StatelessWidget {
  const ChatExampleRow({
    super.key,
    required this.backgroundColor,
    required this.icon,
    required this.description,
    required this.onTap,
  });

  final Color backgroundColor;
  final Widget icon;
  final String description;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return SecondaryButton(
      onPressed: onTap,
      height: 54,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          children: [
            Container(
              height: 30,
              width: 30,
              padding: EdgeInsets.all(5),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: backgroundColor,
              ),
              child: icon,
            ),
            const SizedBox(width: 8),
            Text(
              description,
              style: TextStyles.body4.copyWith(
                color: AppColors.textSecondary,
              ),
            )
          ],
        ),
      ),
    );
  }
}
