import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/secondary_button.dart';
import 'package:neuroworld/modules/chat/ui/controllers/accept_consent_controller.dart';

class ConsentCard extends ConsumerWidget {
  const ConsentCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SliverToBoxAdapter(
      child: ChatBubble(
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 14, vertical: 8),
          child: Column(
            children: [
              Text(context.L.chatConsentTitle, style: TextStyles.heading5),
              const SizedBox(height: 8),
              Text(
                context.L.chatConsentBody,
                style: TextStyles.body2,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: PrimaryButton(
                      height: 40,
                      onPressed: () => ref
                          .read(
                            acceptConsentControllerProvider.notifier,
                          )
                          .acceptConsent(),
                      child: Text(
                        context.L.chatConsentAgree,
                        style: TextStyles.buttonMedium.copyWith(
                          color: Colors.white,
                          height: 0,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: SecondaryButton(
                      height: 40,
                      backgroundColor: AppColors.surfacePrimary,
                      onPressed: () => NavService.pop(context),
                      child: Text(
                        context.L.chatConsentDecline,
                        style: TextStyles.buttonMedium.copyWith(
                          color: AppColors.textSecondary,
                          height: 0,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
