import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/secondary_button.dart';

class ChatExampleColumn extends StatelessWidget {
  const ChatExampleColumn({
    super.key,
    required this.backgroundColor,
    required this.icon,
    required this.description,
    required this.onTap,
  });

  final Color backgroundColor;
  final Widget icon;
  final String description;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SecondaryButton(
        onPressed: onTap,
        child: Container(
          padding: EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 30,
                width: 30,
                padding: EdgeInsets.all(5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: backgroundColor,
                ),
                child: icon,
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: TextStyles.body4.copyWith(
                  color: AppColors.textSecondary,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
