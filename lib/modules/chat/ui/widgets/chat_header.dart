import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class ChatHeader extends StatelessWidget {
  const ChatHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(width: 0.5, color: AppColors.stroke),
        ),
      ),
      child: AppBar(
        forceMaterialTransparency: true,
        backgroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          context.L.chatScreenTitle,
          style: TextStyles.subheading1,
        ),
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Icons.close_rounded, size: 28),
        ),
      ),
    );
  }
}
