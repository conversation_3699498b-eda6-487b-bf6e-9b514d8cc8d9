import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/secondary_button.dart';
import 'package:neuroworld/modules/chat/data/models/chat.dart';
import 'package:neuroworld/modules/chat/ui/controllers/chat_stream_controller.dart';

class ButtonAction extends ConsumerWidget {
  const ButtonAction({
    super.key,
    required this.allowActions,
    required this.chat,
  });

  final bool allowActions;
  final Chat chat;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: SecondaryButton(
            onPressed: () {
              if (allowActions) {
                ref
                    .read(chatStreamControllerProvider.notifier)
                    .stream(chat.message.actions![0]);
              }
            },
            disabled: allowActions,
            backgroundColor: allowActions ? Colors.white : AppColors.stroke,
            child: Text(
              chat.message.actions![0],
              style: TextStyles.buttonMedium.copyWith(
                color: AppColors.textSecondary,
                height: 0,
              ),
            ),
          ),
        )
      ],
    );
  }
}
