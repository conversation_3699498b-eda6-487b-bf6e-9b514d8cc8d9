import 'package:flutter/material.dart';
import 'package:gpt_markdown/gpt_markdown.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/modules/chat/data/models/chat.dart';

class UserChatBubble extends StatelessWidget {
  const UserChatBubble({super.key, required this.chat});

  final Chat chat;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: ChatBubble(
        backgroundColor: AppColors.unwindOrange,
        child: GptMarkdown(
          chat.message.text,
          style: TextStyles.body2,
        ),
      ),
    );
  }
}
