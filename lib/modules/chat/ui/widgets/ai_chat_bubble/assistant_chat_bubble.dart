import 'package:flutter/material.dart';
import 'package:gpt_markdown/gpt_markdown.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/modules/chat/data/models/action_type.dart';
import 'package:neuroworld/modules/chat/data/models/avatar.dart';
import 'package:neuroworld/modules/chat/data/models/chat.dart';
import 'package:neuroworld/modules/chat/ui/widgets/ai_chat_bubble/button_action.dart';

class AssistantChatBubble extends ConsumerWidget {
  const AssistantChatBubble({
    super.key,
    required this.hasSeparateAction,
    required this.chat,
    required this.allowActions,
  });

  final bool hasSeparateAction;
  final Chat chat;
  final bool allowActions;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ChatBubbleWithAvatar(
      avatar: hasSeparateAction
          ? null
          : chat.message.avatar?.icon ?? Avatar.myla.icon,
      toolTipAlignment: hasSeparateAction ? null : Alignment.bottomLeft,
      child: Column(
        children: [
          GptMarkdown(
            chat.message.text,
            style: TextStyles.body2.copyWith(color: AppColors.textSecondary),
          ),
          if (chat.message.actionType == ActionType.button)
            ButtonAction(allowActions: allowActions, chat: chat)
        ],
      ),
    );
  }
}
