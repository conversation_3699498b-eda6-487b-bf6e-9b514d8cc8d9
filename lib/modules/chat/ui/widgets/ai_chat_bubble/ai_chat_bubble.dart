import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/modules/chat/data/models/action_type.dart';
import 'package:neuroworld/modules/chat/data/models/chat.dart';
import 'package:neuroworld/modules/chat/data/models/chat_role.dart';
import 'package:neuroworld/modules/chat/ui/widgets/ai_chat_bubble/assistant_chat_bubble.dart';
import 'package:neuroworld/modules/chat/ui/widgets/ai_chat_bubble/cta_action.dart';
import 'package:neuroworld/modules/chat/ui/widgets/ai_chat_bubble/list_action.dart';
import 'package:neuroworld/modules/chat/ui/widgets/ai_chat_bubble/user_chat_bubble.dart';

class AIChatBubble extends ConsumerWidget {
  const AIChatBubble({
    super.key,
    required this.chat,
    required this.allowActions,
  });

  final Chat chat;
  final bool allowActions;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasSeparateAction = chat.message.actionType != null &&
        chat.message.actionType != ActionType.none &&
        chat.message.actionType != ActionType.button;
    return Column(
      children: [
        switch (chat.role) {
          ChatRole.assistant => AssistantChatBubble(
              hasSeparateAction: hasSeparateAction,
              chat: chat,
              allowActions: allowActions),
          ChatRole.user => UserChatBubble(chat: chat),
        },
        if (hasSeparateAction)
          Column(
            children: [
              const SizedBox(height: 12),
              ChatBubbleWithAvatar(
                avatar: chat.message.avatar!.icon,
                toolTipAlignment: Alignment.bottomLeft,
                child: switch (chat.message.actionType) {
                  null => const SizedBox.shrink(),
                  ActionType.none => const SizedBox.shrink(),
                  ActionType.button => const SizedBox.shrink(),
                  ActionType.list =>
                    ListAction(allowActions: allowActions, chat: chat),
                  ActionType.cta => CtaAction(),
                },
              ),
            ],
          )
      ],
    );
  }
}
