import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/widgets/dialogs.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/upgrade_plan_dialog.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/chat/data/models/action_type.dart';
import 'package:neuroworld/modules/chat/ui/controllers/cta_controller.dart';

class CtaAction extends ConsumerWidget {
  const CtaAction({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PrimaryButton(
      onPressed: () async {
        // Refresh goals with CTA action
        await ref.read(cTAControllerProvider.notifier).refresh(ActionType.cta);
        final user = ref.read(authStateProvider)?.user;

        // Check if user already has max free goals
        if (user != null && user.selectedGoals.length >= 5) {
          // ignore: use_build_context_synchronously
          GoalsRoute().go(context);
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Dialogs.showCustomDialog(context, content: UpgradePlanDialog());
          });
        }
      },
      child: Text(context.L.chatCTAButton),
    );
  }
}
