import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/secondary_button.dart';
import 'package:neuroworld/modules/chat/data/models/chat.dart';
import 'package:neuroworld/modules/chat/ui/controllers/chat_stream_controller.dart';

class ListAction extends ConsumerWidget {
  const ListAction({super.key, required this.allowActions, required this.chat});

  final bool allowActions;
  final Chat chat;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView.separated(
      primary: false,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) => SecondaryButton(
        height: null,
        onPressed: () {
          if (allowActions) {
            ref
                .read(chatStreamControllerProvider.notifier)
                .stream(chat.message.actions![index]);
          }
        },
        disabled: allowActions,
        backgroundColor: allowActions ? Colors.white : AppColors.stroke,
        child: Text(
          chat.message.actions![index],
          textAlign: TextAlign.center,
          style: TextStyles.buttonMedium.copyWith(
            color: AppColors.textSecondary,
            height: 0,
          ),
        ),
      ),
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemCount: chat.message.actions!.length,
    );
  }
}
