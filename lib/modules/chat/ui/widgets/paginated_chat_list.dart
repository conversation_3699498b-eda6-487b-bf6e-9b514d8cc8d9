import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/app_constants.dart';
import 'package:neuroworld/modules/chat/data/services/chat_service.dart';
import 'package:neuroworld/modules/chat/ui/widgets/ai_chat_bubble/ai_chat_bubble.dart';
import 'package:neuroworld/modules/chat/ui/widgets/loading_skeleton.dart';

class PaginatedChatList extends ConsumerWidget {
  const PaginatedChatList({super.key, required this.allowActions});

  final bool allowActions;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final page = index ~/ (AppConstants.chatsPageSize * 2) + 1;
          final res = ref.watch(getChatsProvider(page));

          return res.when(
            error: (err, stack) => const SizedBox.shrink(),
            loading: () {
              if ((index ~/ 2) > AppConstants.chatsPageSize * page) {
                return null;
              }

              return LoadingSkeleton(index: index);
            },
            data: (response) {
              final indexInPage = (index ~/ 2) % AppConstants.chatsPageSize;
              if (indexInPage >= response.results.length) {
                return null;
              }
              // access in reverse because backend doesnt send us reversed list
              final chat =
                  response.results[response.results.length - 1 - indexInPage];
              return index.isOdd
                  ? const SizedBox(height: 12)
                  : AIChatBubble(
                      chat: chat,
                      allowActions: index == 0 && allowActions,
                    );
            },
          );
        },
      ),
    );
  }
}
