import 'dart:math';

import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:shimmer/shimmer.dart';

class LoadingSkeleton extends StatelessWidget {
  const LoadingSkeleton({super.key, required this.index});

  final int index;

  @override
  Widget build(BuildContext context) {
    if (index.isOdd) {
      return const SizedBox(height: 12);
    }
    final isLeft = (index ~/ 2) % 2 == 0;
    final randomHeight = Random().nextInt(100) + 50;

    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ),
      child: Row(
        mainAxisAlignment:
            isLeft ? MainAxisAlignment.start : MainAxisAlignment.end,
        children: [
          if (isLeft)
            Shimmer.fromColors(
              baseColor: AppColors.chatBubble,
              highlightColor: AppColors.unwindBlue,
              child: const CircleAvatar(
                radius: 25,
              ),
            ),
          if (isLeft) const SizedBox(width: 8),
          Shimmer.fromColors(
            baseColor: isLeft ? AppColors.chatBubble : AppColors.unwindOrange,
            highlightColor: isLeft ? AppColors.unwindBlue : Colors.white,
            child: Container(
              width: MediaQuery.of(context).size.width * 0.6,
              height: isLeft ? randomHeight.toDouble() : randomHeight / 2,
              decoration: BoxDecoration(
                color: isLeft ? AppColors.chatBubble : AppColors.unwindOrange,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
