import 'package:flutter/material.dart';
import 'package:gpt_markdown/gpt_markdown.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/modules/chat/data/models/avatar.dart';
import 'package:neuroworld/modules/chat/ui/controllers/chat_stream_controller.dart';
import 'package:neuroworld/modules/chat/ui/providers/chat_list_provider.dart';
import 'package:neuroworld/modules/chat/ui/providers/stream_text_provider.dart';

class TextStreaming extends ConsumerWidget {
  const TextStreaming({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chatStreamController = ref.watch(chatStreamControllerProvider);
    final chatList = ref.watch(chatListProvider);
    final streamText = ref.watch(streamTextProvider);

    return SliverToBoxAdapter(
      child: !chatStreamController.isLoading && streamText.isEmpty
          ? const SizedBox.shrink()
          : Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (chatStreamController.isLoading)
                    chatList.length > 1 && chatList[1].message.avatar != null
                        ? chatList[1].message.avatar!.loadingWidget
                        : Avatar.myla.loadingWidget,
                  if (chatStreamController.isLoading) const SizedBox(width: 12),
                  Flexible(
                    child: ChatBubble(
                      toolTipAlignment: Alignment.bottomLeft,
                      child: streamText.isEmpty
                          ? Text(
                              "...",
                              style: TextStyles.heading3
                                  .copyWith(color: AppColors.textTertiary),
                            )
                          : GptMarkdown(
                              streamText,
                              style: TextStyles.body2.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
