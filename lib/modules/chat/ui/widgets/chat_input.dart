import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/input_decorations.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/chat/ui/controllers/chat_stream_controller.dart';

class ChatInput extends HookConsumerWidget {
  const ChatInput({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.enabled,
  });

  final TextEditingController controller;
  final FocusNode focusNode;
  final bool enabled;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useListenable(controller);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(width: 0.5, color: AppColors.stroke),
        ),
      ),
      padding: EdgeInsets.fromLTRB(
        16,
        16,
        16,
        12 + MediaQuery.of(context).padding.bottom,
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              focusNode: focusNode,
              controller: controller,
              style: TextStyles.inputValue,
              cursorColor: AppColors.textPrimary,
              onSubmitted: (value) {
                if (enabled && value.isNotEmpty) {
                  controller.clear();
                  ref.read(chatStreamControllerProvider.notifier).stream(value);
                }
              },
              cursorWidth: 1.5,
              cursorRadius: Radius.circular(10),
              textInputAction: TextInputAction.send,
              textCapitalization: TextCapitalization.sentences,
              maxLines: 2,
              minLines: 1,
              enabled: enabled,
              decoration: InputDecorations.mylaChatInputDecoration.copyWith(
                hintText: context.L.chatInputPlaceholder,
                suffixIcon: GestureDetector(
                  onTap: () {
                    if (enabled && controller.text.isNotEmpty) {
                      FocusManager.instance.primaryFocus?.unfocus();
                      ref
                          .read(chatStreamControllerProvider.notifier)
                          .stream(controller.text);
                      controller.clear();
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      gradient: enabled && controller.text.isNotEmpty
                          ? LinearGradient(
                              colors: [
                                AppColors.orangeGradientLight,
                                AppColors.orangeGradientDark
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            )
                          : null,
                      color: AppColors.sendButtonDisabled,
                    ),
                    margin: const EdgeInsets.fromLTRB(2, 6, 10, 6),
                    padding: const EdgeInsets.fromLTRB(6, 6, 8, 6),
                    height: 32,
                    width: 32,
                    child: Assets.svgs.chat.paperPlane.svg(),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
