import 'package:flutter/material.dart';
import 'package:neuroworld/modules/chat/data/models/chat.dart';
import 'package:neuroworld/modules/chat/ui/widgets/ai_chat_bubble/ai_chat_bubble.dart';

class NewChatsList extends StatelessWidget {
  const NewChatsList({
    super.key,
    required this.chatList,
    required this.allowActions,
  });

  final List<Chat> chatList;
  final bool allowActions;

  @override
  Widget build(BuildContext context) {
    return SliverPadding(
      padding: const EdgeInsets.only(top: 12),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index.isEven) {
              final chat = chatList[index ~/ 2];
              return AIChatBubble(
                chat: chat,
                allowActions: index == 0 && allowActions,
              );
            } else {
              return const SizedBox(height: 12);
            }
          },
          childCount: chatList.length * 2 - 1,
        ),
      ),
    );
  }
}
