import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/neuro.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/modules/chat/data/services/chat_service.dart';
import 'package:neuroworld/modules/chat/ui/widgets/chat_example_column.dart';
import 'package:neuroworld/modules/chat/ui/widgets/chat_example_row.dart';

class ChatIntroduction extends ConsumerWidget {
  const ChatIntroduction({super.key, required this.onTapExample});

  final void Function(String) onTapExample;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final initialChats = ref.watch(getChatsProvider(1));
    return SliverToBoxAdapter(
      child: initialChats.when(
        data: (data) {
          // show introduction only if no existing chats and no new chats
          if (data.results.isEmpty) {
            return Column(
              children: [
                Assets.svgs.avatars.mylaWave.svg(width: 75),
                const SizedBox(height: 16),
                Text(
                  context.L.chatMylaGreeting,
                  textAlign: TextAlign.center,
                  style: TextStyles.heading4,
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    ChatExampleColumn(
                      description: context.L.chatExamplePrompt1,
                      backgroundColor: Neuro.nutrition.color.withAlpha(25),
                      icon: Assets.svgs.chat.nutritionExample.svg(),
                      onTap: () => onTapExample(context.L.chatExamplePrompt1),
                    ),
                    const SizedBox(width: 16),
                    ChatExampleColumn(
                      description: context.L.chatExamplePrompt2,
                      backgroundColor: Neuro.exercise.color.withAlpha(25),
                      icon: Assets.svgs.chat.exerciseExample.svg(),
                      onTap: () => onTapExample(context.L.chatExamplePrompt2),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ChatExampleRow(
                  description: context.L.chatExamplePrompt3,
                  backgroundColor: Neuro.optimize.color.withAlpha(25),
                  icon: Assets.svgs.chat.optimizeExample.svg(),
                  onTap: () => onTapExample(context.L.chatExamplePrompt3),
                ),
                const SizedBox(height: 16),
                ChatBubble(
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: context.L.chatDisclaimerNote,
                                style: TextStyles.body2.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              TextSpan(
                                text: context.L.chatDisclaimer,
                                style: TextStyles.body2,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 64),
              ],
            );
          }
          return SizedBox.shrink();
        },
        loading: () => SizedBox.shrink(),
        error: (_, __) => SizedBox.shrink(),
      ),
    );
  }
}
