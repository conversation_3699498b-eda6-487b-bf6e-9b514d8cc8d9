import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/chat/data/models/action_type.dart';

/// converts actiontype string to actiontype enum
class ActionTypeConverter implements JsonConverter<ActionType, String> {
  const ActionTypeConverter();

  @override
  ActionType fromJson(String s) => ActionType.byName(s);

  @override
  String toJson(ActionType n) => n.name;
}
