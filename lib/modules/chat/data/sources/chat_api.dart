import 'package:dio/dio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/data/sources/network/api/api.dart';
import 'package:neuroworld/core/data/sources/network/api/api_endpoints.dart';
import 'package:neuroworld/modules/chat/data/models/request/chat_request.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'chat_api.g.dart';

@Riverpod(keepAlive: true)
ChatApi chatApi(Ref ref) => ChatApi(api: ref.watch(apiProvider));

class ChatApi {
  ChatApi({required this.api});

  final Api api;

  Future<Response<dynamic>> getChats(ChatRequest request) => api.get(
        path: ApiEndpoints.chats,
        queryParameters: request.toJson(),
      );
}
