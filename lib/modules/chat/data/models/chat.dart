import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/core/data/converters/date_converter.dart';
import 'package:neuroworld/modules/chat/data/converters/chat_role_converter.dart';
import 'package:neuroworld/modules/chat/data/models/chat_message.dart';
import 'package:neuroworld/modules/chat/data/models/chat_role.dart';

part 'chat.freezed.dart';
part 'chat.g.dart';

@freezed
abstract class Chat with _$Chat {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory Chat({
    String? id,
    required ChatMessage message,
    @ChatRoleConverter() required ChatRole role,
    @DateConverter() required DateTime date,
  }) = _Chat;

  factory Chat.fromJson(Map<String, dynamic> json) => _$ChatFromJson(json);
}
