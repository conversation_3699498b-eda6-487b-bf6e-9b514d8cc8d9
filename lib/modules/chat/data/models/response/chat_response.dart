import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/chat/data/models/chat.dart';

part 'chat_response.freezed.dart';
part 'chat_response.g.dart';

@freezed
abstract class ChatResponse with _$ChatResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory ChatResponse({
    required int count,
    required List<Chat> results,
  }) = _ChatResponse;

  factory ChatResponse.fromJson(Map<String, dynamic> json) =>
      _$ChatResponseFromJson(json);
}
