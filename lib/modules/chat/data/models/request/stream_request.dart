import 'package:freezed_annotation/freezed_annotation.dart';

part 'stream_request.freezed.dart';
part 'stream_request.g.dart';

@freezed
abstract class StreamRequest with _$StreamRequest {
  @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
  const factory StreamRequest({
    required String message,
    bool? newThread,
  }) = _StreamRequest;

  factory StreamRequest.fromJson(Map<String, dynamic> json) =>
      _$StreamRequestFromJson(json);
}
