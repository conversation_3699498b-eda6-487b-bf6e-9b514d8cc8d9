import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/chat/data/converters/action_type_converter.dart';
import 'package:neuroworld/modules/chat/data/converters/avatar_converter.dart';
import 'package:neuroworld/modules/chat/data/models/action_type.dart';
import 'package:neuroworld/modules/chat/data/models/avatar.dart';

part 'chat_message.freezed.dart';
part 'chat_message.g.dart';

@freezed
abstract class ChatMessage with _$ChatMessage {
  @JsonSerializable()
  const factory ChatMessage({
    required String text,
    @AvatarConverter() Avatar? avatar,
    @ActionTypeConverter() ActionType? actionType,
    List<String>? actions,
  }) = _ChatMessage;

  factory ChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageFromJson(json);
}
