import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';

enum Avatar {
  myla('My<PERSON>'),
  nuri('<PERSON><PERSON>'),
  spark('Spark'),
  flo('<PERSON>lo'),
  luna('<PERSON>'),
  sophi('So<PERSON>');

  const Avatar(this.name);

  final String name;

  static Avatar byName(String name) =>
      Avatar.values.firstWhere((award) => award.name == name);

  Widget get icon => SvgPicture.asset(
        _getCharacterIconPath(),
      );

  Widget get loadingWidget => switch (this) {
        Avatar.myla => Assets.lottie.mylaThinking.lottie(
            frameRate: FrameRate.max,
            width: 56,
          ),
        Avatar.nuri => Assets.lottie.nuriThinking.lottie(
            frameRate: FrameRate.max,
            width: 63,
          ),
        Avatar.spark => Padding(
            padding: const EdgeInsets.only(left: 10),
            child: Assets.lottie.sparkThinking.lottie(
              frameRate: FrameRate.max,
              width: 38,
            ),
          ),
        Avatar.flo => Assets.lottie.floThinking.lottie(
            frameRate: FrameRate.max,
            width: 60,
          ),
        Avatar.luna => Assets.lottie.lunaThinking.lottie(
            frameRate: FrameRate.max,
            width: 60,
          ),
        Avatar.sophi => Assets.lottie.sophiThinking.lottie(
            frameRate: FrameRate.max,
            width: 60,
          ),
      };

  String _getCharacterIconPath() {
    return switch (this) {
      Avatar.myla => Assets.svgs.avatars.mylaWave.path,
      Avatar.nuri => Assets.svgs.avatars.nuri.path,
      Avatar.spark => Assets.svgs.avatars.spark.path,
      Avatar.luna => Assets.svgs.avatars.luna.path,
      Avatar.flo => Assets.svgs.avatars.flo.path,
      Avatar.sophi => Assets.svgs.avatars.sophi.path,
    };
  }
}
