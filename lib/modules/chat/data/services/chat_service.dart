import 'dart:convert';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:http/http.dart';
import 'package:neuroworld/core/config/api_config.dart';
import 'package:neuroworld/core/constants/app_constants.dart';
import 'package:neuroworld/core/data/sources/network/api/api_endpoints.dart';
import 'package:neuroworld/core/infrastructure/error/app_exceptions.dart';
import 'package:neuroworld/modules/auth/data/models/response/auth.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/chat/data/models/request/chat_request.dart';
import 'package:neuroworld/modules/chat/data/models/request/stream_request.dart';
import 'package:neuroworld/modules/chat/data/models/response/chat_response.dart';
import 'package:neuroworld/modules/chat/data/sources/chat_api.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'chat_service.g.dart';

@Riverpod(keepAlive: true)
ChatService chatService(Ref ref) => ChatService(
      chatApi: ref.watch(chatApiProvider),
      auth: ref.watch(authStateProvider),
    );

class ChatService {
  ChatService({required this.chatApi, required this.auth});

  final ChatApi chatApi;
  final Auth? auth;

  Future<StreamedResponse> sendMessage(
    String message, {
    bool newThread = false,
  }) async {
    if (auth == null) {
      throw ServerException(
        type: ServerExceptionType.unauthorized,
        message: "Unauthorized",
      );
    }
    final request =
        Request('POST', Uri.parse(ApiConfig.baseUrl + ApiEndpoints.stream))
          ..headers['Content-Type'] = 'application/json'
          // ..headers['Accept'] = 'text/event-stream'
          ..headers['Authorization'] = 'Bearer ${auth!.access}'
          ..body = jsonEncode(
            StreamRequest(message: message, newThread: newThread).toJson(),
          );

    return await request.send();
  }

  Future<ChatResponse> getChats(int page, int pageSize) async {
    final res =
        await chatApi.getChats(ChatRequest(page: page, pageSize: pageSize));
    return ChatResponse.fromJson(res.data);
  }
}

// chat pagination provider
@riverpod
Future<ChatResponse> getChats(Ref ref, int page) =>
    ref.watch(chatServiceProvider).getChats(page, AppConstants.chatsPageSize);
