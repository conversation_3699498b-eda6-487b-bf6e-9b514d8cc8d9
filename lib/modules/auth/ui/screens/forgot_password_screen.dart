import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/constants/gen/fonts.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/input_decorations.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/labeled_input_field.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/toasts.dart';
import 'package:neuroworld/modules/auth/ui/controllers/forgot_password_controller.dart';
import 'package:neuroworld/modules/auth/ui/controllers/forgot_password_form_controller.dart';

class ForgotPasswordScreen extends HookConsumerWidget {
  const ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.easyListen(
      forgotPasswordControllerProvider,
      whenData: (data) {
        if (data != null) {
          // need to send user to landing screen
          Toasts.showSuccessToast(context, data.message);
        }
      },
    );

    final form = ref.watch(forgotPasswordFormControllerProvider);

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size(0, 90),
        child: Padding(
          padding: const EdgeInsets.only(top: 0),
          child: AppBar(
            leading: IconButton(
              icon: Assets.svgs.backArrow.svg(),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.transparent,
          ),
        ),
      ),
      extendBodyBehindAppBar: true,
      body: CustomScrollView(
        slivers: [
          SliverFillRemaining(
            hasScrollBody: false,
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: Assets.images.bg.provider(),
                  fit: BoxFit.cover,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.only(left: 30, right: 30, top: 120),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.L.forgotPasswordScreenTitle,
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 26,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      context.L.forgotPasswordScreenSubtitle,
                      style: TextStyle(
                        fontFamily: FontFamily.sFProText,
                        fontWeight: FontWeight.w400,
                        fontSize: 18,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 34),
                    LabeledInputField(
                      label: context.L.emailFieldLabel,
                      inputField: TextFormField(
                        autovalidateMode: AutovalidateMode.onUnfocus,
                        onChanged: (value) => ref
                            .read(forgotPasswordFormControllerProvider.notifier)
                            .email = value,
                        validator: (value) =>
                            form.email.validator(value ?? '')?.message(context),
                        style: TextStyles.inputValue,
                        cursorColor: Colors.black,
                        textInputAction: TextInputAction.next,
                        keyboardType: TextInputType.emailAddress,
                        enabled: !form.status.isInProgressOrSuccess,
                        decoration:
                            InputDecorations.textFieldDecoration.copyWith(
                          hintText: context.L.signupEmailPlaceholder,
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),
                    SizedBox(
                      width: double.infinity,
                      child: PrimaryButton(
                        disabled: form.isNotValid,
                        onPressed: () => ref
                            .read(forgotPasswordControllerProvider.notifier)
                            .forgotPassword(form.email.value),
                        child: Text(context.L.submitButton),
                      ),
                    ),
                    const SizedBox(height: 10),
                    Align(
                      alignment: Alignment.center,
                      child: TextButton(
                        onPressed: () => context.pop(),
                        style: TextButton.styleFrom(
                            padding: EdgeInsets.symmetric(horizontal: 30)),
                        child: Text(
                          context.L.backToLoginButton,
                          style: TextStyle(
                            color: AppColors.secondaryBlue,
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            height: 18 / 15,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
