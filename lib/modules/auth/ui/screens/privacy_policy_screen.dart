import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(statusBarColor: Colors.white),
    );

    return PopScope(
      onPopInvokedWithResult: (_, __) => SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(statusBarColor: Colors.transparent),
      ),
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: const Size(0, 80),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 24),
            child: AppBar(
              title: Text(
                context.L.privacyPolicyTitle,
                style: TextStyles.subheading2,
              ),
              bottom: const PreferredSize(
                preferredSize: Size.fromHeight(0.5),
                child: Divider(color: AppColors.stroke, height: 0.5),
              ),
              centerTitle: true,
              leading: IconButton(
                icon: Assets.svgs.backArrow.svg(),
                onPressed: () => context.pop(),
              ),
              backgroundColor: Colors.white,
              scrolledUnderElevation: 0,
            ),
          ),
        ),
        extendBodyBehindAppBar: true,
        body: Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              image: Assets.images.bg.provider(),
              fit: BoxFit.cover,
            ),
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.only(
                left: 30, right: 30, top: 140, bottom: 30),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _sectionTitle("Effective Date: February 5, 2025"),
                _sectionTitle("Last Updated: February 5, 2025"),
                _sectionTitle("Company Name: Brain Initiative Inc."),
                _sectionHeader("1. Introduction"),
                _sectionBody(
                    "Brain Initiative Inc. understands how important your privacy is. This Privacy Policy describes how we collect, use, disclose, and protect your personal information when you use the NEURO World Application (\"Application\") and related services.\n\nBy using the Application, you agree to this Privacy Policy. If you do not agree with this policy, please do not use the Application.\n\nThis Privacy Policy applies to:\n\n• The NEURO World Application\n• The Brain Initiative Inc. website and its mobile versions\n• All services and content provided through these platforms"),
                _sectionHeader("2. Information We Collect"),
                _sectionSubHeader("2.1 Information You Provide to Us"),
                _bulletList([
                  "Account Information: Name, email, phone number, date of birth, gender, and password.",
                  "Profile Information: Health goals, interests, location, and any additional personal details you choose to share.",
                  "User-Generated Content: Posts, comments, reviews, or participation in interactive features.",
                  "Subscription and Payment Information: If you subscribe to premium services, payment details are collected via third-party payment processors."
                ]),
                _sectionSubHeader("2.2 Information Collected Automatically"),
                _bulletList([
                  "Usage Data: Features accessed, time spent, and interactions within the Application.",
                  "Device Information: Device type, operating system, browser, IP address, and unique device identifiers.",
                  "Location Information: If enabled, we collect geolocation data for personalized experiences. This can be disabled in your device settings."
                ]),
                _sectionSubHeader("2.3 Information from Third Parties"),
                _bulletList([
                  "Social Media Logins: If you connect via Google or Apple, we collect limited profile details.",
                  "Analytics Providers: To analyze user behavior, engagement, and service improvements."
                ]),
                _sectionHeader("3. How We Use Your Information"),
                _bulletList([
                  "Providing, personalizing, and improving the NEURO World Application.",
                  "Authenticating users and securing accounts.",
                  "Offering tailored health insights and recommendations.",
                  "Processing payments and managing subscriptions.",
                  "Sending important updates, newsletters, and promotional offers.",
                  "Monitoring for security threats, fraud prevention, and legal compliance."
                ]),
                _sectionHeader("4. How We Share Your Information"),
                _sectionSubHeader("4.1 With Service Providers"),
                _sectionBody(
                    "We share data with third-party vendors that assist in payment processing, analytics, and customer support."),
                _sectionSubHeader("4.2 With Other Users"),
                _sectionBody(
                    "If you participate in public community features, your username and shared content may be visible to others."),
                _sectionSubHeader("4.3 Legal Compliance"),
                _sectionBody(
                    "We may disclose data when required by law enforcement, court orders, or government authorities."),
                _sectionSubHeader("4.4 Business Transfers"),
                _sectionBody(
                    "If Brain Initiative Inc. is acquired or undergoes a merger, your data may be transferred to the new entity."),
                _sectionHeader("5. Your Choices & Control Over Your Data"),
                _sectionSubHeader("5.1 Managing Your Information"),
                _bulletList([
                  "You can update or delete your profile data in the Application settings.",
                  "You can request data deletion <NAME_EMAIL>."
                ]),
                _sectionSubHeader("5.2 Opting Out of Communications"),
                _bulletList([
                  "Unsubscribe from marketing emails using the opt-out link in our emails.",
                  "Essential service-related notifications will still be sent."
                ]),
                _sectionSubHeader("5.3 Location & Tracking Controls"),
                _bulletList([
                  "Disable location tracking in device settings.",
                  "Clear cookies and tracking data in your browser."
                ]),
                _sectionSubHeader("5.4 Data Portability & Deletion"),
                _bulletList([
                  "You may request a copy of your data.",
                  "Data deletion requests are subject to legal retention requirements."
                ]),
                _sectionHeader("6. Data Security"),
                _bulletList([
                  "Encryption for sensitive data.",
                  "Access controls to limit unauthorized access.",
                  "Regular security audits to monitor system vulnerabilities."
                ]),
                _sectionBody(
                    "While we take security seriously, no system is completely foolproof. Users are encouraged to use strong passwords and secure their accounts."),
                _sectionHeader("7. Data Retention"),
                _sectionBody(
                    "Your personal data is stored as long as your account is active or required for legal and business purposes. Once no longer needed, it is deleted or anonymized."),
                _sectionHeader("8. Third-Party Links and Services"),
                _sectionBody(
                    "The Application may contain links to third-party services. This Privacy Policy does not apply to external sites or applications. Please review their privacy policies before sharing data."),
                _sectionHeader("9. International Data Transfers"),
                _sectionBody(
                    "If you access the Application from outside the United States, your data may be transferred and processed in the U.S., where different privacy laws apply. By using the Application, you consent to this transfer."),
                _sectionHeader("10. Children's Privacy"),
                _sectionBody(
                    "The Application is not intended for children under 13. If we learn that we have collected data from a child under 13, we will delete it promptly."),
                _sectionHeader("11. Cookies & Tracking Technologies"),
                _bulletList([
                  "Enhance your experience.",
                  "Track usage and engagement.",
                  "Deliver personalized ads (if applicable)."
                ]),
                _sectionBody(
                    "You can manage cookie preferences in your browser settings."),
                _sectionHeader("12. Your Rights Under Applicable Laws"),
                _sectionSubHeader("12.1 GDPR (European Users)"),
                _bulletList([
                  "Right to access, correct, or delete your data.",
                  "Right to object to data processing.",
                  "Right to withdraw consent at any time."
                ]),
                _sectionSubHeader("12.2 CCPA (California Users)"),
                _bulletList([
                  "Right to request information on data collection and usage.",
                  "Right to opt-out of data sharing.",
                  "Right to request data deletion."
                ]),
                _sectionBody(
                    "To exercise these rights, contact <EMAIL>."),
                _sectionHeader("13. Updates to This Privacy Policy"),
                _sectionBody(
                    "We may update this Privacy Policy periodically. Users will be notified of major changes via email or in-app notifications. Continued use of the Application after updates constitutes acceptance of the new policy."),
                _sectionHeader("14. Contact Information"),
                _bulletList([
                  "Email: <EMAIL>",
                  "Address: Brain Initiative Inc., Redondo Beach, CA, USA"
                ]),
                _sectionBody(
                    "If you have unresolved privacy concerns, EEA users may contact their local data protection authority."),
                _sectionHeader("15. Final Acknowledgment"),
                _sectionBody(
                    "By using the NEURO World Application, you acknowledge that you have read and understood this Privacy Policy and agree to its terms."),
                _sectionTitle("Last updated: February 5, 2025"),
                _sectionTitle(
                    "© 2025 Brain Initiative Inc. All rights reserved."),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _sectionTitle(String text) => Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Text(text, style: TextStyles.heading5),
      );

  Widget _sectionHeader(String text) => Padding(
        padding: const EdgeInsets.only(top: 16, bottom: 12),
        child: Text(text, style: TextStyles.heading3),
      );

  Widget _sectionSubHeader(String text) => Padding(
        padding: const EdgeInsets.only(bottom: 12, top: 12),
        child: Text(text, style: TextStyles.heading4),
      );

  Widget _sectionBody(String text) => Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: Text(text, style: TextStyles.body3),
      );

  Widget _bulletList(List<String> items) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: items
            .map((item) => Padding(
                  padding: const EdgeInsets.only(bottom: 6),
                  child: Text("• $item", style: TextStyles.body3),
                ))
            .toList(),
      );
}
