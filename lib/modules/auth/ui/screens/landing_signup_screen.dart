import 'dart:io' show Platform;

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/auth/ui/controllers/auth_controller.dart';
import 'package:neuroworld/modules/auth/ui/widgets/bottom_signin.dart';
import 'package:neuroworld/modules/auth/ui/widgets/sso_button.dart';

class LandingSignupScreen extends ConsumerWidget {
  const LandingSignupScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.easyListen(
      authControllerProvider,
      loadingText: context.L.ssoLoading,
      whenData: (state) {
        if (state != null) {
          if (state.profile.isOnboardingCompleted) {
            const HomeRoute().go(context);
          } else {
            const OnboardingRoute().go(context);
          }
        }
      },
    );

    return Scaffold(
        body: Stack(
      children: [
        CustomScrollView(
          slivers: [
            SliverFillRemaining(
              hasScrollBody: false,
              child: Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: Assets.images.bg.provider(),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    children: [
                      const SizedBox(height: 100),
                      Assets.svgs.logo.svg(height: 55),
                      const SizedBox(height: 28),
                      Text(
                        context.L.landingSignupScreenTitle,
                        textAlign: TextAlign.center,
                        style: TextStyles.heading4,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        context.L.landingSignupScreenSubtitle,
                        textAlign: TextAlign.center,
                        style: TextStyles.body1.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      SizedBox(height: Platform.isIOS ? 45 : 80),
                      if (Platform.isIOS)
                        SSOButton(
                          buttonType: SSOButtonType.signupApple,
                          onPressed: () => ref
                              .read(authControllerProvider.notifier)
                              .appleAuth(),
                        ),
                      if (Platform.isIOS) const SizedBox(height: 16),
                      SSOButton(
                        buttonType: SSOButtonType.signupGoogle,
                        onPressed: () => ref
                            .read(authControllerProvider.notifier)
                            .googleAuth(),
                      ),
                      const SizedBox(height: 16),
                      SSOButton(
                        buttonType: SSOButtonType.signupEmail,
                        onPressed: () => context.push(SignupRoute().location),
                      ),
                      const SizedBox(height: 20),
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          style: TextStyles.body4.copyWith(fontSize: 16),
                          children: [
                            TextSpan(
                              text: context.L.signInPrefix,
                            ),
                            TextSpan(
                              text: const String.fromEnvironment('APP_NAME'),
                              style:
                                  TextStyles.subheading2.copyWith(fontSize: 16),
                            ),
                            TextSpan(
                              text: context.L.signInMiddle,
                            ),
                            TextSpan(
                              text: context.L.termsLink,
                              style: TextStyles.subheading2.copyWith(
                                  fontSize: 16, color: AppColors.secondaryBlue),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  context
                                      .push(TermsAndConditionsRoute().location);
                                },
                            ),
                            TextSpan(
                              text: context.L.signInBeforePrivacy,
                            ),
                            TextSpan(
                              text: context.L.privacyLink,
                              style: TextStyles.subheading2.copyWith(
                                  fontSize: 16, color: AppColors.secondaryBlue),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  context.push(PrivacyPolicyRoute().location);
                                },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        BottomSignIn(
          onSignUpTap: () => context.pop(),
        ),
      ],
    ));
  }
}
