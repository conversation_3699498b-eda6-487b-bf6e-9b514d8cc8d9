import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/input_decorations.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/labeled_input_field.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/toasts.dart';
import 'package:neuroworld/modules/auth/ui/controllers/reset_password_controller.dart';
import 'package:neuroworld/modules/auth/ui/controllers/reset_password_form_controller.dart';

class ResetPasswordScreen extends HookConsumerWidget {
  final String token;
  const ResetPasswordScreen({super.key, required this.token});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.easyListen(
      resetPasswordControllerProvider,
      whenData: (data) {
        if (data != null) {
          context.go(LoginRoute().location);
          Toasts.showSuccessToast(context, data.message);
        }
      },
    );

    final form = ref.watch(resetPasswordFormControllerProvider);

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: CustomScrollView(
        slivers: [
          SliverFillRemaining(
            hasScrollBody: false,
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: Assets.images.bg.provider(),
                  fit: BoxFit.cover,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Form(
                  key: form.key,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 120),
                      Text(
                        context.L.resetPasswordScreenTitle,
                        style: TextStyles.heading3,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        context.L.resetPasswordScreenSubTitle,
                        style: TextStyles.body1.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 32),
                      LabeledInputField(
                        label: context.L.newPassword,
                        inputField: TextFormField(
                          autovalidateMode: AutovalidateMode.onUnfocus,
                          onChanged: (value) => ref
                              .read(
                                  resetPasswordFormControllerProvider.notifier)
                              .newPassword = value,
                          validator: (value) => form.newPassword
                              .validator(value ?? '')
                              ?.message(context),
                          style: TextStyles.inputValue.copyWith(
                            color: AppColors.textPrimary,
                          ),
                          cursorColor: Colors.black,
                          textInputAction: TextInputAction.done,
                          enabled: !form.status.isInProgressOrSuccess,
                          decoration:
                              InputDecorations.textFieldDecoration.copyWith(
                            hintText: context.L.newPasswordPlaceholder,
                            isDense: true,
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 12),
                            errorMaxLines: 3,
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),
                      LabeledInputField(
                        label: context.L.confirmPassword,
                        inputField: TextFormField(
                          autovalidateMode: AutovalidateMode.onUnfocus,
                          onChanged: (value) => ref
                              .read(
                                  resetPasswordFormControllerProvider.notifier)
                              .confirmPassword = value,
                          validator: (value) => form.confirmPassword
                              .validator(value ?? '')
                              ?.message(context),
                          style: TextStyles.inputValue.copyWith(
                            color: AppColors.textPrimary,
                          ),
                          cursorColor: Colors.black,
                          textInputAction: TextInputAction.done,
                          enabled: !form.status.isInProgressOrSuccess,
                          decoration:
                              InputDecorations.textFieldDecoration.copyWith(
                            hintText: context.L.confirmPasswordPlaceholder,
                            isDense: true,
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 12),
                            errorMaxLines: 3,
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),
                      SizedBox(
                        width: double.infinity,
                        child: PrimaryButton(
                          // onPressed: () => ref
                          //     .read(resetPasswordControllerProvider.notifier)
                          //     .resetPassword(form.newPassword.value, token),
                          onPressed: () async {
                            await ref
                                .read(resetPasswordFormControllerProvider
                                    .notifier)
                                .submit(token);
                            // ignore: use_build_context_synchronously
                            LoginRoute().go(context);
                          },
                          disabled: form.isNotValid,
                          child: form.status.isInProgressOrSuccess
                              ? SizedBox(
                                  height: 26,
                                  width: 26,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(context.L.resetPasswordScreenTitle),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
