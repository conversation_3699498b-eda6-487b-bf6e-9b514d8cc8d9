import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:formz/formz.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/app_error_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/input_decorations.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/labeled_input_field.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/toasts.dart';
import 'package:neuroworld/modules/auth/data/forms/login_form.dart';
import 'package:neuroworld/modules/auth/ui/controllers/login_form_controller.dart';

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPasswordHidden = useState(true);

    ref.listen<LoginForm>(loginFormControllerProvider, (_, state) {
      switch (state.status) {
        case FormzSubmissionStatus.failure:
          Toasts.showErrorToast(
            context,
            message:
                state.error?.errorMessage(context) ?? context.L.unknownError,
          );
          ref.read(loginFormControllerProvider.notifier).resetStatus();
        default:
          break;
      }
    });

    final form = ref.watch(loginFormControllerProvider);

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size(0, 90),
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: AppBar(
            scrolledUnderElevation: 0,
            leading: IconButton(
              icon: Assets.svgs.backArrow.svg(),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.transparent,
          ),
        ),
      ),
      extendBodyBehindAppBar: true,
      body: CustomScrollView(
        slivers: [
          SliverFillRemaining(
            hasScrollBody: false,
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: Assets.images.bg.provider(),
                  fit: BoxFit.cover,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Form(
                  key: form.key,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 150),
                      Text(
                        context.L.loginScreenTitle,
                        style: TextStyles.heading3,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        context.L.loginScreenSubtitle,
                        style: TextStyles.body1.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 40),
                      LabeledInputField(
                        label: context.L.emailFieldLabel,
                        inputField: TextFormField(
                          autovalidateMode: AutovalidateMode.onUnfocus,
                          onChanged: (value) => ref
                              .read(loginFormControllerProvider.notifier)
                              .email = value,
                          validator: (value) => form.email
                              .validator(value ?? '')
                              ?.message(context),
                          style: TextStyles.inputValue,
                          cursorColor: Colors.black,
                          textInputAction: TextInputAction.next,
                          keyboardType: TextInputType.emailAddress,
                          enabled: !form.status.isInProgressOrSuccess,
                          decoration:
                              InputDecorations.textFieldDecoration.copyWith(
                            hintText: context.L.signupEmailPlaceholder,
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),
                      LabeledInputField(
                        label: context.L.passwordFieldLabel,
                        inputField: TextFormField(
                          autovalidateMode: AutovalidateMode.onUnfocus,
                          onChanged: (value) => ref
                              .read(loginFormControllerProvider.notifier)
                              .password = value,
                          validator: (value) => form.password
                              .validator(value ?? '')
                              ?.message(context),
                          style: TextStyles.inputValue,
                          obscureText: isPasswordHidden.value,
                          cursorColor: Colors.black,
                          keyboardType: TextInputType.visiblePassword,
                          enabled: !form.status.isInProgressOrSuccess,
                          decoration:
                              InputDecorations.textFieldDecoration.copyWith(
                            hintText: context.L.signupPasswordPlaceholder,
                            suffixIcon: IconButton(
                              icon: isPasswordHidden.value
                                  ? Assets.svgs.auth.eyeClose.svg()
                                  : Assets.svgs.auth.eyeOpen.svg(),
                              onPressed: () => isPasswordHidden.value =
                                  !isPasswordHidden.value,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () =>
                              context.push(ForgotPasswordRoute().location),
                          style: TextButton.styleFrom(padding: EdgeInsets.zero),
                          child: Text(
                            context.L.loginForgotPassword,
                            style: TextStyle(
                              color: AppColors.secondaryBlue,
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 15),
                      SizedBox(
                        width: double.infinity,
                        child: PrimaryButton(
                          onPressed: () => ref
                              .read(loginFormControllerProvider.notifier)
                              .submit(),
                          disabled: form.isNotValid,
                          child: form.status.isInProgressOrSuccess
                              ? SizedBox(
                                  height: 26,
                                  width: 26,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(context.L.signInButton),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
