import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class TermsAndConditionsScreen extends StatelessWidget {
  const TermsAndConditionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(statusBarColor: Colors.white));

    return PopScope(
      onPopInvokedWithResult: (_, __) => SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(statusBarColor: Colors.transparent),
      ),
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: const Size(0, 80),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 24),
            child: AppBar(
              title: Text(
                context.L.termsAndConditionsScreenTitle,
                style: TextStyles.subheading2,
              ),
              bottom: const PreferredSize(
                preferredSize: Size.fromHeight(0.5),
                child: Divider(color: AppColors.stroke, height: 0.5),
              ),
              centerTitle: true,
              leading: IconButton(
                icon: Assets.svgs.backArrow.svg(),
                onPressed: () => context.pop(),
              ),
              backgroundColor: Colors.white,
              scrolledUnderElevation: 0,
            ),
          ),
        ),
        extendBodyBehindAppBar: true,
        body: Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              image: Assets.images.bg.provider(),
              fit: BoxFit.cover,
              alignment: Alignment.center,
            ),
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.only(
                left: 30, right: 30, top: 140, bottom: 30),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "PLEASE READ THESE TERMS AND CONDITIONS OF USE CAREFULLY BEFORE USING THIS APPLICATION.",
                  style: TextStyles.heading5,
                ),
                const SizedBox(height: 12),
                Text(
                  "By using this Application, you signify your assent to these Terms and Conditions. If you do not agree with all of these Terms and Conditions of use, do not use this Application.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 12),
                Text(
                  "Brain Initiative Inc. may revise and update these Terms and Conditions at any time. Your continued usage of the NEURO World Application (\"Application\") will mean you accept those changes.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 16),
                Text(
                  "Children's Privacy",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                Text(
                  "We are committed to protecting the privacy of children. This Application is not intended or designed to attract children under the age of 13. We do not collect personally identifiable information from any person we actually know is a child under the age of 13.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 16),
                Text(
                  "Use of Materials",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                Text(
                  "All materials posted in this Application are protected by the copyright laws in the United States and in foreign countries. NEURO World authorizes you to view or download a single copy of the material in the Application solely for your personal, non-commercial use if you include the copyright notice located at the end of the material, for example:",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 12),
                Text(
                  "\"© 2025, NEURO World. All rights reserved.\"",
                  style: TextStyles.body3.copyWith(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 12),
                Text(
                  "Any special rules for the use of certain software and other items accessible in the Application may be included elsewhere within the Application and are incorporated into these Terms and Conditions by reference.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 12),
                Text(
                  "Title to the materials remains with Brain Initiative Inc. or its licensors. Any use of the materials not expressly permitted by these Terms and Conditions is a breach of these Terms and Conditions and may violate copyright, trademark, and other laws. Content and features are subject to change or termination without notice in the editorial discretion of NEURO World.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 12),
                Text(
                  "If you violate any of these Terms and Conditions, your permission to use the materials automatically terminates, and you must immediately destroy any copies you have made of any portion of the materials",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 16),
                Text(
                  "Liability of NEURO World and Its Licensors",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                Text(
                  "The use of the Application and the Content is at your own risk.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 12),
                Text(
                  "When using the Application, information will be transmitted over a medium that may be beyond the control and jurisdiction of NEURO World and its suppliers. Accordingly, NEURO World assumes no liability for or relating to the delay, failure, interruption, or corruption of any data or other information transmitted in connection with use of the Application.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 12),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            "The Application and the Content are provided on an \"as is\" basis. ",
                        style: TextStyles.body3,
                      ),
                      TextSpan(
                        text:
                            "NEURO World, ITS LICENSORS, AND ITS SUPPLIERS, TO THE FULLEST EXTENT PERMITTED BY LAW, DISCLAIM ALL WARRANTIES",
                        style: TextStyles.body3
                            .copyWith(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(
                        text:
                            ", including but not limited to the implied warranties of merchantability, non-infringement of third parties' rights, and fitness for a particular purpose.",
                        style: TextStyles.body3,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  "In no event shall NEURO World, its licensors, its suppliers, or any third parties mentioned in the Application be liable for any damages (including, without limitation, incidental and consequential damages, personal injury/wrongful death, lost profits, or damages resulting from lost data or business interruption) resulting from the use of or inability to use the Application or the Content.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 16),
                Text(
                  "User Submissions",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            "The personal information you submit to NEURO World is governed by the ",
                        style: TextStyles.body3,
                      ),
                      TextSpan(
                        text: "Privacy Policy",
                        style: TextStyles.body3
                            .copyWith(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(
                        text:
                            ". To the extent there is an inconsistency between this Agreement and the Privacy Policy, this Agreement shall govern.",
                        style: TextStyles.body3,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            "The Application contains functionality that allows users to upload content (collectively \"Public Areas\"). You agree that you will not upload or transmit any communications or content that infringes or violates any rights of any party. By submitting communications or content to the Public Areas, you agree that such submission is ",
                        style: TextStyles.body3,
                      ),
                      TextSpan(
                        text: "non-confidential for all purposes.",
                        style: TextStyles.body3
                            .copyWith(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  "Passwords",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                Text(
                  "You are responsible for maintaining the confidentiality of your password and account. NEURO World cannot and does not assume any responsibility or liability for any information you submit or third parties' use or misuse of information transmitted or received using the Application.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 16),
                Text(
                  "Community and Public Areas",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                Text(
                  "If you use a Public Area, you are solely responsible for your own communications and their consequences. NEURO World is not responsible for any content posted by users.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 12),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            "In consideration of being allowed to use the Public Areas, you agree ",
                        style: TextStyles.body3,
                      ),
                      TextSpan(
                        text: "not",
                        style: TextStyles.body3
                            .copyWith(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(
                        text: " to:",
                        style: TextStyles.body3,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                    "\u2022 Post material that infringes on intellectual property rights.\n\u2022 Post material that is unlawful, obscene, defamatory, or abusive.\n\u2022 Post advertisements or solicitations.\n\u2022 Impersonate another person.\n\u2022 Distribute viruses or other harmful code."),
                const SizedBox(height: 12),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "Brain Initiative Inc. reserves the right to ",
                        style: TextStyles.body3,
                      ),
                      TextSpan(
                        text: "remove content and terminate accounts",
                        style: TextStyles.body3
                            .copyWith(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(
                        text: " that violate these rules.",
                        style: TextStyles.body3,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  "Advertisements, Searches, and Links to Other Applications",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                Text(
                  "NEURO World may provide links to third-party applications. These links are provided for convenience only, and NEURO World does not endorse or assume responsibility for any content, accuracy, or policies of third-party applications.",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 16),
                Text(
                  "Indemnity",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "You agree to ",
                        style: TextStyles.body3,
                      ),
                      TextSpan(
                        text:
                            "defend, indemnify, and hold harmless NEURO World",
                        style: TextStyles.body3
                            .copyWith(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(
                        text:
                            " and its officers, directors, employees, agents, licensors, and suppliers from any claims, liabilities, and expenses arising from your violation of these Terms and Conditions.",
                        style: TextStyles.body3,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  "Jurisdiction",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            "These Terms and Conditions are governed by the internal substantive laws of the State of California. ",
                        style: TextStyles.body3,
                      ),
                      TextSpan(
                        text:
                            "You expressly agree that exclusive jurisdiction for any dispute with Brain Initiative Inc. resides in the courts of the State of California.",
                        style: TextStyles.body3
                            .copyWith(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  "Notice and Takedown Procedures; Copyright Agent",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                Text(
                  "If you believe any materials in the Application infringe your copyright, you may request removal by providing:",
                  style: TextStyles.body3,
                ),
                const SizedBox(height: 12),
                Text(
                    "1. Identification of the copyrighted work.\n2. Identification of the infringing material.\n3. Your contact information.\n4. A statement that you have a good faith belief that the use is unauthorized.\n5. A statement that the information is accurate and that you are authorized to act on behalf of the copyright owner."),
                const SizedBox(height: 16),
                Text(
                  "Complete Agreement",
                  style: TextStyles.heading3,
                ),
                const SizedBox(height: 12),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            "Except as expressly provided in a particular \"legal notice\" in the Application, these Terms and Conditions and the Privacy Policy constitute the ",
                        style: TextStyles.body3,
                      ),
                      TextSpan(
                        text: "entire agreement",
                        style: TextStyles.body3
                            .copyWith(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(
                        text:
                            " between you and Brain Initiative Inc. regarding the use of the Application.",
                        style: TextStyles.body3,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  "Last updated on February 5, 2025",
                  style: TextStyles.body3,
                ),
                Text(
                  "© ${DateTime.now().year} NEURO World All rights reserved.",
                  style: TextStyles.body3,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
