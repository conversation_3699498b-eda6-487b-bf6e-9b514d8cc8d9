import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:formz/formz.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/app_error_extension.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/input_decorations.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/labeled_input_field.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/toasts.dart';
import 'package:neuroworld/modules/auth/data/forms/signup_form.dart';
import 'package:neuroworld/modules/auth/ui/controllers/signup_form_controller.dart';

class SignupScreen extends HookConsumerWidget {
  const SignupScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPasswordHidden = useState(true);

    ref.listen<SignupForm>(signupFormControllerProvider, (_, state) {
      switch (state.status) {
        case FormzSubmissionStatus.failure:
          Toasts.showErrorToast(
            context,
            message:
                state.error?.errorMessage(context) ?? context.L.unknownError,
          );
          ref.read(signupFormControllerProvider.notifier).resetStatus();
        default:
          break;
      }
    });

    final form = ref.watch(signupFormControllerProvider);

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size(0, 90),
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: AppBar(
            scrolledUnderElevation: 0,
            leading: IconButton(
              icon: Assets.svgs.backArrow.svg(),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.transparent,
          ),
        ),
      ),
      extendBodyBehindAppBar: true,
      body: CustomScrollView(
        slivers: [
          SliverFillRemaining(
            hasScrollBody: false,
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: Assets.images.bg.provider(),
                  fit: BoxFit.cover,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Form(
                  key: form.key,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 145),
                      Text(
                        context.L.signupScreenTitle,
                        style: TextStyles.heading3,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        context.L.signupScreenSubTitle,
                        style: TextStyles.body1.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 40),
                      LabeledInputField(
                        label: context.L.fullNameFieldLabel,
                        inputField: TextFormField(
                          autovalidateMode: AutovalidateMode.onUnfocus,
                          onChanged: (value) => ref
                              .read(signupFormControllerProvider.notifier)
                              .fullName = value,
                          validator: (value) => form.fullName
                              .validator(value ?? '')
                              ?.message(context),
                          style: TextStyles.inputValue,
                          cursorColor: Colors.black,
                          keyboardType: TextInputType.name,
                          textInputAction: TextInputAction.next,
                          enabled: !form.status.isInProgressOrSuccess,
                          decoration:
                              InputDecorations.textFieldDecoration.copyWith(
                            hintText: context.L.signupFullNamePlaceholder,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      LabeledInputField(
                        label: context.L.emailFieldLabel,
                        inputField: TextFormField(
                          autovalidateMode: AutovalidateMode.onUnfocus,
                          onChanged: (value) => ref
                              .read(signupFormControllerProvider.notifier)
                              .email = value,
                          validator: (value) => form.email
                              .validator(value ?? '')
                              ?.message(context),
                          style: TextStyles.inputValue,
                          cursorColor: Colors.black,
                          keyboardType: TextInputType.emailAddress,
                          textInputAction: TextInputAction.next,
                          enabled: !form.status.isInProgressOrSuccess,
                          decoration:
                              InputDecorations.textFieldDecoration.copyWith(
                            hintText: context.L.signupEmailPlaceholder,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      LabeledInputField(
                        label: context.L.passwordFieldLabel,
                        inputField: TextFormField(
                          autovalidateMode: AutovalidateMode.onUnfocus,
                          onChanged: (value) => ref
                              .read(signupFormControllerProvider.notifier)
                              .password = value,
                          validator: (value) => form.password
                              .validator(value ?? '')
                              ?.message(context),
                          style: TextStyles.inputValue,
                          obscureText: isPasswordHidden.value,
                          enableSuggestions: false,
                          autocorrect: false,
                          cursorColor: Colors.black,
                          enabled: !form.status.isInProgressOrSuccess,
                          decoration:
                              InputDecorations.textFieldDecoration.copyWith(
                            hintText: context.L.signupPasswordPlaceholder,
                            suffixIcon: IconButton(
                              icon: isPasswordHidden.value
                                  ? Assets.svgs.auth.eyeClose.svg()
                                  : Assets.svgs.auth.eyeOpen.svg(),
                              onPressed: () => isPasswordHidden.value =
                                  !isPasswordHidden.value,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 35),
                      SizedBox(
                        width: double.infinity,
                        child: PrimaryButton(
                          onPressed: () => ref
                              .read(signupFormControllerProvider.notifier)
                              .submit(),
                          disabled: form.isNotValid,
                          child: form.status.isInProgressOrSuccess
                              ? SizedBox(
                                  height: 26,
                                  width: 26,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(context.L.signUpButton),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
