import 'dart:ui';

import 'package:flutter/material.dart';

Path transformedPath(String islandName, Path path) {
  final Matrix4 matrix = Matrix4.identity();

  if (islandName == "unwind") {
    matrix.translate(60.12, 16.69);
  } else if (islandName == "restore") {
    matrix.translate(172.24, 3.48);
  } else if (islandName == "exercise") {
    matrix.translate(3.95, 91.49);
  } else if (islandName == "optimize") {
    matrix.translate(214.22, 105.91);
  } else if (islandName == "nutrition") {
    matrix.translate(15.12, 195.07);
  }

  return path.transform(matrix.storage);
}

bool isPointInPath(Offset point, Path path) {
  final PathMetrics pathMetrics = path.computeMetrics();
  for (final PathMetric metric in pathMetrics) {
    final Path extractedPath = metric.extractPath(0, metric.length);
    if (extractedPath.contains(point)) {
      return true;
    }
  }
  return false;
}
