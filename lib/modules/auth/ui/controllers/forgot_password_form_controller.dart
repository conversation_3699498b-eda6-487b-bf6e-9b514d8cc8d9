import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:neuroworld/core/data/validators/email_validator.dart';
import 'package:neuroworld/modules/auth/data/forms/forgot_password_form.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'forgot_password_form_controller.g.dart';

@riverpod
class ForgotPasswordFormController extends _$ForgotPasswordFormController {
  @override
  ForgotPasswordForm build() =>
      ForgotPasswordForm(email: EmailInput.pure(), key: GlobalKey<FormState>());

  set email(String email) =>
      state = state.copyWith(email: EmailInput.dirty(email));

  Future<void> submit() async {
    if (state.status != FormzSubmissionStatus.inProgress &&
        state.status != FormzSubmissionStatus.success &&
        state.isValid) {
      state = state.copyWith(status: FormzSubmissionStatus.inProgress);
      try {
        state = state.copyWith(status: FormzSubmissionStatus.success);
      } catch (e) {
        state = state.copyWith(status: FormzSubmissionStatus.failure, error: e);
      }
    }
  }
}
