import 'package:neuroworld/modules/auth/data/models/response/forgot_password_response.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'forgot_password_controller.g.dart';

@riverpod
class ForgotPasswordController extends _$ForgotPasswordController {
  @override
  FutureOr<ForgotPasswordResponse?> build() => null;

  Future<ForgotPasswordResponse?> forgotPassword(String email) async {
    state = const AsyncLoading();
    try {
      final forgotPassword =
            await ref.read(authServiceProvider).forgotPassword(email);
      state = AsyncData(forgotPassword);
      return state.value!;
    } catch (e, s) {
      state = AsyncError(e, s);
      return state.valueOrNull;
    }
  }
}
