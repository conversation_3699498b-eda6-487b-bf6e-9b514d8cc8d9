// ignore_for_file: curly_braces_in_flow_control_structures

import 'package:google_sign_in/google_sign_in.dart';
import 'package:neuroworld/core/infrastructure/error/app_exceptions.dart';
import 'package:neuroworld/modules/auth/data/models/user.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

part 'auth_controller.g.dart';

@riverpod
class AuthController extends _$AuthController {
  @override
  FutureOr<User?> build() => null;

  Future<void> googleAuth() async {
    state = const AsyncLoading();

    final googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);

    try {
      final googleUser = await googleSignIn.signIn();
      if (googleUser == null)
        throw const AuthException(type: AuthExceptionType.googleSSO);
      final googleAuth = await googleUser.authentication;
      if (googleAuth.idToken == null)
        throw const AuthException(type: AuthExceptionType.googleSSO);

      final auth =
          await ref.read(authServiceProvider).googleSSO(googleAuth.idToken!);

      ref.read(authStateProvider.notifier).login(auth);
      state = AsyncValue.data(auth.user);
    } catch (e, s) {
      await GoogleSignIn().signOut();
      if (e is AuthException) {
        // can we ignore all AuthException throws for Google SSO? (see cases above)
        state = const AsyncData(null);
        return;
      }
      state = AsyncError(e, s);
    }
  }

  Future<void> appleAuth() async {
    state = const AsyncLoading();

    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      if (credential.identityToken == null)
        throw const AuthException(type: AuthExceptionType.appleSSO);

      final auth = await ref
          .read(authServiceProvider)
          .appleSSO(credential.identityToken!);

      ref.read(authStateProvider.notifier).login(auth);

      state = AsyncValue.data(auth.user);
    } catch (e, s) {
      if (e is SignInWithAppleAuthorizationException) {
        state = AsyncError(
          AuthException(
            type: e.code == AuthorizationErrorCode.canceled
                ? AuthExceptionType.canceled
                : AuthExceptionType.appleSSO,
          ),
          s,
        );
      } else if (e is AuthException) {
        state = AsyncError(AuthException(type: AuthExceptionType.appleSSO), s);
      } else {
        state = AsyncError(e, s);
      }
    }
  }
}
