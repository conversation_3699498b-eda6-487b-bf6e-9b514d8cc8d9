import 'dart:async';

import 'package:neuroworld/modules/goals/data/models/request/get_goal_request.dart';
import 'package:neuroworld/modules/goals/data/models/response/get_goal_response.dart';
import 'package:neuroworld/modules/goals/data/services/goals_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'onboarding_controller.g.dart';

@riverpod
class OnboardingController extends _$OnboardingController {
  @override
  FutureOr<GetGoalsResponse?> build() => null;

  Future<GetGoalsResponse?> getGoals({
    GetGoalRequest? request,
  }) async {
    state = await AsyncValue.guard(() async {
      final result = await ref.read(goalsServiceProvider).getGoal(request);
      return result;
    });

    return state.valueOrNull;
  }
}
