// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:neuroworld/core/data/validators/password_validator.dart';
import 'package:neuroworld/modules/auth/data/forms/reset_form.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'reset_password_form_controller.g.dart';

@riverpod
class ResetPasswordFormController extends _$ResetPasswordFormController {
  @override
  ResetPasswordForm build() => ResetPasswordForm(
        newPassword: PasswordInput.pure(),
        confirmPassword: PasswordInput.pure(),
        key: GlobalKey<FormState>(),
      );

  set newPassword(String password) {
    state = state.copyWith(newPassword: PasswordInput.dirty(password));
    _validatePasswords();
  }

  set confirmPassword(String password) {
    state = state.copyWith(confirmPassword: PasswordInput.dirty(password));
    _validatePasswords();
  }

  void resetStatus() =>
      state = state.copyWith(status: FormzSubmissionStatus.initial);

  void _validatePasswords() {
    if (state.confirmPassword.value.isNotEmpty &&
        state.confirmPassword.value != state.newPassword.value) {
      state = state.copyWith(
        confirmPassword: PasswordInput.dirty(state.confirmPassword.value),
      );
    }
  }

  Future<void> submit(String token) async {
    if (state.status != FormzSubmissionStatus.inProgress &&
        state.status != FormzSubmissionStatus.success &&
        state.isValid) {
      // state = state.copyWith(status: FormzSubmissionStatus.inProgress);
      try {
        await ref
            .read(authServiceProvider)
            .resetPassword(state.newPassword.value, token);
        state = state.copyWith(status: FormzSubmissionStatus.success);
      } catch (e) {
        print("This is the error: $e");
        state = state.copyWith(status: FormzSubmissionStatus.failure, error: e);
      }
    }
  }
}
