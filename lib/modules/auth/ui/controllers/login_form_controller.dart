import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:neuroworld/core/data/validators/email_validator.dart';
import 'package:neuroworld/core/data/validators/password_validator.dart';
import 'package:neuroworld/modules/auth/data/forms/login_form.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'login_form_controller.g.dart';

@riverpod
class LoginFormController extends _$LoginFormController {
  @override
  LoginForm build() =>
      LoginForm(email: EmailInput.pure(), key: GlobalKey<FormState>());

  set email(String email) =>
      state = state.copyWith(email: EmailInput.dirty(email));

  set password(String password) =>
      state = state.copyWith(password: PasswordInput.dirty(password));

  void resetStatus() =>
      state = state.copyWith(status: FormzSubmissionStatus.initial);

  Future<void> submit() async {
    if (state.status != FormzSubmissionStatus.inProgress &&
        state.status != FormzSubmissionStatus.success &&
        state.key.currentState!.validate() &&
        state.isValid) {
      state = state.copyWith(status: FormzSubmissionStatus.inProgress);
      try {
        final auth = await ref
            .read(authServiceProvider)
            .login(state.email.value, state.password.value);

        ref.read(authStateProvider.notifier).login(auth);
        state = state.copyWith(status: FormzSubmissionStatus.success);
      } catch (e) {
        state = state.copyWith(status: FormzSubmissionStatus.failure, error: e);
      }
    }
  }
}
