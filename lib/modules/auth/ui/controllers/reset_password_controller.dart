import 'package:neuroworld/modules/auth/data/models/response/forgot_password_response.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'reset_password_controller.g.dart';

@riverpod
class ResetPasswordController extends _$ResetPasswordController {
  @override
  FutureOr<ForgotPasswordResponse?> build() => null;

  Future<ForgotPasswordResponse?> resetPassword(
      String newPassword, String token) async {
    state = const AsyncLoading();
    try {
      final resetPassword =
          await ref.read(authServiceProvider).resetPassword(newPassword, token);
      state = AsyncData(resetPassword);
      return state.value!;
    } catch (e, s) {
      state = AsyncError(e, s);
      return state.valueOrNull;
    }
  }
}
