import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/infrastructure/error/app_exceptions.dart';
import 'package:neuroworld/modules/auth/data/sources/auth_shared_prefs.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'check_auth_provider.g.dart';

@riverpod
bool checkAuth(Ref ref) {
  try {
    final authSharedPrefs = ref.watch(authSharedPrefsProvider);
    final auth = authSharedPrefs.getAuthData();

    ref.read(authStateProvider.notifier).login(auth);
    return true;
  } catch (e) {
    if (e is CacheException) {
      // TODO: might want to show a session expiry message here
      // with reason being cache invalidation
      // ref.read(signOutProvider.notifier).signOut();
    }
    return false;
  }
}
