import 'dart:async';

import 'package:google_sign_in/google_sign_in.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'sign_out_provider.g.dart';

@riverpod
class SignOut extends _$SignOut {
  @override
  FutureOr<bool?> build() => null;

  Future<void> signOut() async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(
      () async {
        await ref.read(authServiceProvider).signOut();
        unawaited(GoogleSignIn().signOut());
        ref.read(authStateProvider.notifier).signOut();
        return true;
      },
    );
  }
}
