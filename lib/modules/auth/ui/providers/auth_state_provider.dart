import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:neuroworld/modules/auth/data/models/response/auth.dart';
import 'package:neuroworld/modules/auth/data/models/user.dart';
import 'package:neuroworld/modules/badges/data/models/user_badge.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_state_provider.g.dart';

@Riverpod(keepAlive: true)
class AuthState extends _$AuthState {
  @override
  Auth? build() => null;

  void login(Auth auth) => state = auth;

  void signOut() => state = null;

  void updateUser(User user) => state = state?.copyWith(user: user);

  void appendBadges(List<UserBadge> badges) {
    if (state != null) {
      final currentBadges = state!.user.badges;
      final updatedBadges = [...currentBadges, ...badges];
      final updatedUser = state!.user.copyWith(badges: updatedBadges.toIList());
      state = state!.copyWith(user: updatedUser);
    }
  }
}
