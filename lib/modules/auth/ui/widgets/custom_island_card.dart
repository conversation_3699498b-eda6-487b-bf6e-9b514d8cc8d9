import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class CustomIslandCard extends StatelessWidget {
  final String title;
  final String description;
  final Color borderColor;
  final bool isSelected;
  final VoidCallback onTap;
  final bool isDisabled;

  const CustomIslandCard({
    super.key,
    required this.title,
    required this.description,
    required this.borderColor,
    required this.isSelected,
    required this.onTap,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: isDisabled,
      child: GestureDetector(
        onTap: isDisabled ? null : onTap,
        child: Container(
          height: 72,
          width: 130,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? borderColor.withValues(alpha: 0.1)
                  : Colors.white,
              width: 2,
            ),
          ),
          child: Container(
            height: 66,
            width: 120,
            decoration: BoxDecoration(
              color: isSelected
                  ? borderColor.withValues(alpha: 0.1)
                  : Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: borderColor, width: 2),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title.toUpperCase(),
                        style: TextStyles.heading1.copyWith(
                          fontSize: 12,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: SvgPicture.asset(
                          isDisabled
                              ? Assets.svgs.disabledIslandsSelected.path
                              : isSelected
                                  ? Assets.svgs.selected.path
                                  : Assets.svgs.deselected.path,
                          width: 18,
                          height: 18,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyles.subheading2.copyWith(
                      fontSize: 10,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
