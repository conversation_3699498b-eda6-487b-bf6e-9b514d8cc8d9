import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

enum AvatarPosition { leftTop, leftCenter, leftBottom }

class BrainAssistant extends StatelessWidget {
  final String message;
  final String? buttonTitle;
  final VoidCallback? onTap;
  final AvatarPosition avatarPosition;
  final double avatarSize;
  final String? avatarPath;
  final bool showAvatar;

  const BrainAssistant({
    super.key,
    required this.message,
    this.buttonTitle,
    this.onTap,
    this.avatarPosition = AvatarPosition.leftBottom,
    this.avatarSize = 80,
    this.avatarPath,
    this.showAvatar = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        crossAxisAlignment: avatarPosition == AvatarPosition.leftTop
            ? CrossAxisAlignment.start
            : avatarPosition == AvatarPosition.leftCenter
                ? CrossAxisAlignment.center
                : CrossAxisAlignment.end,
        children: [
          // **Avatar (Optional but Space Reserved)**
          SizedBox(
            width: avatarSize,
            height: avatarSize,
            child: showAvatar && avatarPath != null
                ? SvgPicture.asset(avatarPath!)
                : const SizedBox(), // Empty space if avatar is hidden
          ),
          const SizedBox(width: 8), // Space between avatar and chat bubble

          // **Chat Bubble**
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.brainBg,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // **Message**
                  Text(
                    message,
                    style: TextStyles.body2.copyWith(
                      color: AppColors.brainText,
                    ),
                  ),
                  if (buttonTitle != null && onTap != null) ...[
                    const SizedBox(height: 10),
                    // **Button**
                    GestureDetector(
                      onTap: onTap,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        decoration: BoxDecoration(
                          color: AppColors.surfacePrimary,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.2),
                              blurRadius: 4,
                              spreadRadius: 1,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          buttonTitle!,
                          style: TextStyles.subtitle2.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
                    ),
                  ]
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
