import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class BottomSignIn extends StatelessWidget {
  final VoidCallback onSignUpTap;

  const BottomSignIn({super.key, required this.onSignUpTap});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 74,
      left: 0,
      right: 0,
      child: Center(
        child: RichText(
          text: TextSpan(
            style: TextStyles.body1.copyWith(
              color: AppColors.textPrimary,
            ),
            children: [
              const TextSpan(text: "Already have an account? "),
              WidgetSpan(
                child: GestureDetector(
                  onTap: onSignUpTap,
                  child: Text(
                    'Sign In',
                    style: TextStyles.subheading2.copyWith(
                      color: AppColors.secondaryBlue,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
