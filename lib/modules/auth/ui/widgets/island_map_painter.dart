import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:neuroworld/modules/onboarding/data/island_paths.dart';

class IslandMapPainter extends CustomPainter {
  final ui.Image? baseMap;
  final String? selectedIsland;
  final List<String> disabledIslands;

  IslandMapPainter({
    required this.baseMap,
    required this.selectedIsland,
    this.disabledIslands = const [],
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint();

    if (baseMap != null) {
      canvas.drawImage(baseMap!, Offset.zero, paint);
    }

    for (var entry in islandPaths.entries) {
      final islandKey = entry.key;
      final Path path = entry.value;

      paint.color = Colors.transparent;

      canvas.save();

      if (islandKey == "unwind") {
        canvas.translate(60.12, 16.69);
      } else if (islandKey == "restore") {
        canvas.translate(172.24, 3.48);
      } else if (islandKey == "exercise") {
        canvas.translate(3.95, 91.49);
      } else if (islandKey == "optimize") {
        canvas.translate(214.22, 105.91);
      } else if (islandKey == "nutrition") {
        canvas.translate(15.12, 195.07);
      }

      canvas.scale(1.0);
      canvas.drawPath(path, paint);
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
