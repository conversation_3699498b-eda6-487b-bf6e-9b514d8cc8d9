import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/modules/auth/ui/widgets/island_map_painter.dart';
import 'package:neuroworld/modules/goals/data/island_category_data.dart';
import 'package:neuroworld/modules/onboarding/data/island_data.dart';

import '../providers/selected_island_provider.dart';
import '../utils/image_loader.dart';
import 'custom_island_card.dart';

class IslandMapWidget extends ConsumerStatefulWidget {
  final List<String>? disabledIslands;
  final List<IslandCategory>? allGoals;

  const IslandMapWidget({super.key, this.disabledIslands, this.allGoals});

  @override
  ConsumerState<IslandMapWidget> createState() => _IslandMapWidgetState();
}

class _IslandMapWidgetState extends ConsumerState<IslandMapWidget> {
  ui.Image? _baseMap;

  @override
  void initState() {
    super.initState();
    _loadBaseMap();
  }

  Future<void> _loadBaseMap() async {
    final image = await loadImage(Assets.images.fullMap.path);
    setState(() {
      _baseMap = image;
    });
  }

  @override
  Widget build(BuildContext context) {
    final selectedIsland = ref.watch(selectedIslandProvider);

    return Center(
      child: SizedBox(
        width: 350,
        height: 335,
        child: Stack(
          children: [
            Positioned.fill(
              child: FittedBox(
                fit: BoxFit.none,
                child: SizedBox(
                  width: _baseMap?.width.toDouble() ?? 0,
                  height: _baseMap?.height.toDouble() ?? 0,
                  child: CustomPaint(
                    painter: IslandMapPainter(
                      baseMap: _baseMap,
                      selectedIsland: selectedIsland,
                      disabledIslands: widget.disabledIslands ?? [],
                    ),
                  ),
                ),
              ),
            ),
            ...islandData.entries.map((entry) {
              final islandKey = entry.key;
              final island = entry.value;

              final matchingGoal = widget.allGoals?.firstWhere(
                (goal) => goal.key == islandKey,
              );

              final isDisabled =
                  widget.disabledIslands?.contains(islandKey) ?? false;

              final title = matchingGoal?.title ?? '';
              final description = matchingGoal?.subtitle ?? '';
              final borderColor = matchingGoal?.color ?? Colors.transparent;

              return Positioned(
                top: island.position.dy,
                left: island.position.dx - 40,
                child: CustomIslandCard(
                  title: title,
                  description: description,
                  borderColor: borderColor,
                  isSelected: selectedIsland == islandKey,
                  isDisabled: isDisabled,
                  onTap: () {
                    ref.read(selectedIslandProvider.notifier).select(
                          selectedIsland == islandKey ? null : islandKey,
                        );
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
