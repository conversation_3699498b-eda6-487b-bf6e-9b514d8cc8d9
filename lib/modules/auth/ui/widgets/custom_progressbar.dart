import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

class CustomProgressBar extends StatelessWidget {
  final double progress;
  final Duration duration;

  const CustomProgressBar({
    super.key,
    required this.progress,
    this.duration = const Duration(milliseconds: 500),
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: 10,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(5),
          ),
        ),
        TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.0, end: progress),
          duration: duration,
          builder: (context, value, child) {
            return FractionallySizedBox(
              widthFactor: value,
              child: Container(
                height: 10,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.orangeGradientLight,
                      AppColors.orangeGradientDark,
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(5),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
