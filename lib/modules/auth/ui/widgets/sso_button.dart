import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

enum SSOButtonType {
  apple(),
  google(),
  email(),
  signupApple(),
  signupGoogle(),
  signupEmail();

  Widget getIcon() {
    return switch (this) {
      SSOButtonType.apple || SSOButtonType.signupApple => Padding(
          padding: const EdgeInsets.only(top: 3, right: 3),
          child: Assets.svgs.auth.apple.svg(height: 25),
        ),
      SSOButtonType.google || SSOButtonType.signupGoogle => Padding(
          padding: const EdgeInsets.only(top: 3, right: 3),
          child: Assets.svgs.auth.google.svg(height: 25),
        ),
      SSOButtonType.email || SSOButtonType.signupEmail => Padding(
          padding: const EdgeInsets.only(top: 5, right: 5),
          child: Assets.svgs.auth.envelope.svg(height: 17),
        ),
    };
  }

  String getLabel(BuildContext context) {
    return switch (this) {
      SSOButtonType.apple => context.L.landingSSOApple,
      SSOButtonType.google => context.L.landingSSOGoogle,
      SSOButtonType.email => context.L.landingSSOEmail,
      SSOButtonType.signupApple => context.L.landingSignupSSOApple,
      SSOButtonType.signupGoogle => context.L.landingSignupSSOGoogle,
      SSOButtonType.signupEmail => context.L.landingSignupSSOEmail,
    };
  }
}

class SSOButton extends StatelessWidget {
  const SSOButton({
    super.key,
    required this.buttonType,
    required this.onPressed,
  });

  final SSOButtonType buttonType;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.only(top: 8, bottom: 10),
          textStyle: TextStyles.buttonLarge,
          foregroundColor: AppColors.textSecondary,
          side: BorderSide(
            width: 1,
            color: AppColors.stroke,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        icon: buttonType.getIcon(),
        label: Text(buttonType.getLabel(context)),
      ),
    );
  }
}
