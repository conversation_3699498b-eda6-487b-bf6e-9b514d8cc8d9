import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/auth/ui/widgets/island_map_widget.dart';
import 'package:neuroworld/modules/goals/data/island_category_data.dart';
import 'package:neuroworld/modules/onboarding/data/onboarding_data.dart';

class OnboardingContent extends HookConsumerWidget {
  final OnboardingData onboardingData;
  final bool? isAddGoal;
  final List<String>? disabledIslands;
  final List<IslandCategory> allGoals;

  final bool isReverse;
  final VoidCallback? onReverseCompleted;

  const OnboardingContent({
    super.key,
    required this.onboardingData,
    this.isAddGoal,
    this.disabledIslands,
    required this.allGoals,
    this.isReverse = false,
    this.onReverseCompleted,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Animation controller
    final controller =
        useAnimationController(duration: const Duration(seconds: 2));

    final anim = CurvedAnimation(
      parent: controller,
      curve: Curves.easeOutQuad,
      reverseCurve: Curves.easeInQuart,
    );
    final scaleAnim = Tween<double>(begin: 1, end: 1.5).animate(anim);
    final opacityAnim = Tween<double>(begin: 1, end: 0.7).animate(anim);
    final blurAnim = Tween<double>(begin: 0, end: 5).animate(anim);

    useEffect(() {
      Future.delayed(
        const Duration(milliseconds: 500),
        () => controller.forward(),
      );
      return null;
    }, [controller]);

    useEffect(() {
      if (isReverse) {
        controller.reverse().then((_) {
          if (onReverseCompleted != null) {
            onReverseCompleted!();
          }
        });
      }
      return null;
    }, [isReverse, controller]);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (onboardingData.animation.isNotEmpty)
          SizedBox(
            height: 99,
            width: 104,
            child: Lottie.asset(
              onboardingData.animation,
              height: 250,
              width: 250,
              fit: BoxFit.cover,
            ),
          ),
        SizedBox(height: onboardingData.animation.isNotEmpty ? 24 : 12),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 34),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (isAddGoal == null)
                Column(
                  children: [
                    Text(
                      onboardingData.title(context),
                      textAlign: TextAlign.center,
                      style: TextStyles.heading3.copyWith(
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      onboardingData.description(context),
                      textAlign: TextAlign.center,
                      style: TextStyles.body1.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w400,
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                )
            ],
          ),
        ),
        if (onboardingData.type != OnboardingStepType.animation)
          const SizedBox(height: 24),
        if (onboardingData.type == OnboardingStepType.animation)
          SizedBox(
            height: 400,
            child: LayoutBuilder(
              builder: (context, constraints) {
                const double svgSize = 200;
                final centerLeft = (constraints.maxWidth - svgSize) / 2;
                final centerBottom = (constraints.maxHeight - svgSize) / 2;

                final positionAnim = RelativeRectTween(
                  begin: onboardingData.buildRelativeRect(constraints, svgSize),
                  end: RelativeRect.fromLTRB(
                    centerLeft,
                    centerBottom,
                    constraints.maxWidth - centerLeft - svgSize,
                    constraints.maxHeight - centerBottom - svgSize,
                  ),
                ).animate(anim);

                return Stack(
                  children: [
                    if (onboardingData.image != null &&
                        !onboardingData.shouldBlur)
                      Center(
                        child: SvgPicture.asset(
                          onboardingData.image!,
                          width: 350,
                          height: 350,
                        ),
                      ),
                    if (onboardingData.image != null &&
                        onboardingData.shouldBlur)
                      AnimatedBuilder(
                        animation: anim,
                        child: Center(
                          child: SvgPicture.asset(
                            onboardingData.image!,
                            width: 350,
                            height: 350,
                          ),
                        ),
                        builder: (context, child) => Opacity(
                          opacity: opacityAnim.value,
                          child: ImageFiltered(
                            imageFilter: ImageFilter.blur(
                              sigmaX: blurAnim.value,
                              sigmaY: blurAnim.value,
                            ),
                            child: child,
                          ),
                        ),
                      ),
                    if (onboardingData.type == OnboardingStepType.animation &&
                        onboardingData.animationPath != null)
                      PositionedTransition(
                        rect: positionAnim,
                        child: ScaleTransition(
                          scale: scaleAnim,
                          child: SizedBox(
                            width: svgSize,
                            height: svgSize,
                            child: SvgPicture.asset(
                              onboardingData.animationPath!,
                            ),
                          ),
                        ),
                      ),
                  ],
                );
              },
            ),
          ),
        if (onboardingData.type == OnboardingStepType.mapView) ...[
          IslandMapWidget(
            disabledIslands: disabledIslands,
            allGoals: allGoals,
          ),
        ],
      ],
    );
  }
}
