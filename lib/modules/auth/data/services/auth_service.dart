import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/modules/auth/data/models/profile.dart';
import 'package:neuroworld/modules/auth/data/models/request/apple_sso_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/forgot_password_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/google_sso_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/login_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/refresh_token_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/reset_password_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/signup_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/update_profile_request.dart';
import 'package:neuroworld/modules/auth/data/models/response/auth.dart';
import 'package:neuroworld/modules/auth/data/models/response/forgot_password_response.dart';
import 'package:neuroworld/modules/auth/data/models/user.dart';
import 'package:neuroworld/modules/auth/data/sources/auth_api.dart';
import 'package:neuroworld/modules/auth/data/sources/auth_shared_prefs.dart';
import 'package:neuroworld/modules/home/<USER>/models/request/feedback_request.dart';
import 'package:neuroworld/modules/home/<USER>/models/response/feedback_response.dart';
import 'package:neuroworld/modules/notification/data/providers/fcm_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_service.g.dart';

@Riverpod(keepAlive: true)
AuthService authService(Ref ref) => AuthService(
      authSharedPrefs: ref.watch(authSharedPrefsProvider),
      authApi: ref.watch(authApiProvider),
      fcmToken: ref.watch(fcmTokenProvider).requireValue,
    );

class AuthService {
  AuthService({
    required this.authSharedPrefs,
    required this.authApi,
    required this.fcmToken,
  });
  final AuthSharedPrefs authSharedPrefs;
  final AuthApi authApi;
  final String? fcmToken;

  Future<Auth> login(String email, String password) async {
    final res = await authApi.login(
      LoginRequest(
        email: email,
        password: password,
        fcmToken: fcmToken,
      ),
    );
    final authResponse = Auth.fromJson(res.data);
    await authSharedPrefs.setAuthData(authResponse);
    return authResponse;
  }

  Future<void> signOut() async {
    await authApi.logout(fcmToken: fcmToken);
    await authSharedPrefs.clearUserData();
  }

  Future<Auth> googleSSO(String idToken) async {
    final timeZone = await FlutterTimezone.getLocalTimezone();
    final body = GoogleSSORequest(
      idToken: idToken,
      timeZone: timeZone,
      fcmToken: fcmToken,
    );
    final res = await authApi.googleSSO(body);
    final authResponse = Auth.fromJson(res.data);
    await authSharedPrefs.setAuthData(authResponse);
    return authResponse;
  }

  Future<Auth> appleSSO(String idToken) async {
    final timeZone = await FlutterTimezone.getLocalTimezone();
    final body = AppleSSORequest(
      idToken: idToken,
      timeZone: timeZone,
      fcmToken: fcmToken,
    );
    final res = await authApi.appleSSO(body);
    final authResponse = Auth.fromJson(res.data);
    await authSharedPrefs.setAuthData(authResponse);
    return authResponse;
  }

  Future<Auth> createUser(
    String fullName,
    String email,
    String password,
  ) async {
    final timeZone = await FlutterTimezone.getLocalTimezone();
    final res = await authApi.createUser(
      SignupRequest(
        fullName: fullName,
        email: email,
        password: password,
        timeZone: timeZone,
        fcmToken: fcmToken,
      ),
    );
    final authResponse = Auth.fromJson(res.data);
    await authSharedPrefs.setAuthData(authResponse);
    return authResponse;
  }

  Future<Auth> updateUserProfile(UpdateProfileRequest newProfile) async {
    final userProfileId = authSharedPrefs.getAuthData().user.profile.id;
    final res = await authApi.updateUserProfile(userProfileId, newProfile);
    final updatedProfile = Profile.fromJson(res.data);
    final newAuth = authSharedPrefs.getAuthData().copyWith.user(
          profile: updatedProfile,
        );
    await authSharedPrefs.setAuthData(newAuth);
    return newAuth;
  }

  Future<Auth> updateUserProfilePicture(
      Uint8List compressedImageBytes, String fileName) async {
    final res =
        await authApi.updateUserProfilePicture(compressedImageBytes, fileName);

    final updatedProfile = Profile.fromJson(res.data);
    final newAuth = authSharedPrefs.getAuthData().copyWith.user(
          profile: updatedProfile,
        );
    await authSharedPrefs.setAuthData(newAuth);
    return newAuth;
  }

  Future<Auth> refreshUser() async {
    final res = await authApi.me(fcmToken: fcmToken);
    final user = User.fromJson(res.data['user']);
    final newAuth = authSharedPrefs.getAuthData().copyWith(user: user);
    await authSharedPrefs.setAuthData(newAuth);
    return newAuth;
  }

  Future<Auth> refreshToken(
    Auth auth,
    DioException unAuthorizedErr,
  ) async {
    final request = RefreshTokenRequest(
      access: auth.access,
      refresh: auth.refresh,
    );
    final res = await authApi.refreshToken(request);

    if (res == null) {
      // throw 511 session expired
      unAuthorizedErr.response?.statusCode = 511;
      throw unAuthorizedErr;
    }

    final String newAccessToken = res.data['access'];
    final newAuth = auth.copyWith(access: newAccessToken);
    await authSharedPrefs.setAuthData(newAuth);
    return newAuth;
  }

  Future<FeedbackResponse> postFeedback(
    String? comment,
    int score,
  ) async {
    final res = await authApi
        .postFeedback(FeedbackRequest(comment: comment ?? '', score: score));
    final feedbackResponse = FeedbackResponse.fromJson(res.data);
    return feedbackResponse;
  }

  Future<int?> deleteUser() async {
    final userProfileId = authSharedPrefs.getAuthData().user.id;
    final res = await authApi.deleteUser(userProfileId);
    return res.statusCode;
  }

  Future<ForgotPasswordResponse> forgotPassword(String email) async {
    final res =
        await authApi.forgotPassword(ForgotPasswordRequest(email: email));
    final forgotPasswordResponse = ForgotPasswordResponse.fromJson(res.data);
    return forgotPasswordResponse;
  }

  Future<ForgotPasswordResponse> resetPassword(
      String newPassword, String token) async {
    final res = await authApi.resetPassword(
        ResetPasswordRequest(newPassword: newPassword, token: token));
    final resetPasswordResponse = ForgotPasswordResponse.fromJson(res.data);
    return resetPasswordResponse;
  }
}
