import 'package:freezed_annotation/freezed_annotation.dart';

part 'signup_request.freezed.dart';
part 'signup_request.g.dart';

@freezed
abstract class SignupRequest with _$SignupRequest {
  @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
  const factory SignupRequest({
    required String fullName,
    required String email,
    required String password,
    required String timeZone,
    String? fcmToken,
  }) = _SignupRequest;

  factory SignupRequest.fromJson(Map<String, dynamic> json) =>
      _$SignupRequestFromJson(json);
}
