import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/core/data/converters/date_converter.dart';
import 'package:neuroworld/modules/home/<USER>/models/gender.dart';

part 'update_profile_request.freezed.dart';
part 'update_profile_request.g.dart';

@freezed
abstract class UpdateProfileRequest with _$UpdateProfileRequest {
  @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
  const factory UpdateProfileRequest({
    String? fullName,
    @GenderConverter() Gender? gender,
    @DateConverter() DateTime? birthDate,
    bool? isOnboardingCompleted,
    bool? isProfileCompleted,
    bool? isChatConsent,
    // ... points, firstTrackedDate,
  }) = _UpdateProfileRequest;

  factory UpdateProfileRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateProfileRequestFromJson(json);
}
