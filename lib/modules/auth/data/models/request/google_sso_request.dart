import 'package:freezed_annotation/freezed_annotation.dart';

part 'google_sso_request.freezed.dart';
part 'google_sso_request.g.dart';

@freezed
abstract class GoogleSSORequest with _$GoogleSSORequest {
  @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
  const factory GoogleSSORequest({
    required String idToken,
    required String timeZone,
    String? fcmToken,
  }) = _GoogleSSORequest;

  factory GoogleSSORequest.fromJson(Map<String, dynamic> json) =>
      _$GoogleSSORequestFromJson(json);
}
