import 'package:freezed_annotation/freezed_annotation.dart';

part 'apple_sso_request.freezed.dart';
part 'apple_sso_request.g.dart';

@freezed
abstract class AppleSSORequest with _$AppleSSORequest {
  @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
  const factory AppleSSORequest({
    required String idToken,
    required String timeZone,
    String? fcmToken,
  }) = _AppleSSORequest;

  factory AppleSSORequest.fromJson(Map<String, dynamic> json) =>
      _$AppleSSORequestFromJson(json);
}
