import 'package:freezed_annotation/freezed_annotation.dart';

part 'active_subscription.freezed.dart';
part 'active_subscription.g.dart';

@freezed
sealed class Plan with _$Plan {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory Plan({
    required String id,
    required String tier,
    required String name,
    required String billingPeriod,
    required String productId,
    required int durationDays,
  }) = _Plan;

  factory Plan.fromJson(Map<String, dynamic> json) => _$PlanFromJson(json);
}

@freezed
sealed class ActiveSubscription with _$ActiveSubscription {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory ActiveSubscription({
    required String id,
    required Plan plan,
    required String status,
    required DateTime startTime,
    required DateTime expiryTime,
  }) = _ActiveSubscription;

  factory ActiveSubscription.fromJson(Map<String, dynamic> json) =>
      _$ActiveSubscriptionFromJson(json);
}
