import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/auth/data/models/user.dart';

part 'auth.freezed.dart';
part 'auth.g.dart';

@freezed
sealed class Auth with _$Auth {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory Auth({
    required String access,
    required String refresh,
    required User user,
  }) = _Auth;

  factory Auth.fromJson(Map<String, dynamic> json) => _$AuthFromJson(json);
}
