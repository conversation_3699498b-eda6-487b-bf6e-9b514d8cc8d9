import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/core/data/converters/date_converter.dart';
import 'package:neuroworld/modules/home/<USER>/models/gender.dart';

part 'profile.freezed.dart';
part 'profile.g.dart';

@freezed
abstract class Profile with _$Profile {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory Profile({
    required String id,
    required String? profilePictureUrl,
    required String? fullName,
    required int points,
    @GenderConverter() Gender? gender,
    required bool isOnboardingCompleted,
    required bool isProfileCompleted,
    required bool isChatConsent,
    @DateConverter() DateTime? birthDate,
    @DateConverter() DateTime? firstTrackedDate,
    required List<dynamic> badges,
  }) = _Profile;

  factory Profile.fromJson(Map<String, dynamic> json) =>
      _$ProfileFromJson(json);
}
