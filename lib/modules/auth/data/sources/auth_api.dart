import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/config/api_config.dart';
import 'package:neuroworld/core/data/sources/network/api/api.dart';
import 'package:neuroworld/core/data/sources/network/api/api_endpoints.dart';
import 'package:neuroworld/modules/auth/data/models/request/apple_sso_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/forgot_password_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/google_sso_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/login_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/refresh_token_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/reset_password_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/signup_request.dart';
import 'package:neuroworld/modules/auth/data/models/request/update_profile_request.dart';
import 'package:neuroworld/modules/home/<USER>/models/request/feedback_request.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_api.g.dart';

@Riverpod(keepAlive: true)
AuthApi authApi(Ref ref) {
  return AuthApi(
    ref,
    api: ref.watch(apiProvider),
  );
}

class AuthApi {
  AuthApi(
    this.ref, {
    required this.api,
  });

  final Ref ref;
  final Api api;

  Future<Response<dynamic>> me({String? fcmToken}) => api.get(
        path: ApiEndpoints.me,
        queryParameters: fcmToken != null ? {"fcm_token": fcmToken} : null,
      );

  Future<Response<dynamic>> login(LoginRequest request) => api.post(
        path: ApiEndpoints.token,
        data: request.toJson(),
      );

  Future<Response<dynamic>> logout({String? fcmToken}) => api.post(
        path: ApiEndpoints.logout,
        data: fcmToken != null ? {"fcm_token": fcmToken} : null,
      );

  Future<Response<dynamic>> googleSSO(GoogleSSORequest request) {
    return api.post(
      path: ApiEndpoints.googleToken,
      data: request.toJson(),
    );
  }

  Future<Response<dynamic>> appleSSO(AppleSSORequest request) {
    return api.post(
      path: ApiEndpoints.appleToken,
      data: request.toJson(),
    );
  }

  Future<Response<dynamic>> createUser(SignupRequest request) => api.post(
        path: ApiEndpoints.users,
        data: request.toJson(),
      );

  Future<Response<dynamic>> updateUserProfile(
    String userProfileId,
    UpdateProfileRequest request,
  ) =>
      api.patch(
        path: ApiEndpoints.userProfileById(userProfileId),
        data: request.toJson(),
      );

  Future<Response<dynamic>> updateUserProfilePicture(
    Uint8List compressedImageBytes,
    String fileName,
  ) =>
      api.post(
        path: ApiEndpoints.uploadProfilePicture,
        data: FormData.fromMap(
          {
            'profile_picture': MultipartFile.fromBytes(
              compressedImageBytes,
              filename: fileName,
            ),
          },
        ),
      );

  Future<Response<dynamic>?> refreshToken(RefreshTokenRequest request) async {
    try {
      // we use separate dio for obtaining refresh token
      // because we don't want to hit deadlock on same interceptors
      Dio refreshDio = Dio(
        BaseOptions(baseUrl: ApiConfig.baseUrl),
      );

      final response = await refreshDio.post(ApiEndpoints.tokenRefresh, data: {
        "access": request.access,
        "refresh": request.refresh,
      });
      if (response.statusCode == 200) {
        return response;
      }
    }
    // silence errors, need to rethrow the 401 if refresh token request fails
    catch (e) {
      return null;
    }
    return null;
  }

  Future<Response<dynamic>> postFeedback(FeedbackRequest request) {
    return api.post(
      path: ApiEndpoints.feedback,
      data: request.toJson(),
    );
  }

  Future<Response<dynamic>> deleteUser(String userProfileId) => api.delete(
        path: ApiEndpoints.deleteUser(userProfileId),
      );

  Future<Response<dynamic>> forgotPassword(ForgotPasswordRequest request) {
    return api.post(
      path: ApiEndpoints.forgotPassword,
      data: request.toJson(),
    );
  }

  Future<Response<dynamic>> resetPassword(ResetPasswordRequest request) {
    return api.post(
      path: ApiEndpoints.resetPassword,
      data: request.toJson(),
    );
  }
}
