import 'dart:convert';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/shared_prefs_keys.dart';
import 'package:neuroworld/core/data/sources/shared_preferences.dart';
import 'package:neuroworld/core/infrastructure/error/app_exceptions.dart';
import 'package:neuroworld/modules/auth/data/models/response/auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'auth_shared_prefs.g.dart';

@Riverpod(keepAlive: true)
AuthSharedPrefs authSharedPrefs(Ref ref) {
  return AuthSharedPrefs(ref);
}

class AuthSharedPrefs {
  AuthSharedPrefs(this.ref);

  final Ref ref;
  SharedPreferencesWithCache get sharedPreferences =>
      ref.read(sharedPreferencesProvider).requireValue;

  Future<void> setAuthData(Auth auth) async {
    final authJson = json.encode(auth.toJson());
    await sharedPreferences.setString(SharedPrefsKeys.auth, authJson);
  }

  Auth getAuthData() {
    final authJson = sharedPreferences.getString(SharedPrefsKeys.auth);
    if (authJson != null) {
      final auth = Auth.fromJson(json.decode(authJson) as Map<String, dynamic>);
      return auth;
    } else {
      throw const CacheException(
        type: CacheExceptionType.notFound,
        message: 'Auth data not found in cache',
      );
    }
  }

  Future<void> clearUserData() async {
    await [
      sharedPreferences.remove(SharedPrefsKeys.auth),
      // add more keys to clear
    ].wait;
  }
}
