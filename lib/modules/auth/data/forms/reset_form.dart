// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:formz/formz.dart';
import 'package:neuroworld/core/data/models/form_model.dart';
import 'package:neuroworld/core/data/validators/password_validator.dart';

class ResetPasswordForm extends FormModel with FormzMixin {
  ResetPasswordForm({
    required super.key,
    super.status,
    required this.newPassword,
    required this.confirmPassword,
    this.error,
  });

  final PasswordInput newPassword;
  final PasswordInput confirmPassword;
  final Object? error;

  @override
  List<FormzInput> get inputs => [newPassword, confirmPassword];

  @override
  ResetPasswordForm copyWith({
    PasswordInput? newPassword,
    PasswordInput? confirmPassword,
    FormzSubmissionStatus? status,
    Object? error,
  }) {
    return ResetPasswordForm(
      key: key,
      newPassword: newPassword ?? this.newPassword,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      status: status ?? super.status,
      error: error ?? this.error,
    );
  }
}
