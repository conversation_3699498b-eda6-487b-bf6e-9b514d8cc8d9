import 'package:formz/formz.dart';
import 'package:neuroworld/core/data/models/form_model.dart';
import 'package:neuroworld/core/data/validators/email_validator.dart';
import 'package:neuroworld/core/data/validators/name_validator.dart';
import 'package:neuroworld/core/data/validators/password_validator.dart';

class SignupForm extends FormModel with FormzMixin {
  SignupForm({
    required super.key,
    this.fullName = const NameInput.pure(),
    required this.email,
    this.password = const PasswordInput.pure(),
    this.error,
    super.status,
  });

  final NameInput fullName;
  final EmailInput email;
  final PasswordInput password;
  final Object? error;

  @override
  List<FormzInput> get inputs => [email, password];

  @override
  SignupForm copyWith({
    NameInput? fullName,
    EmailInput? email,
    PasswordInput? password,
    bool? isTermsAndConditionsChecked,
    FormzSubmissionStatus? status,
    Object? error,
  }) {
    return SignupForm(
      key: key,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      password: password ?? this.password,
      status: status ?? super.status,
      error: error ?? this.error,
    );
  }
}
