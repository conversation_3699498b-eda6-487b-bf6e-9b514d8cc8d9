import 'package:formz/formz.dart';
import 'package:neuroworld/core/data/models/form_model.dart';
import 'package:neuroworld/core/data/validators/email_validator.dart';
import 'package:neuroworld/core/data/validators/password_validator.dart';

class LoginForm extends FormModel with FormzMixin {
  LoginForm({
    required super.key,
    super.status,
    required this.email,
    this.password = const PasswordInput.pure(),
    this.error,
  });

  final EmailInput email;
  final PasswordInput password;
  final Object? error;

  @override
  List<FormzInput> get inputs => [email, password];

  @override
  LoginForm copyWith({
    EmailInput? email,
    PasswordInput? password,
    FormzSubmissionStatus? status,
    Object? error,
  }) {
    return LoginForm(
      key: key,
      email: email ?? this.email,
      password: password ?? this.password,
      status: status ?? super.status,
      error: error ?? this.error,
    );
  }
}
