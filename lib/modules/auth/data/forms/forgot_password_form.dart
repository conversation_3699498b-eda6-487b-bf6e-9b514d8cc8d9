import 'package:formz/formz.dart';
import 'package:neuroworld/core/data/models/form_model.dart';
import 'package:neuroworld/core/data/validators/email_validator.dart';

class ForgotPasswordForm extends FormModel with FormzMixin {
  ForgotPasswordForm({
    required super.key,
    super.status,
    required this.email,
    this.error,
  });

  final EmailInput email;
  final Object? error;

  @override
  List<FormzInput> get inputs => [email];

  @override
  ForgotPasswordForm copyWith({
    EmailInput? email,
    FormzSubmissionStatus? status,
    Object? error,
  }) {
    return ForgotPasswordForm(
      key: key,
      email: email ?? this.email,
      status: status ?? super.status,
      error: error ?? this.error,
    );
  }
}
