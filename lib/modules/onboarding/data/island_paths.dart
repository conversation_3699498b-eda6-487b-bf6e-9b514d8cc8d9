import 'dart:ui';

final Map<String, Path> islandPaths = {
  "unwind": Path()
    ..moveTo(3.54874, 48.715)
    ..cubicTo(3.3117, 48.952, 3.12207, 49.4156, 3.12207, 49.7474)
    ..lineTo(3.12207, 60.5828)
    ..cubicTo(3.12207, 60.9147, 3.31697, 61.3835, 3.54874, 61.6153)
    ..lineTo(22.3697, 80.4363)
    ..cubicTo(22.6068, 80.6733, 22.7964, 81.1368, 22.7964, 81.4687)
    ..lineTo(22.7964, 86.3728)
    ..cubicTo(22.7964, 86.7047, 22.9913, 87.1735, 23.2231, 87.4052)
    ..lineTo(41.3435, 105.526)
    ..cubicTo(41.5806, 105.763, 42.0441, 105.952, 42.376, 105.952)
    ..lineTo(53.6538, 105.952)
    ..cubicTo(53.9857, 105.952, 54.4545, 105.757, 54.6862, 105.526)
    ..lineTo(66.9281, 93.2839)
    ..cubicTo(67.1651, 93.0468, 67.3547, 92.5833, 67.3547, 92.2514)
    ..lineTo(67.3547, 86.7047)
    ..cubicTo(67.3547, 86.3728, 67.6287, 86.0989, 67.9605, 86.0989)
    ..lineTo(85.3382, 86.0989)
    ..cubicTo(85.67, 86.0989, 86.1389, 85.904, 86.3706, 85.6722)
    ..lineTo(91.7014, 80.3414)
    ..cubicTo(91.9384, 80.1044, 92.1281, 79.6409, 92.1281, 79.309)
    ..lineTo(92.1281, 34.54)
    ..cubicTo(92.1281, 34.2081, 92.323, 33.7393, 92.5548, 33.5075)
    ..lineTo(98.9074, 27.1549)
    ..cubicTo(99.1445, 26.9178, 99.3341, 26.4543, 99.3341, 26.1224)
    ..lineTo(99.3341, 17.2887)
    ..cubicTo(99.3341, 16.9569, 99.0602, 16.6777, 98.7283, 16.6724)
    ..lineTo(96.0419, 16.6355)
    ..cubicTo(95.71, 16.6355, 95.1622, 16.625, 94.8303, 16.625)
    ..lineTo(76.0778, 16.625)
    ..cubicTo(75.746, 16.625, 75.2771, 16.8199, 75.0454, 17.0517)
    ..lineTo(70.1939, 21.9031)
    ..cubicTo(69.9569, 22.1401, 69.4934, 22.3298, 69.1615, 22.3298)
    ..lineTo(56.4825, 22.3298)
    ..cubicTo(56.1506, 22.3298, 55.6818, 22.5247, 55.45, 22.7564)
    ..lineTo(51.6469, 26.5596)
    ..cubicTo(51.4098, 26.7967, 50.9463, 26.9863, 50.6144, 26.9863)
    ..lineTo(38.0671, 26.9863)
    ..cubicTo(37.7352, 26.9863, 37.2664, 27.1812, 37.0346, 27.413)
    ..lineTo(25.3143, 39.1333)
    ..cubicTo(25.0773, 39.3703, 24.6137, 39.56, 24.2819, 39.56)
    ..lineTo(13.3095, 39.56)
    ..cubicTo(12.9777, 39.56, 12.5089, 39.7549, 12.2771, 39.9866)
    ..lineTo(3.55401, 48.7097)
    ..close()
    ..addRRect(RRect.fromRectAndCorners(
      Rect.fromLTWH(15, 2, 80, 20),
      bottomRight: Radius.circular(10),
      bottomLeft: Radius.circular(10),
      topLeft: Radius.circular(10),
      topRight: Radius.circular(10),
    ))
    ..moveTo(23, 17)
    ..lineTo(23, 10)
    ..lineTo(25, 10)
    ..lineTo(25, 17)
    ..cubicTo(25, 19, 26.5, 20, 27.8, 20)
    ..cubicTo(29, 20, 30, 19, 30, 17)
    ..lineTo(30, 10)
    ..lineTo(32, 10)
    ..lineTo(32, 17)
    ..cubicTo(32, 20, 30, 22, 27.8, 22)
    ..cubicTo(25.5, 22, 23, 20, 23, 17)
    ..close()
    ..moveTo(40, 22)
    ..lineTo(35, 14)
    ..lineTo(35, 22)
    ..lineTo(33, 22)
    ..lineTo(33, 10)
    ..lineTo(36, 10)
    ..lineTo(41, 18)
    ..lineTo(41, 10)
    ..lineTo(43, 10)
    ..lineTo(43, 22)
    ..lineTo(40, 22)
    ..close(),
  "restore": Path()
    ..moveTo(5.66715, 87.1885)
    ..cubicTo(5.43011, 86.9515, 5.24048, 86.4879, 5.24048, 86.1561)
    ..lineTo(5.24048, 33.9967)
    ..cubicTo(5.24048, 33.6648, 5.43538, 33.196, 5.66715, 32.9643)
    ..lineTo(12.2516, 26.3798)
    ..cubicTo(12.4886, 26.1428, 12.9522, 25.9531, 13.284, 25.9531)
    ..lineTo(44.6418, 25.9531)
    ..cubicTo(44.9737, 25.9531, 45.4425, 26.148, 45.6743, 26.3798)
    ..lineTo(51.5424, 32.2479)
    ..cubicTo(51.7794, 32.4849, 52.2429, 32.6745, 52.5748, 32.6745)
    ..lineTo(69.8998, 32.6745)
    ..cubicTo(70.2317, 32.6745, 70.7005, 32.8694, 70.9323, 33.1012)
    ..lineTo(76.2367, 38.4056)
    ..cubicTo(76.4737, 38.6427, 76.9373, 38.8323, 77.2691, 38.8323)
    ..lineTo(90.1062, 38.8323)
    ..cubicTo(90.4381, 38.8323, 90.9069, 39.0272, 91.1386, 39.259)
    ..lineTo(96.8434, 44.9638)
    ..cubicTo(97.0804, 45.2008, 97.544, 45.3904, 97.8759, 45.3904)
    ..lineTo(106.214, 45.3904)
    ..cubicTo(106.546, 45.3904, 107.015, 45.5853, 107.247, 45.8171)
    ..lineTo(120.937, 59.5075)
    ..cubicTo(121.174, 59.7445, 121.638, 59.9342, 121.97, 59.9342)
    ..lineTo(130.872, 59.9342)
    ..cubicTo(131.741, 59.9342, 132.447, 60.64, 132.447, 61.5092)
    ..lineTo(132.447, 68.1621)
    ..cubicTo(132.447, 68.494, 132.652, 68.947, 132.9, 69.1682)
    ..lineTo(143.166, 78.2547)
    ..cubicTo(143.414, 78.476, 143.619, 78.929, 143.619, 79.2609)
    ..lineTo(143.619, 89.8487)
    ..cubicTo(143.619, 90.1805, 143.424, 90.6493, 143.193, 90.8811)
    ..lineTo(137.736, 96.3383)
    ..cubicTo(137.498, 96.5753, 137.035, 96.765, 136.703, 96.765)
    ..lineTo(84.6227, 96.765)
    ..cubicTo(84.2908, 96.765, 83.822, 96.5701, 83.5902, 96.3383)
    ..lineTo(75.8943, 88.6424)
    ..cubicTo(75.6573, 88.4053, 75.1937, 88.2157, 74.8619, 88.2157)
    ..lineTo(62.0933, 88.2157)
    ..cubicTo(61.7614, 88.2157, 61.2926, 88.4106, 61.0608, 88.6424)
    ..lineTo(55.6089, 94.0943)
    ..cubicTo(55.3719, 94.3314, 54.9083, 94.521, 54.5765, 94.521)
    ..lineTo(13.6159, 94.521)
    ..cubicTo(13.284, 94.521, 12.8152, 94.3261, 12.5834, 94.0943)
    ..lineTo(5.6566, 87.1675)
    ..close()
    ..addRRect(RRect.fromRectAndCorners(
      Rect.fromLTWH(10, 2, 100, 20),
      bottomRight: Radius.circular(10),
      bottomLeft: Radius.circular(10),
      topLeft: Radius.circular(10),
      topRight: Radius.circular(10),
    ))
    ..moveTo(36, 17)
    ..lineTo(35, 17)
    ..lineTo(35, 22)
    ..lineTo(33, 22)
    ..lineTo(33, 10)
    ..lineTo(37, 10)
    ..cubicTo(39, 10, 41, 12, 41, 14)
    ..cubicTo(41, 15.5, 40, 17, 38, 17)
    ..lineTo(40, 22)
    ..lineTo(38, 22)
    ..lineTo(36, 17)
    ..close()
    ..moveTo(42, 22)
    ..lineTo(42, 10)
    ..lineTo(49, 10)
    ..lineTo(49, 12)
    ..lineTo(44, 12)
    ..lineTo(44, 15)
    ..lineTo(48, 15)
    ..lineTo(48, 17)
    ..lineTo(44, 17)
    ..lineTo(44, 20)
    ..lineTo(49, 20)
    ..lineTo(49, 22)
    ..close()
    ..moveTo(65, 12)
    ..lineTo(65, 22)
    ..lineTo(63, 22)
    ..lineTo(63, 12)
    ..lineTo(60, 12)
    ..lineTo(60, 10)
    ..lineTo(69, 10)
    ..lineTo(69, 12)
    ..close(),
  "exercise": Path()
    ..moveTo(0, 0)
    ..cubicTo(112.901, 71.6386, 111.837, 71.4753, 111.168, 71.4753)
    ..lineTo(105.711, 71.4753)
    ..cubicTo(105.042, 71.4753, 104.115, 71.0908, 103.641, 70.6167)
    ..lineTo(87.3585, 54.3347)
    ..cubicTo(86.8897, 53.8659, 86.2312, 53.0073, 85.8994, 52.4278)
    ..lineTo(82.8337, 47.0918)
    ..cubicTo(82.5018, 46.5124, 81.875, 45.6274, 81.443, 45.1217)
    ..lineTo(58.1867, 17.8621)
    ..cubicTo(57.7548, 17.3564, 57.4019, 16.3924, 57.4019, 15.7287)
    ..lineTo(57.4019, 12.5735)
    ..cubicTo(57.4019, 11.9045, 57.0173, 10.9774, 56.5433, 10.5033)
    ..lineTo(51.0861, 5.04611)
    ..cubicTo(50.6172, 4.57729, 49.6849, 4.1875, 49.0159, 4.1875)
    ..lineTo(45.139, 4.1875)
    ..cubicTo(44.47, 4.1875, 43.6693, 4.66684, 43.3533, 5.25154)
    ..lineTo(39.4922, 12.4049)
    ..cubicTo(39.1761, 12.9896, 38.918, 14.0168, 38.918, 14.6857)
    ..lineTo(38.918, 23.7091)
    ..cubicTo(38.918, 24.3781, 38.5335, 25.3052, 38.0594, 25.7792)
    ..lineTo(32.8972, 30.9414)
    ..cubicTo(32.4284, 31.4103, 32.0386, 32.3426, 32.0386, 33.0116)
    ..lineTo(32.0386, 43.9892)
    ..cubicTo(32.0386, 44.6582, 31.654, 45.5853, 31.1799, 46.0594)
    ..lineTo(28.1932, 49.0461)
    ..cubicTo(27.7244, 49.5149, 27.3346, 50.4472, 27.3346, 51.1162)
    ..lineTo(27.3346, 56.7156)
    ..cubicTo(27.3346, 57.3846, 26.9501, 58.3117, 26.476, 58.7858)
    ..lineTo(24.8799, 60.3819)
    ..cubicTo(24.4111, 60.8507, 23.8317, 61.7514, 23.5999, 62.373)
    ..lineTo(20.1707, 71.5544)
    ..cubicTo(19.939, 72.1812, 19.3595, 73.0767, 18.8907, 73.5455)
    ..lineTo(18.6484, 73.7878)
    ..cubicTo(18.1796, 74.2566, 17.7898, 75.189, 17.7898, 75.858)
    ..lineTo(17.7898, 86.7197)
    ..cubicTo(17.7898, 87.3887, 18.1743, 88.3157, 18.6484, 88.7898)
    ..lineTo(22.9151, 93.0565)
    ..cubicTo(23.3839, 93.5254, 23.7737, 94.4577, 23.7737, 95.1267)
    ..lineTo(23.7737, 116.239)
    ..cubicTo(23.7737, 116.908, 24.1583, 117.835, 24.6324, 118.309)
    ..lineTo(32.1597, 125.837)
    ..cubicTo(32.6285, 126.305, 33.5609, 126.695, 34.2299, 126.695)
    ..lineTo(44.8229, 126.695)
    ..cubicTo(45.4919, 126.695, 46.419, 126.311, 46.8878, 125.837)
    ..lineTo(63.4964, 109.159)
    ..cubicTo(63.9652, 108.685, 64.8976, 108.301, 65.5613, 108.301)
    ..lineTo(73.0307, 108.301)
    ..cubicTo(73.6997, 108.301, 74.6268, 107.916, 75.1009, 107.442)
    ..lineTo(85.4411, 97.102)
    ..cubicTo(85.9099, 96.6332, 86.2997, 95.7009, 86.2997, 95.0319)
    ..lineTo(86.2997, 92.9565)
    ..cubicTo(86.2997, 92.2875, 86.6843, 91.3604, 87.1583, 90.8863)
    ..lineTo(90.7561, 87.2886)
    ..cubicTo(91.2249, 86.8198, 92.1573, 86.43, 92.8262, 86.43)
    ..lineTo(103.72, 86.43)
    ..cubicTo(104.389, 86.43, 105.316, 86.8145, 105.79, 87.2886)
    ..lineTo(108.244, 89.7432)
    ..cubicTo(108.713, 90.2121, 109.646, 90.6019, 110.315, 90.6019)
    ..lineTo(112.364, 90.6019)
    ..cubicTo(113.033, 90.6019, 113.96, 90.2173, 114.434, 89.7432)
    ..lineTo(118.374, 85.8031)
    ..cubicTo(118.843, 85.3343, 119.233, 84.402, 119.233, 83.733)
    ..lineTo(119.233, 74.8361)
    ..cubicTo(119.233, 74.1671, 118.711, 73.4612, 118.074, 73.2611)
    ..lineTo(113.538, 71.8388)
    ..close(),
  "optimize": Path()
    ..moveTo(0, 0)
    ..cubicTo(51.5275, 4.85267, 51.9173, 4.56823, 52.2439, 4.52609)
    ..lineTo(100.308, 4.52626)
    ..cubicTo(100.64, 4.48412, 101.991, 4.96179, 102.155, 5.25151)
    ..lineTo(112.758, 17.2631)
    ..cubicTo(112.921, 17.5528, 113.053, 18.0637, 113.053, 18.3956)
    ..lineTo(113.053, 55.869)
    ..cubicTo(113.053, 56.2009, 112.858, 56.6697, 112.626, 56.9015)
    ..lineTo(106.837, 62.6905)
    ..cubicTo(106.6, 62.9276, 106.41, 63.3911, 106.41, 63.723)
    ..lineTo(106.41, 70.3443)
    ..cubicTo(106.41, 70.6761, 106.215, 71.1449, 105.984, 71.3767)
    ..lineTo(93.0991, 84.2612)
    ..cubicTo(92.862, 84.4982, 92.3985, 84.6878, 92.0666, 84.6878)
    ..lineTo(78.1603, 84.6878)
    ..cubicTo(77.8284, 84.6878, 77.3596, 84.8827, 77.1278, 85.1145)
    ..lineTo(69.1317, 93.1107)
    ..cubicTo(68.8946, 93.3477, 68.4311, 93.5373, 68.0992, 93.5373)
    ..lineTo(53.4554, 93.5373)
    ..cubicTo(53.1235, 93.5373, 52.6547, 93.7322, 52.4229, 93.964)
    ..lineTo(49.4784, 96.9086)
    ..cubicTo(49.2413, 97.1456, 48.7778, 97.3353, 48.4459, 97.3353)
    ..lineTo(17.204, 97.3353)
    ..cubicTo(16.8722, 97.3353, 16.4034, 97.1404, 16.1716, 96.9086)
    ..lineTo(7.94892, 88.6859)
    ..cubicTo(7.71188, 88.4489, 7.52225, 87.9853, 7.52225, 87.6535)
    ..lineTo(7.52225, 81.127)
    ..cubicTo(7.52225, 80.7951, 7.32734, 80.3263, 7.09556, 80.0945)
    ..lineTo(2.64976, 75.6487)
    ..cubicTo(2.41272, 75.4117, 2.22307, 74.9481, 2.22307, 74.6163)
    ..lineTo(2.22307, 64.7449)
    ..cubicTo(2.22307, 64.413, 2.3811, 63.9178, 2.57073, 63.6439)
    ..lineTo(12.5011, 54.8212)
    ..cubicTo(12.6908, 54.5473, 14.6709, 54.8212, 15.0028, 54.8212)
    ..lineTo(35.3414, 54.7021)
    ..cubicTo(35.6732, 54.7021, 38.5165, 53.5744, 38.7957, 53.3901)
    ..lineTo(47.3239, 42.1523)
    ..cubicTo(47.6031, 41.9679, 47.8296, 41.5465, 47.8296, 41.2147)
    ..lineTo(47.8296, 13.3176)
    ..cubicTo(47.8296, 12.9858, 47.9455, 12.4643, 48.0877, 12.1641)
    ..lineTo(51.4063, 5.14766)
    ..close(),
  "nutrition": Path()
    ..moveTo(138.229, 20.9572)
    ..cubicTo(137.76, 20.4884, 137.091, 19.6351, 136.743, 19.0662)
    ..lineTo(133.725, 14.0831)
    ..cubicTo(133.377, 13.5142, 132.856, 12.5555, 132.566, 11.9602)
    ..lineTo(129.532, 5.76031)
    ..cubicTo(129.237, 5.15981, 128.452, 4.66992, 127.788, 4.66992)
    ..lineTo(97.879, 4.66992)
    ..cubicTo(97.2101, 4.66992, 96.283, 5.05446, 95.8089, 5.52854)
    ..lineTo(90.9311, 10.4063)
    ..cubicTo(90.4623, 10.8751, 89.5299, 11.2649, 88.861, 11.2649)
    ..lineTo(81.3073, 11.2649)
    ..cubicTo(80.6383, 11.2649, 80.0957, 11.8127, 80.0957, 12.4765)
    ..lineTo(80.0957, 17.2278)
    ..cubicTo(80.0957, 17.8968, 79.7112, 18.8239, 79.2371, 19.298)
    ..lineTo(71.2515, 27.2836)
    ..cubicTo(70.7827, 27.7524, 69.8503, 28.1422, 69.1813, 28.1422)
    ..lineTo(62.8445, 28.1422)
    ..cubicTo(62.1755, 28.1422, 61.6435, 28.69, 61.6593, 29.3537)
    ..lineTo(61.6961, 31.1131)
    ..cubicTo(61.7119, 31.7821, 61.4854, 32.8145, 61.1905, 33.415)
    ..lineTo(57.9772, 39.9995)
    ..cubicTo(57.6875, 40.6, 57.0607, 41.4744, 56.5866, 41.9432)
    ..lineTo(53.6157, 44.9141)
    ..cubicTo(53.1469, 45.3829, 52.7571, 46.3153, 52.7571, 46.9843)
    ..lineTo(52.7571, 59.7001)
    ..cubicTo(52.7571, 60.3691, 53.1416, 61.2962, 53.6157, 61.7703)
    ..lineTo(62.9182, 71.0728)
    ..cubicTo(63.387, 71.5416, 63.7768, 72.474, 63.7768, 73.143)
    ..lineTo(63.7768, 79.6537)
    ..cubicTo(63.7768, 80.3227, 64.1614, 81.2498, 64.6354, 81.7238)
    ..lineTo(75.8027, 92.8911)
    ..cubicTo(76.2715, 93.3599, 77.2038, 93.7497, 77.8728, 93.7497)
    ..lineTo(95.9406, 93.7497)
    ..cubicTo(96.6095, 93.7497, 97.5366, 93.3652, 98.0107, 92.8911)
    ..lineTo(102.341, 88.5611)
    ..cubicTo(102.809, 88.0923, 103.742, 87.7025, 104.411, 87.7025)
    ..lineTo(110.505, 87.7025)
    ..cubicTo(111.174, 87.7025, 112.101, 87.318, 112.576, 86.8439)
    ..lineTo(121.941, 77.4782)
    ..cubicTo(122.41, 77.0094, 122.8, 76.077, 122.8, 75.408)
    ..lineTo(122.8, 63.3084)
    ..cubicTo(122.8, 62.6394, 123.184, 61.7124, 123.658, 61.2383)
    ..lineTo(133.209, 51.6882)
    ..cubicTo(133.677, 51.2194, 134.067, 50.287, 134.067, 49.618)
    ..lineTo(134.067, 46.2363)
    ..cubicTo(134.067, 45.5673, 134.452, 44.6402, 134.926, 44.1661)
    ..lineTo(141.226, 37.8661)
    ..cubicTo(141.695, 37.3973, 142.084, 36.4649, 142.084, 35.796)
    ..lineTo(142.084, 26.0194)
    ..cubicTo(142.084, 25.3504, 141.7, 24.4233, 141.226, 23.9492)
    ..lineTo(138.234, 20.9572)
    ..close()
    ..addRRect(RRect.fromRectAndCorners(
      Rect.fromLTWH(10, 70, 110, 25),
      bottomRight: Radius.circular(10),
      bottomLeft: Radius.circular(10),
      topLeft: Radius.circular(10),
      topRight: Radius.circular(10),
    ))
    ..moveTo(23, 96)
    ..lineTo(19, 89)
    ..lineTo(19, 96)
    ..lineTo(17, 96)
    ..lineTo(17, 86)
    ..lineTo(19, 86)
    ..lineTo(24, 93)
    ..lineTo(24, 86)
    ..lineTo(26, 86)
    ..lineTo(26, 96)
    ..close()
    ..moveTo(27, 93)
    ..lineTo(27, 86)
    ..lineTo(29, 86)
    ..lineTo(29, 92)
    ..cubicTo(29, 94, 30, 95, 31, 95)
    ..cubicTo(32, 95, 33, 94, 33, 92)
    ..lineTo(33, 86)
    ..lineTo(36, 86)
    ..lineTo(36, 93)
    ..cubicTo(36, 96, 34, 97, 31, 97)
    ..cubicTo(28, 97, 27, 96, 27, 93)
    ..close()
    ..moveTo(42, 88)
    ..lineTo(42, 96)
    ..lineTo(40, 96)
    ..lineTo(40, 88)
    ..lineTo(37, 88)
    ..lineTo(37, 86)
    ..lineTo(46, 86)
    ..lineTo(46, 88)
    ..close(),
};
