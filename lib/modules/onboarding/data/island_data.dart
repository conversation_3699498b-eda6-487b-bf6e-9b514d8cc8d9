import 'dart:ui';

import 'package:neuroworld/core/constants/gen/assets.gen.dart';

class IslandData {
  final Offset position;
  final Size size;
  final String selectedImage;

  IslandData({
    required this.position,
    required this.size,
    required this.selectedImage,
  });
}

final Map<String, IslandData> islandData = {
  "unwind": IslandData(
    position: Offset(50.12, 9.0),
    size: <PERSON><PERSON>(120, 66),
    selectedImage: Assets.svgs.onboarding.unwindSelected.path,
  ),
  "restore": IslandData(
    position: Offset(240.24, 0.5),
    size: <PERSON><PERSON>(120, 66),
    selectedImage: Assets.svgs.onboarding.restoreSelected.path,
  ),
  "exercise": IslandData(
    position: Offset(38.0, 120.0),
    size: <PERSON><PERSON>(120, 66),
    selectedImage: Assets.svgs.onboarding.exerciseSelected.path,
  ),
  "optimize": IslandData(
    position: Offset(262.22, 115.91),
    size: <PERSON><PERSON>(120, 66),
    selectedImage: Assets.svgs.onboarding.optimizeSelected.path,
  ),
  "nutrition": IslandD<PERSON>(
    position: Offset(85.12, 200.07),
    size: <PERSON><PERSON>(120, 66),
    selectedImage: Assets.svgs.onboarding.nutritionSelected.path,
  ),
};
