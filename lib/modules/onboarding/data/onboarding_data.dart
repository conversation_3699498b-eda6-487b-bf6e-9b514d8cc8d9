import 'package:flutter/material.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

enum OnboardingStepType {
  standard,
  questionnaire,
  questionnaireCircular,
  tooltip,
  mapView,
  animation,
}

class ChatBubbleData {
  final String content;
  final String avatarPath;

  const ChatBubbleData({
    required this.content,
    required this.avatarPath,
  });
}

class TooltipData {
  final String content;
  final Alignment position;
  final Widget target;
  final double width;
  final double height;

  const TooltipData({
    required this.content,
    required this.position,
    required this.target,
    this.width = 300.0,
    this.height = 173.0,
  });
}

enum OnboardingData {
  step1(
    type: OnboardingStepType.standard,
    animation: 'assets/lottie/myla.json',
    questions: [],
    tooltip: null,
  ),
  step2(
    type: OnboardingStepType.animation,
    animation: '',
    image: 'assets/svgs/onboarding/map.svg',
    questions: [],
    tooltip: null,
    chatBubble: ChatBubbleData(
      content:
          "The NEURO Islands are where you'll unlock habits, gather resources for your NEURO City, and learn how to live a brain-heathy life!\n\nLet's meet the inhabitants!",
      avatarPath: 'assets/svgs/avatars/myla_wave.svg',
    ),
  ),
  step3(
    type: OnboardingStepType.animation,
    animation: '',
    image: 'assets/svgs/onboarding/map_nutrition.svg',
    questions: [],
    tooltip: null,
    animationPath: 'assets/svgs/onboarding/nutrition_island.svg',
    shouldBlur: true,
    chatBubble: ChatBubbleData(
      content:
          "Hi! I'm Nuri, Nutrition Island's head forager and plant-powered chef. I'm here to help you fuel your brain with delicious, nourishing meals!",
      avatarPath: 'assets/svgs/avatars/nuri.svg',
    ),
  ),
  step4(
    type: OnboardingStepType.animation,
    animation: '',
    image: 'assets/svgs/onboarding/map_exercise.svg',
    questions: [],
    tooltip: null,
    animationPath: 'assets/svgs/onboarding/exercise_island.svg',
    shouldBlur: true,
    chatBubble: ChatBubbleData(
      content:
          "Hey! I'm Spark. Whether it's climbing, running, or hanging from a branch mid-crunch, I'm all about staying active. Stop by Exercise Island any time for a workout!",
      avatarPath: 'assets/svgs/avatars/spark.svg',
    ),
  ),
  step5(
    type: OnboardingStepType.animation,
    animation: '',
    image: 'assets/svgs/onboarding/map_unwind.svg',
    questions: [],
    tooltip: null,
    animationPath: 'assets/svgs/onboarding/unwind_island.svg',
    shouldBlur: true,
    chatBubble: ChatBubbleData(
      content:
          "Hello! I'm Flo. Life can get overwhelming sometimes! But I've learned how we can manage stress and relax. Whenever you're on Unwind Island, we can breathe, reflect, and find calm together.",
      avatarPath: 'assets/svgs/avatars/flo.svg',
    ),
  ),
  step6(
    type: OnboardingStepType.animation,
    animation: '',
    image: 'assets/svgs/onboarding/map_restore.svg',
    questions: [],
    tooltip: null,
    animationPath: 'assets/svgs/onboarding/restore_island.svg',
    shouldBlur: true,
    chatBubble: ChatBubbleData(
      content:
          "Hiya! I'm Luna. Sleep is my superpower — and yours, too, if you make a habit of it! Visit Restore Island, and we can work on nighttime routines that help your brain recharge and thrive.",
      avatarPath: 'assets/svgs/avatars/luna.svg',
    ),
  ),
  step7(
    type: OnboardingStepType.animation,
    animation: '',
    image: 'assets/svgs/onboarding/map_optimize.svg',
    questions: [],
    tooltip: null,
    animationPath: 'assets/svgs/onboarding/optimize_island.svg',
    shouldBlur: true,
    chatBubble: ChatBubbleData(
      content:
          "How's it going? I'm Sofi! I run Optimize Island, where you can train your brain with a library of games, or pick up new cognitively-stimulating habits to enjoy.",
      avatarPath: 'assets/svgs/avatars/sophi.svg',
    ),
  ),
  step8(
    type: OnboardingStepType.mapView,
    animation: '',
    questions: [],
    tooltip: null,
  );

  const OnboardingData({
    required this.type,
    required this.animation,
    required this.questions,
    required this.tooltip,
    this.image,
    this.chatBubble,
    this.animationPath,
    this.shouldBlur = false,
  });

  final OnboardingStepType type;
  final String animation;
  final String? image;
  final List<String> questions;
  final TooltipData? tooltip;
  final ChatBubbleData? chatBubble;
  final String? animationPath;
  final bool shouldBlur;

  RelativeRect? buildRelativeRect(BoxConstraints constraints, double svgSize) {
    return switch (this) {
      OnboardingData.step1 => null,
      OnboardingData.step2 => null,
      OnboardingData.step3 => RelativeRect.fromLTRB(
          100,
          constraints.maxHeight - svgSize,
          constraints.maxWidth - svgSize,
          0,
        ),
      OnboardingData.step4 => RelativeRect.fromLTRB(
          0,
          (constraints.maxHeight - svgSize) / 2 + 25,
          240,
          (constraints.maxHeight - svgSize) / 2 + 25,
        ),
      OnboardingData.step5 => RelativeRect.fromLTRB(
          100,
          10,
          constraints.maxWidth - svgSize - 15,
          constraints.maxHeight - svgSize,
        ),
      OnboardingData.step6 => RelativeRect.fromLTRB(
          constraints.maxWidth - svgSize - 35,
          -40,
          65,
          constraints.maxHeight - svgSize,
        ),
      OnboardingData.step7 => RelativeRect.fromLTRB(
          constraints.maxWidth - svgSize,
          140,
          0,
          constraints.maxHeight - svgSize - 60,
        ),
      OnboardingData.step8 => null,
    };
  }

  /// Get localized title
  String title(BuildContext context) {
    return switch (this) {
      OnboardingData.step1 => context.L.onboardingStepOneTitle,
      OnboardingData.step2 => "NEURO World",
      OnboardingData.step3 => "Nutrition Island",
      OnboardingData.step4 => "Exercise Island",
      OnboardingData.step5 => "Unwind Island",
      OnboardingData.step6 => "Restore Island",
      OnboardingData.step7 => "Optimize Island",
      OnboardingData.step8 => context.L.selectIsland,
    };
  }

  /// Get localized description
  String description(BuildContext context) {
    return switch (this) {
      OnboardingData.step1 => context.L.onboardingStepOneDescription,
      OnboardingData.step2 => "",
      OnboardingData.step3 => "",
      OnboardingData.step4 => "",
      OnboardingData.step5 => "",
      OnboardingData.step6 => "",
      OnboardingData.step7 => "",
      OnboardingData.step8 => context.L.onboardingStepThreeDescription,
    };
  }

  /// Get localized button text
  String buttonText(BuildContext context) {
    return switch (this) {
      OnboardingData.step1 => context.L.letsGetStarted,
      OnboardingData.step2 => context.L.letsGetStarted,
      OnboardingData.step3 => context.L.next,
      OnboardingData.step4 => context.L.next,
      OnboardingData.step5 => context.L.next,
      OnboardingData.step6 => context.L.next,
      OnboardingData.step7 => context.L.next,
      OnboardingData.step8 => context.L.next,
    };
  }

  static OnboardingData forStep(int step) {
    return switch (step) {
      0 => OnboardingData.step1,
      1 => OnboardingData.step2,
      2 => OnboardingData.step3,
      3 => OnboardingData.step4,
      4 => OnboardingData.step5,
      5 => OnboardingData.step6,
      6 => OnboardingData.step7,
      7 => OnboardingData.step8,
      _ => throw ArgumentError('Invalid onboarding step: $step'),
    };
  }
}
