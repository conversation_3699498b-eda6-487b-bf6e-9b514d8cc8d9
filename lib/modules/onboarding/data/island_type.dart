import 'package:neuroworld/core/constants/neuro.dart';

extension IslandTypeExtension on Neuro {
  String get characterName {
    switch (this) {
      case Neuro.nutrition:
        return '<PERSON><PERSON>';
      case Neuro.exercise:
        return 'Spark';
      case Neuro.unwind:
        return 'Flo';
      case Neuro.restore:
        return '<PERSON>';
      case Neuro.optimize:
        return '<PERSON>phi';
    }
  }
}
