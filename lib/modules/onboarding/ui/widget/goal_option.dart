import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/auth/ui/widgets/brain_assistant.dart';

class GoalOption extends StatefulWidget {
  final String avatarPath;
  final List<String> options;
  final AvatarPosition avatarPosition;
  final double avatarSize;
  final void Function(int selectedIndex)? onSelected;

  const GoalOption({
    super.key,
    required this.avatarPath,
    required this.options,
    this.avatarPosition = AvatarPosition.leftBottom,
    this.avatarSize = 80,
    this.onSelected,
  });

  @override
  State<GoalOption> createState() => _GoalOptionState();
}

class _GoalOptionState extends State<GoalOption> {
  int? selectedIndex;

  @override
  Widget build(BuildContext context) {
    final bool isAnySelected = selectedIndex != null;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        crossAxisAlignment: _getAlignment(),
        children: [
          SizedBox(
            width: widget.avatarSize,
            height: widget.avatarSize,
            child: SvgPicture.asset(widget.avatarPath),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.brainBg,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: List.generate(widget.options.length, (index) {
                  bool isSelected = selectedIndex == index;
                  bool isDisabled = isAnySelected && !isSelected;

                  return GestureDetector(
                    onTap: () {
                      if (!isAnySelected) {
                        setState(() {
                          selectedIndex = index;
                        });
                        widget.onSelected?.call(index);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      margin: const EdgeInsets.symmetric(vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: Colors.grey.shade300,
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.all(12),
                              child: Text(
                                widget.options[index],
                                style: TextStyles.body3.copyWith(
                                  color: isDisabled
                                      ? AppColors.textSecondary
                                      : AppColors.textPrimary,
                                  overflow: TextOverflow.visible,
                                ),
                              ),
                            ),
                          ),
                          SvgPicture.asset(
                            isSelected
                                ? Assets.svgs.onboarding.radioSelected.path
                                : isDisabled
                                    ? Assets.svgs.onboarding.radioDisabled.path
                                    : Assets
                                        .svgs.onboarding.radioUnselected.path,
                            width: 24,
                            height: 24,
                          ),
                        ],
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  CrossAxisAlignment _getAlignment() {
    switch (widget.avatarPosition) {
      case AvatarPosition.leftTop:
        return CrossAxisAlignment.start;
      case AvatarPosition.leftCenter:
        return CrossAxisAlignment.center;
      case AvatarPosition.leftBottom:
        return CrossAxisAlignment.end;
    }
  }
}
