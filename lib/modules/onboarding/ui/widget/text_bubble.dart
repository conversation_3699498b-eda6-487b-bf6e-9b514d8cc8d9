import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class TextBubble extends StatefulWidget {
  final String message;

  const TextBubble({super.key, required this.message});

  @override
  State<TextBubble> createState() => _TextBubbleState();
}

class _TextBubbleState extends State<TextBubble> {
  late final String bubbleText;

  @override
  void initState() {
    super.initState();
    bubbleText = widget.message;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(64, 0, 24, 0),
      // padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Align(
        alignment: Alignment.centerRight,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.unwindOrange,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            bubbleText,
            style: TextStyles.body3.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ),
    );
  }
}
