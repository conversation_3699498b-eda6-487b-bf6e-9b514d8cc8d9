import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/secondary_button.dart';
import 'package:neuroworld/modules/auth/ui/widgets/brain_assistant.dart';

class DaysSelection extends HookWidget {
  final String avatarPath;
  final AvatarPosition avatarPosition;
  final String confirmButtonText;
  final ValueChanged<int> onConfirm;

  const DaysSelection({
    super.key,
    required this.avatarPath,
    required this.onConfirm,
    this.avatarPosition = AvatarPosition.leftBottom,
    this.confirmButtonText = "Confirm Days",
  });

  @override
  Widget build(BuildContext context) {
    final selectedDays = useState(1);
    final isSliderEnabled = useState(true);
    final isConfirmDisabled = useState(false);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        crossAxisAlignment: _getAlignment(),
        children: [
          SizedBox(
            width: 58,
            height: 58,
            child: SvgPicture.asset(avatarPath),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Stack(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.brainBg,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: AppColors.stroke),
                        ),
                        child: Column(
                          children: [
                            SliderTheme(
                              data: SliderTheme.of(context).copyWith(
                                trackHeight: 8,
                                activeTrackColor: AppColors.secondaryBlue,
                                inactiveTrackColor:
                                    AppColors.stroke.withValues(alpha: 0.5),
                                disabledActiveTickMarkColor:
                                    AppColors.secondaryBlue,
                                disabledInactiveTickMarkColor:
                                    AppColors.secondaryBlue,
                                thumbColor: AppColors.secondaryBlue,
                                disabledThumbColor: AppColors.secondaryBlue,
                                thumbShape: const RoundSliderThumbShape(
                                    enabledThumbRadius: 10),
                                activeTickMarkColor: AppColors.secondaryBlue,
                                inactiveTickMarkColor: AppColors.secondaryBlue,
                                disabledActiveTrackColor:
                                    AppColors.secondaryBlue,
                              ),
                              child: Slider(
                                min: 1,
                                max: 7,
                                divisions: 6,
                                value: selectedDays.value.toDouble(),
                                onChanged: isSliderEnabled.value
                                    ? (val) => selectedDays.value = val.toInt()
                                    : null,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(23, 0, 23, 16),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: List.generate(7, (index) {
                                  final dayVal = index + 1;
                                  return Text(
                                    "$dayVal",
                                    style: TextStyles.tooltipHeading.copyWith(
                                      color: selectedDays.value == dayVal
                                          ? AppColors.textSecondary
                                          : AppColors.inputInactiveText,
                                    ),
                                  );
                                }),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: SecondaryButton(
                          height: 36,
                          onPressed: () => {
                            if (!isConfirmDisabled.value)
                              {
                                onConfirm(selectedDays.value),
                                isSliderEnabled.value = false,
                                isConfirmDisabled.value = true,
                              }
                          },
                          backgroundColor: Colors.white,
                          padding: EdgeInsets.zero,
                          disabled: isConfirmDisabled.value,
                          child: Text(
                            confirmButtonText,
                            style: TextStyles.subtitle2.copyWith(
                              color: isConfirmDisabled.value
                                  ? AppColors.buttonDisabled
                                  : AppColors.textSecondary,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  CrossAxisAlignment _getAlignment() {
    switch (avatarPosition) {
      case AvatarPosition.leftTop:
        return CrossAxisAlignment.start;
      case AvatarPosition.leftCenter:
        return CrossAxisAlignment.center;
      case AvatarPosition.leftBottom:
        return CrossAxisAlignment.end;
    }
  }
}
