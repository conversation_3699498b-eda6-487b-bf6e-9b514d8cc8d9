import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/data/models/request/set_goal_request.dart';
import 'package:neuroworld/modules/goals/data/models/response/set_goal_response.dart';
import 'package:neuroworld/modules/goals/data/services/goals_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'set_goal_controller.g.dart';

@riverpod
class SetGoalController extends _$SetGoalController {
  @override
  FutureOr<SetGoalResponse?> build() => null;

  Future<SetGoalResponse?> setGoal({
    required SetGoalRequest request,
  }) async {
    state = await AsyncValue.guard(() async {
      final result = await ref.read(goalsServiceProvider).setGoal(request);
      ref.read(authStateProvider.notifier).updateUser(result.user);
      return result;
    });

    return state.valueOrNull;
  }
}
