import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/modules/auth/ui/providers/sign_out_provider.dart';
import 'package:neuroworld/modules/home_shell/ui/utils/tab_item.dart';
import 'package:neuroworld/modules/home_shell/ui/widgets/app_bar.dart';
import 'package:neuroworld/modules/home_shell/ui/widgets/bottom_nav_bar.dart';
import 'package:neuroworld/modules/home_shell/ui/widgets/drawer.dart';
import 'package:neuroworld/modules/home_shell/ui/widgets/floating_action_button.dart';
import 'package:neuroworld/modules/notification/data/models/notification.dart';
import 'package:neuroworld/modules/notification/data/providers/fcm_providers.dart';
import 'package:neuroworld/modules/notification/data/services/messaging_service.dart';

/// Builds the "shell" for the home by building a Scaffold with a persistent
/// BottomNavigationBar or similar, where [navigationShell] is placed in the body of the Scaffold.
class HomeShellScreen extends ConsumerWidget {
  const HomeShellScreen({required this.navigationShell, Key? key})
      : super(key: key ?? const ValueKey<String>('HomeShellScreen'));

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.easyListen(signOutProvider, loadingText: context.L.logoutLoading);

    ref.listen(onMessageProvider, (previous, next) {
      next.whenData(
        (message) {
          ref.read(messagingServiceProvider).showRemoteNotification(message);
        },
      );
    });

    ref.listen(
      tappedNotificationProvider,
      (previous, next) {
        if (next != null) {
          if (next.routeLocation != null) context.go(next.routeLocation!);
        }
      },
    );

    void onSelectTab(TabItem tab) {
      // When navigating to a new branch, it's recommended to use the goBranch
      // method, as doing so makes sure the last navigation state of the
      // Navigator for the branch is restored.
      navigationShell.goBranch(
        tab.index,
        // A common pattern when using bottom navigation bars is to support
        // navigating to the initial location when tapping the item that is
        // already active. This demonstrates how to support this behavior,
        // using the initialLocation parameter of goBranch:
        initialLocation: tab.index == navigationShell.currentIndex,
      );
    }

    return PopScope(
      canPop: TabItem.values[navigationShell.currentIndex] == TabItem.home,
      onPopInvokedWithResult: (canPop, _) {
        // this will prevent popping when tab isn't (Home) & instead will pop to home.
        if (!canPop) navigationShell.goBranch(TabItem.home.index);
      },
      child: NavigationBarTheme(
        data: homeShellButtonNavBarThemeData,
        child: Scaffold(
          // using single persistent AppBar for all tabs and update it according to current location.
          // this is necessary to avoid using nested scaffolds as it's discouraged by flutter
          appBar: navigationShell.currentLocationHasAppBar
              ? HomeShellAppBar(
                  toolbarHeight: navigationShell.currentLocationAppBarHeight,
                )
              : null,
          body: navigationShell,
          bottomNavigationBar: HomeShellBottomNavBar(
            currentTab: TabItem.values[navigationShell.currentIndex],
            onSelectTab: onSelectTab,
          ),
          floatingActionButton:
              navigationShell.currentLocationHasFAB ? HomeShellFAB() : null,
          drawer: navigationShell.currentLocationHasDrawer
              ? HomeShellDrawer()
              : null,
        ),
      ),
    );
  }
}
