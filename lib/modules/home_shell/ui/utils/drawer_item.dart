import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/widgets/rounded_icon.dart';
import 'package:neuroworld/modules/subscriptions/ui/utils/subscription_helpers.dart';

enum DrawerItem {
  profile,
  susbcription,
  feedback,
  badges,
  deleteAccount,
  logout;

  const DrawerItem();

  Widget get icon =>
      RoundedIcon(size: 33, child: SvgPicture.asset(_getDrawerIconPath()));

  Widget? getTrailing(WidgetRef ref) =>
      this == DrawerItem.susbcription ? _buildSubscriptionTrailing(ref) : null;

  Widget _buildSubscriptionTrailing(WidgetRef ref) {
    return SubscriptionHelpers.buildSubscriptionBadge(ref);
  }

  String getDrawerItemLabel(BuildContext context) {
    return switch (this) {
      DrawerItem.profile => context.L.menuItemProfile,
      DrawerItem.susbcription => context.L.menuItemSubscription,
      DrawerItem.feedback => context.L.menuItemFeedback,
      DrawerItem.badges => context.L.badges,
      DrawerItem.deleteAccount => context.L.menuItemDeleteAccount,
      DrawerItem.logout => context.L.menuItemLogout,
    };
  }

  String _getDrawerIconPath() {
    return switch (this) {
      DrawerItem.profile => Assets.svgs.menuDrawer.profile.path,
      DrawerItem.susbcription => Assets.svgs.menuDrawer.subscription.path,
      DrawerItem.feedback => Assets.svgs.menuDrawer.feedback.path,
      DrawerItem.badges => Assets.svgs.menuDrawer.badges.path,
      DrawerItem.deleteAccount => Assets.svgs.menuDrawer.deleteAccount.path,
      DrawerItem.logout => Assets.svgs.menuDrawer.logout.path,
    };
  }
}
