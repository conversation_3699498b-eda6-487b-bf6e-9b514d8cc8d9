import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

enum TabItem {
  home,
  goals,
  map,
  summit;

  const TabItem();

  Widget get icon => SvgPicture.asset(
        _getTabIconPath(),
        height: 22,
        width: 22,
      );

  Widget get selectedIcon => SvgPicture.asset(
        _getSelecTedTabIconPath(),
        height: 22,
        width: 22,
      );

  String getTabItemLabel(BuildContext context) {
    return switch (this) {
      TabItem.home => context.L.homeTab,
      TabItem.goals => context.L.goalsTab,
      TabItem.map => context.L.mapTab,
      TabItem.summit => context.L.summitTab,
    };
  }

  String _getTabIconPath() {
    return switch (this) {
      TabItem.home => Assets.svgs.bottomNav.home.path,
      TabItem.goals => Assets.svgs.bottomNav.goals.path,
      TabItem.map => Assets.svgs.bottomNav.map.path,
      TabItem.summit => Assets.svgs.bottomNav.summit.path,
    };
  }

  String _getSelecTedTabIconPath() {
    return switch (this) {
      TabItem.home => Assets.svgs.bottomNav.homeSelected.path,
      TabItem.goals => Assets.svgs.bottomNav.goalsSelected.path,
      TabItem.map => Assets.svgs.bottomNav.mapSelected.path,
      TabItem.summit => Assets.svgs.bottomNav.summitSelected.path,
    };
  }
}
