import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

class HomeShellFAB extends StatelessWidget {
  const HomeShellFAB({
    super.key,
  });
  static final IList<String> _hiddenFABLocations = IListConst([
    const GoalsRoute().location,
    const MapRoute().location,
    const SummitRoute().location,
  ]);

  @override
  Widget build(BuildContext context) {
    final location = GoRouterState.of(context).routeLocation;

    // Home Tab
    if (location == const HomeRoute().location) {
      return Padding(
        padding: const EdgeInsets.only(right: 5, bottom: 10),
        child: Sized<PERSON><PERSON>(
          height: 66,
          width: 66,
          child: FloatingActionButton(
            onPressed: () => ChatRoute().push(context),
            elevation: 0.5,
            backgroundColor: AppColors.boldBlue,
            tooltip: context.L.mylaTooltip,
            shape: CircleBorder(),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.unwindBlue,
                    border: Border.all(width: 1, color: AppColors.boldBlue),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.dropShadow,
                        spreadRadius: -1.5,
                        blurRadius: 5,
                        offset: Offset(0, 3),
                      ),
                      BoxShadow(
                        color: AppColors.dropShadow,
                        spreadRadius: -3,
                        blurRadius: 12,
                        offset: Offset(0, 9.5),
                      ),
                    ],
                  ),
                ),
                Assets.svgs.avatars.mylaWave.svg()
              ],
            ),
          ),
        ),
      );
    }

    return SizedBox.shrink();
  }

  // ToolbarHeight for Android/iOS and BackgroundColor for iOS must be pre-initialized, and we are
  // implementing PreferredSizeWidget to be able to only rebuild the appbar.
}

extension StatefulNavigationShellX on StatefulNavigationShell {
  bool get currentLocationHasFAB {
    final location = shellRouteContext.routerState.routeLocation;
    return !HomeShellFAB._hiddenFABLocations.contains(location);
  }
}
