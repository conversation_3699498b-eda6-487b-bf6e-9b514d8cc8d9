import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

import '../utils/tab_item.dart';

class HomeShellBottomNavBar extends StatelessWidget {
  const HomeShellBottomNavBar({
    required this.currentTab,
    required this.onSelectTab,
    super.key,
    this.widgetKey,
    this.onTap,
    this.backgroundColor,
    this.height,
  });

  final Key? widgetKey;
  final TabItem currentTab;
  final ValueChanged<TabItem> onSelectTab;
  final ValueChanged<int>? onTap;
  final Color? backgroundColor;
  final double? height;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 0.75,
          width: double.infinity,
          color: AppColors.stroke,
        ),
        NavigationBar(
          key: widget<PERSON><PERSON>,
          destinations: TabItem.values
              .map(
                (tabItem) => Stack(
                  children: [
                    NavigationDestination(
                      key: widgetKey,
                      icon: tabItem.icon,
                      selectedIcon: tabItem.selectedIcon,
                      label: tabItem.getTabItemLabel(context),
                    ),
                    Align(
                      alignment: Alignment.topCenter,
                      child: AnimatedContainer(
                        duration: Duration(milliseconds: 150),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppColors.orangeGradientLight,
                              AppColors.orangeGradientDark
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                          color: AppColors.boldOrange,
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(20),
                            bottomRight: Radius.circular(20),
                          ),
                        ),
                        width: (currentTab.index == tabItem.index)
                            ? screenWidth / 8
                            : 0,
                        height: 4.5,
                      ),
                    ),
                  ],
                ),
              )
              .toList(),
          selectedIndex: currentTab.index,
          onDestinationSelected: (index) => onSelectTab(TabItem.values[index]),
          backgroundColor: Colors.white,
          height: height ?? 80,
          elevation: 1,
          animationDuration: Duration(milliseconds: 500),
          indicatorColor: Colors.transparent,
          overlayColor: WidgetStateColor.resolveWith(
            (_) => AppColors.boldOrange.withAlpha(40),
          ),
        ),
      ],
    );
  }
}

NavigationBarThemeData homeShellButtonNavBarThemeData = NavigationBarThemeData(
  labelTextStyle: WidgetStateTextStyle.resolveWith(
    (states) {
      return TextStyles.navBarLabel.copyWith(
        color: states.contains(WidgetState.selected)
            ? AppColors.boldOrange
            : AppColors.textPrimary,
        fontWeight: states.contains(WidgetState.selected)
            ? FontWeight.w600
            : FontWeight.w400,
      );
    },
  ),
);
