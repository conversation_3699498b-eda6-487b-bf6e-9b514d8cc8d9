import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/constants/theme_constants.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/home/<USER>/providers/profile_completion_provider.dart';
import 'package:neuroworld/modules/home/<USER>/widgets/edit_profile/profile_picture.dart';
import 'package:neuroworld/modules/home_shell/ui/utils/time_helper.dart';
import 'package:neuroworld/modules/notification/ui/controllers/notification_permission_controller.dart';
import 'package:permission_handler/permission_handler.dart';

class PreferredAppBarSize extends Size {
  PreferredAppBarSize(this.toolbarHeight, this.bottomHeight)
      : super.fromHeight(toolbarHeight + bottomHeight);

  final double toolbarHeight;
  final double bottomHeight;
}

class HomeShellAppBar extends ConsumerWidget implements PreferredSizeWidget {
  HomeShellAppBar({
    this.toolbarHeight = 56,
    this.bottom,
    this.backgroundColor,
    super.key,
  }) : preferredSize = PreferredAppBarSize(
          toolbarHeight,
          bottom?.preferredSize.height ?? 0.0,
        );

  // routes without appbar
  static final IList<String> _hiddenAppBarLocations = const IListConst([]);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final location = GoRouterState.of(context).routeLocation;
    final auth = ref.watch(authStateProvider);
    final progress = ref.watch(profileCompletionProvider);
    final notificationPermission =
        ref.read(notificationPermissionControllerProvider);
    // Home Tab
    if (location == const HomeRoute().location) {
      return Container(
        color: Colors.white,
        padding: const EdgeInsets.only(top: 12),
        child: AppBar(
          scrolledUnderElevation: 0,
          backgroundColor: Colors.white,
          titleSpacing: 0,
          leading: IconButton(
            onPressed: () => Scaffold.of(context).openDrawer(),
            icon: Icon(
              Icons.menu_rounded,
              size: 28,
            ),
            tooltip: context.L.menuButtonTooltip,
          ),
          title: Padding(
            padding: const EdgeInsets.only(right: 14),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () => ProfileRoute().push(context),
                  child: Stack(
                    children: [
                      ProfilePicture(size: 44),
                      // incomplete profile indicator
                      if (progress < 1)
                        Positioned(
                          right: 0,
                          child: Container(
                            height: 12,
                            width: 12,
                            decoration: BoxDecoration(
                              color: AppColors.boldOrange,
                              borderRadius: BorderRadius.circular(50),
                              border: Border.all(
                                color: Colors.white,
                                width: 1.5,
                              ),
                            ),
                          ),
                        )
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      auth != null && auth.user.profile.fullName != null
                          ? "Hello, ${auth.user.profile.fullName}!"
                          : "Hello there!",
                      style: TextStyles.subtitle3.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      "${getGreetingBasedOnTime()}! 👋",
                      style: TextStyles.heading5,
                    ),
                  ],
                ),
                const Spacer(),
                // Notifications

                IconButton(
                  icon: Stack(
                    children: [
                      Assets.svgs.bell.svg(),
                      if (!notificationPermission.isLoading &&
                          notificationPermission.hasValue &&
                          notificationPermission.requireValue !=
                              PermissionStatus.granted)
                        Positioned(
                          top: -1.5,
                          right: -1.5,
                          child: Container(
                            height: 10,
                            width: 10,
                            decoration: BoxDecoration(
                              color: AppColors.error,
                              borderRadius: BorderRadius.circular(50),
                              border: Border.all(
                                color: Colors.white,
                                width: 1.5,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  onPressed: () {
                    context.push(NotificationRoute().location);
                  },
                ),
              ],
            ),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(0.5),
            child: Container(
              color: AppColors.stroke,
              height: 0.5,
            ),
          ),
        ),
      );
    }

    // Goals Tab
    if (location == const GoalsRoute().location) {
      return Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: Assets.images.headerOrange.provider(),
            fit: BoxFit.cover,
          ),
        ),
        padding: const EdgeInsets.only(top: 50),
        child: AppBar(
          backgroundColor: Colors.transparent,
          titleSpacing: 0,
          scrolledUnderElevation: 0,
          title: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  context.L.goalsTabTitle,
                  style: TextStyles.heading3.copyWith(
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                // Right-aligned bell icon
                FilledButton(
                  onPressed: () {
                    context.push(AddGoalRoute().location);
                  },
                  style: FilledButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    backgroundColor: Colors.white,
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_rounded,
                        size: 28,
                        color: AppColors.boldOrange,
                      ),
                      const SizedBox(width: 3),
                      Text(
                        context.L.goalsTabAction,
                        style: TextStyles.tooltipHeading.copyWith(
                          color: AppColors.boldOrange,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(width: 4),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Map Tab
    if (location == const MapRoute().location) {
      return AppBar(
        backgroundColor: Colors.white,
        title: Padding(
          padding: const EdgeInsets.only(top: 10, left: 5),
          child: Text(
            context.L.mapTab,
            style: TextStyles.heading2,
          ),
        ),
      );
    }

    // Summit Tab
    if (location == const SummitRoute().location) {
      return AppBar(
        backgroundColor: Colors.white,
        title: Padding(
          padding: const EdgeInsets.only(top: 10, left: 5),
          child: Text(
            context.L.summitTab,
            style: TextStyles.heading2,
          ),
        ),
      );
    }

    return AppBar();
  }

  final double toolbarHeight;
  final PreferredSizeWidget? bottom;
  final Color? backgroundColor;

  @override
  final Size preferredSize;
}

extension StatefulNavigationShellX on StatefulNavigationShell {
  bool get currentLocationHasAppBar {
    final location = shellRouteContext.routerState.routeLocation;
    return !HomeShellAppBar._hiddenAppBarLocations.contains(location);
  }

  double get currentLocationAppBarHeight {
    final location = shellRouteContext.routerState.routeLocation;
    if (location == HomeRoute().location) return 80;
    if (location == GoalsRoute().location) return 115;
    return ThemeConstants.appBarHeight;
  }
}
