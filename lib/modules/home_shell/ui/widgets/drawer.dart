import 'dart:io';

import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/providers/package_info_provider.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/auth/ui/providers/sign_out_provider.dart';
import 'package:neuroworld/modules/home_shell/ui/utils/drawer_item.dart';

class HomeShellDrawer extends ConsumerWidget {
  const HomeShellDrawer({
    super.key,
  });

  // only these locations will show drawer
  static final IList<String> _drawerLocations =
      IListConst([const HomeRoute().location]);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final location = GoRouterState.of(context).routeLocation;
    final packageInfo = ref.watch(packageInfoProvider).requireValue;

    // Home Tab Drawer
    if (location == const HomeRoute().location) {
      final safeAreaPadding = MediaQuery.of(context).padding;
      return Column(
        children: [
          Expanded(
            child: Drawer(
              width: MediaQuery.of(context).size.width * 0.8,
              backgroundColor: AppColors.surfacePrimary,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: safeAreaPadding.top + 43.5),
                  Padding(
                    padding: const EdgeInsets.only(left: 20),
                    child: Row(
                      children: [
                        Assets.svgs.logo.svg(width: 32, height: 32),
                        const SizedBox(width: 12),
                        Text(
                          context.L.menuTitle,
                          style: TextStyles.subheading1,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 14),
                  Container(height: 0.5, color: AppColors.stroke),
                  const SizedBox(height: 6),
                  ListView.separated(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final drawerItem = DrawerItem.values[index];
                      return ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: 6,
                          horizontal: 14,
                        ),
                        splashColor: AppColors.stroke,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(50),
                        ),
                        leading: drawerItem.icon,
                        trailing: drawerItem.getTrailing(ref),
                        title: Text(
                          drawerItem.getDrawerItemLabel(context),
                          style: TextStyles.body1.copyWith(letterSpacing: -0.3),
                        ),
                        onTap: () => handleDrawerItemTap(
                          context,
                          ref,
                          drawerItem,
                        ),
                      );
                    },
                    separatorBuilder: (context, index) => Container(
                      height: 0.5,
                      margin: const EdgeInsets.symmetric(horizontal: 14),
                      color: AppColors.stroke,
                    ),
                    itemCount: DrawerItem.values.length,
                  ),
                  const Spacer(),
                  Container(height: 0.5, color: AppColors.stroke),
                  Container(
                    margin: Platform.isAndroid
                        ? EdgeInsets.zero
                        : EdgeInsets.only(bottom: safeAreaPadding.bottom - 12),
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Assets.svgs.logo.svg(width: 20, height: 20),
                        Center(
                          child: Text(
                            'v${packageInfo.version}+${packageInfo.buildNumber}',
                            style: TextStyles.subtitle2.copyWith(
                              color: AppColors.inputInactiveText,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.boldOrange.withAlpha(200),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Text(
                            const String.fromEnvironment('ENV'),
                            style: TextStyles.buttonSmall
                                .copyWith(color: Colors.white),
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
          if (Platform.isAndroid) SizedBox(height: safeAreaPadding.bottom)
        ],
      );
    }

    return SizedBox.shrink();
  }

  void handleDrawerItemTap(
    BuildContext context,
    WidgetRef ref,
    DrawerItem drawerItem,
  ) {
    if (drawerItem == DrawerItem.profile) {
      NavService.pop(context, closeOverlays: true);
      ProfileRoute().push(context);
    } else if (drawerItem == DrawerItem.susbcription) {
      NavService.pop(context, closeOverlays: true);
      SubscriptionRoute().push(context);
    } else if (drawerItem == DrawerItem.feedback) {
      NavService.pop(context, closeOverlays: true);
      FeedbackRoute().push(context);
    } else if (drawerItem == DrawerItem.deleteAccount) {
      NavService.pop(context, closeOverlays: true);
      DeleteAccountRoute().push(context);
    } else if (drawerItem == DrawerItem.logout) {
      ref.read(signOutProvider.notifier).signOut();
      LandingRoute().go(context);
    } else if (drawerItem == DrawerItem.badges) {
      BadgesRoute().push(context);
    }
  }
}

extension StatefulNavigationShellX on StatefulNavigationShell {
  bool get currentLocationHasDrawer {
    final location = shellRouteContext.routerState.routeLocation;
    return HomeShellDrawer._drawerLocations.contains(location);
  }
}
