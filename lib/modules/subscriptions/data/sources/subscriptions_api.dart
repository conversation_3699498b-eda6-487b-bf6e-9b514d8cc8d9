import 'package:dio/dio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/data/sources/network/api/api.dart';
import 'package:neuroworld/core/data/sources/network/api/api_endpoints.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscriptions_api.g.dart';

@Riverpod(keepAlive: true)
SubscriptionsApi subscriptionsApi(Ref ref) => SubscriptionsApi(api: ref.watch(apiProvider));

class SubscriptionsApi {
  SubscriptionsApi({required this.api});

  final Api api;

  Future<Response<dynamic>> getSubscriptions() => api.get(
        path: ApiEndpoints.subscriptions,
      );
}
