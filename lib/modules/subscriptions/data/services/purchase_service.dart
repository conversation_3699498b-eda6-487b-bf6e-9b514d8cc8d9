import 'dart:io';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:neuroworld/modules/subscriptions/data/models/purchase_status.dart'
    as models;
import 'package:neuroworld/modules/subscriptions/ui/controllers/subscription_controller.dart';
import 'package:neuroworld/modules/subscriptions/ui/providers/subscription_success_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'purchase_service.g.dart';

@Riverpod(keepAlive: true)
PurchaseService purchaseService(Ref ref) => PurchaseService(ref);

class PurchaseService {
  final Ref? _ref;

  PurchaseService([this._ref]);

  static const Set<String> productIds = {
    'com.neuroworld.dev.habit.monthly',
    'com.neuroworld.dev.habit.yearly',
    'com.neuroworld.dev.academy.monthly',
    'com.neuroworld.dev.academy.yearly',
    'com.neuroworld.dev.summit.monthly',
    'com.neuroworld.dev.summit.yearly',
    'com.neuroworld.dev.clinical.monthly',
    'com.neuroworld.dev.clinical.yearly',
  };

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  bool _isStoreKit2Initialized = false;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    // Enable StoreKit2 for iOS
    if (Platform.isIOS) {
      await _initializeStoreKit2();
    }

    _isInitialized = true;
  }

  Future<void> _initializeStoreKit2() async {
    try {
      _inAppPurchase
          .getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();

      _isStoreKit2Initialized = true;
    } catch (e) {
      _isStoreKit2Initialized = false;
    }
  }

  Future<List<ProductDetails>> loadProducts() async {
    await initialize();

    final bool isAvailable = await _inAppPurchase.isAvailable();
    if (!isAvailable) {
      throw Exception('Store not available');
    }

    final ProductDetailsResponse response =
        await _inAppPurchase.queryProductDetails(productIds.toSet());

    if (response.error != null) {
      throw Exception('Failed to load products: ${response.error}');
    }
    return response.productDetails;
  }

  Future<bool> purchaseProduct(String productId,
      {String? applicationUserName}) async {
    await initialize();

    final products = await loadProducts();
    final product = products.where((p) => p.id == productId).firstOrNull;

    if (product == null) {
      throw Exception('Product not found: $productId');
    }

    final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: product, applicationUserName: applicationUserName);

    final success =
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
    return success;
  }

  void listenToPurchaseUpdates() {
    _inAppPurchase.purchaseStream.listen(
      (List<PurchaseDetails> purchaseDetailsList) {
        for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
          // Handle different purchase statuses
          switch (purchaseDetails.status) {
            case PurchaseStatus.purchased:
              _handleSuccessfulPurchase(purchaseDetails);
              _inAppPurchase.completePurchase(purchaseDetails);
              break;
            case PurchaseStatus.restored:
              _handleSuccessfulPurchase(purchaseDetails);
              _inAppPurchase.completePurchase(purchaseDetails);
              break;
            case PurchaseStatus.error:
              _inAppPurchase.completePurchase(purchaseDetails);
              break;
            case PurchaseStatus.canceled:
              _inAppPurchase.completePurchase(purchaseDetails);
              break;
            case PurchaseStatus.pending:
              _inAppPurchase.completePurchase(purchaseDetails);
              break;
          }
        }
      },
      onError: (error) {
        // Handle purchase stream errors silently
      },
    );
  }

  /// Handles successful purchase by updating subscription status
  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) {
    if (_ref != null) {
      try {
        // Update subscription status using the controller
        _ref
            .read(subscriptionControllerProvider.notifier)
            .updateSubscriptionStatus(true,
                productId: purchaseDetails.productID);

        // Extract tier name from product ID and trigger success state
        try {
          final productInfo = parseProductId(purchaseDetails.productID);
          _ref.read(subscriptionSuccessProvider.notifier).showSuccess(
                tierName: productInfo.tier,
                productId: purchaseDetails.productID,
              );
        } catch (e) {
          // If parsing fails, still show success with generic tier name
          _ref.read(subscriptionSuccessProvider.notifier).showSuccess(
                tierName: 'Premium',
                productId: purchaseDetails.productID,
              );
        }
      } catch (e) {
        // Handle errors silently - the purchase was successful
        // but we couldn't update the local state
      }
    }
  }

  /// Parses product ID to extract subscription information
  static ({String tier, String name, String billingPeriod, int durationDays})
      parseProductId(String productId) {
    // Product ID format: com.neuroworld.dev.{tier}.{period}
    // Examples:
    // - com.neuroworld.dev.habit.monthly
    // - com.neuroworld.dev.habit.yearly
    // - com.neuroworld.dev.summit.monthly
    // - com.neuroworld.dev.clinical.yearly

    final parts = productId.split('.');
    if (parts.length < 5) {
      throw Exception('Invalid product ID format: $productId');
    }

    final tier = parts[3]; // habit, summit, clinical, academy
    final period = parts[4]; // monthly, yearly

    // Map academy and habit to habits (based on the product IDs in purchase_service.dart)
    final normalizedTier =
        (tier == 'academy' || tier == 'habit') ? 'habits' : tier;

    // Create human-readable name
    final tierName = _capitalizeTierName(normalizedTier);
    final periodName = period == 'monthly' ? 'Monthly' : 'Yearly';
    final name = '$tierName $periodName';

    // Calculate duration days
    final durationDays = period == 'monthly' ? 30 : 365;

    return (
      tier: normalizedTier,
      name: name,
      billingPeriod: period,
      durationDays: durationDays,
    );
  }

  /// Capitalizes tier name for display
  static String _capitalizeTierName(String tier) {
    switch (tier.toLowerCase()) {
      case 'habits':
        return 'Habits';
      case 'summit':
        return 'Summit';
      case 'clinical':
        return 'Clinical';
      case 'free':
        return 'Free';
      default:
        return tier[0].toUpperCase() + tier.substring(1);
    }
  }

  Future<ProductDetails?> getProduct(String productId) async {
    final products = await loadProducts();
    return products.where((p) => p.id == productId).firstOrNull;
  }

  // Expose purchase stream for UI to listen to purchase updates
  Stream<List<PurchaseDetails>> get purchaseStream =>
      _inAppPurchase.purchaseStream;

  bool get isStoreKit2Enabled => _isStoreKit2Initialized;

  models.PaymentSystem get paymentSystem {
    if (Platform.isIOS) {
      return _isStoreKit2Initialized
          ? models.PaymentSystem.storeKit2
          : models.PaymentSystem.storeKit1;
    }
    return models.PaymentSystem.googlePlayBilling;
  }

  models.StoreType get storeType {
    if (Platform.isIOS) {
      return models.StoreType.appStore;
    } else if (Platform.isAndroid) {
      return models.StoreType.googlePlay;
    }
    return models.StoreType.unknown;
  }

  String get platformInfo {
    if (Platform.isIOS) {
      return _isStoreKit2Initialized ? 'StoreKit2' : 'StoreKit1';
    }
    return 'Google Play Billing';
  }
}
