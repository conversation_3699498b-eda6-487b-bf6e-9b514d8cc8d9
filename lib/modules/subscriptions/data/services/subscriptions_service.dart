import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/modules/subscriptions/data/models/response/subscription_plan.dart';
import 'package:neuroworld/modules/subscriptions/data/models/response/subscriptions_response.dart';
import 'package:neuroworld/modules/subscriptions/data/sources/subscriptions_api.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscriptions_service.g.dart';

@Riverpod(keepAlive: true)
SubscriptionsService subscriptionsService(Ref ref) => SubscriptionsService(
      subscriptionsApi: ref.watch(subscriptionsApiProvider),
    );

class SubscriptionsService {
  SubscriptionsService({required this.subscriptionsApi});

  final SubscriptionsApi subscriptionsApi;

  Future<List<SubscriptionPlan>> getSubscriptions() async {
    final res = await subscriptionsApi.getSubscriptions();
    return SubscriptionsResponse.fromList(res.data).plans;
  }
}
