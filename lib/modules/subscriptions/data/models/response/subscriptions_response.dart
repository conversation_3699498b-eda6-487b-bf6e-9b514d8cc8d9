import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/subscriptions/data/models/response/subscription_plan.dart';

part 'subscriptions_response.freezed.dart';
part 'subscriptions_response.g.dart';

@freezed
abstract class SubscriptionsResponse with _$SubscriptionsResponse {
  @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
  const factory SubscriptionsResponse({
    required List<SubscriptionPlan> plans,
  }) = _SubscriptionsResponse;

  factory SubscriptionsResponse.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionsResponseFromJson(json);

  // Helper factory to create from list directly (since API returns array)
  factory SubscriptionsResponse.fromList(List<dynamic> jsonList) =>
      SubscriptionsResponse(
        plans: jsonList
            .map((json) =>
                SubscriptionPlan.fromJson(json as Map<String, dynamic>))
            .toList(),
      );
}
