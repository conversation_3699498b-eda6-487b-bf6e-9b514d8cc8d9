import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/subscriptions/data/models/response/subscription_feature.dart';
import 'package:neuroworld/modules/subscriptions/data/models/response/subscription_pricing.dart';

part 'subscription_plan.freezed.dart';
part 'subscription_plan.g.dart';

@freezed
abstract class SubscriptionPlan with _$SubscriptionPlan {
  @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
  const factory SubscriptionPlan({
    required String tier,
    required List<SubscriptionFeature> features,
    SubscriptionPricing? yearly,
    SubscriptionPricing? monthly,
    SubscriptionPricing? quarterly,
    required List<String> colors,
  }) = _SubscriptionPlan;

  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionPlanFromJson(json);
}
