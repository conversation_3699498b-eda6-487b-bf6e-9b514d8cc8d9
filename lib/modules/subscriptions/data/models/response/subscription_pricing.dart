import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_pricing.freezed.dart';
part 'subscription_pricing.g.dart';

@freezed
abstract class SubscriptionPricing with _$SubscriptionPricing {
  @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
  const factory SubscriptionPricing({
    required String? productId,
    required int price,
  }) = _SubscriptionPricing;

  factory SubscriptionPricing.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionPricingFromJson(json);
}
