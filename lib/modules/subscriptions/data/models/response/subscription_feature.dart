import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_feature.freezed.dart';
part 'subscription_feature.g.dart';

@freezed
abstract class SubscriptionFeature with _$SubscriptionFeature {
  @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
  const factory SubscriptionFeature({
    required String description,
    required String icon,
  }) = _SubscriptionFeature;

  factory SubscriptionFeature.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionFeatureFromJson(json);
}
