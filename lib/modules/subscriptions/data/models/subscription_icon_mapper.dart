import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';

enum SubscriptionIcon {
  habits,
  mylaChat,
  neurocity,
  neuroGames,
  community,
  livestreams,
  clinicalSolution;

  static SubscriptionIcon? fromString(String value) {
    final normalizedValue = value.toLowerCase().trim().replaceAll('_', '');

    for (final icon in SubscriptionIcon.values) {
      final enumName = icon.name.toLowerCase();
      if (enumName == normalizedValue ||
          enumName.replaceAll('_', '') == normalizedValue) {
        return icon;
      }
    }

    if (normalizedValue.contains('myla') && normalizedValue.contains('chat')) {
      return SubscriptionIcon.mylaChat;
    }
    if (normalizedValue.contains('neuro') &&
        normalizedValue.contains('games')) {
      return SubscriptionIcon.neuroGames;
    }
    if (normalizedValue.contains('clinical') &&
        normalizedValue.contains('solution')) {
      return SubscriptionIcon.clinicalSolution;
    }

    return null;
  }

  // Helper methods
  bool get isHabits => this == SubscriptionIcon.habits;
  bool get isMylaChat => this == SubscriptionIcon.mylaChat;
  bool get isNeurocity => this == SubscriptionIcon.neurocity;
  bool get isNeuroGames => this == SubscriptionIcon.neuroGames;
  bool get isCommunity => this == SubscriptionIcon.community;
  bool get isLivestreams => this == SubscriptionIcon.livestreams;
  bool get isClinicalSolution => this == SubscriptionIcon.clinicalSolution;

  // Validation methods
  static bool isValid(String? value) {
    if (value == null) return false;
    return fromString(value) != null;
  }

  static SubscriptionIcon get defaultIcon => SubscriptionIcon.habits;

  String get svgPath {
    switch (this) {
      case SubscriptionIcon.habits:
        return Assets.svgs.habit.path;
      case SubscriptionIcon.mylaChat:
        return Assets.svgs.mylaChat.path;
      case SubscriptionIcon.neurocity:
        return Assets.svgs.neurocity.path;
      case SubscriptionIcon.neuroGames:
        return Assets.svgs.neuroGames.path;
      case SubscriptionIcon.community:
        return Assets.svgs.community.path;
      case SubscriptionIcon.livestreams:
        return Assets.svgs.liveStream.path;
      case SubscriptionIcon.clinicalSolution:
        return Assets.svgs.liveStream.path;
    }
  }
}

class SubscriptionIconMapper {
  static SvgPicture getIconFromName(String iconName) {
    final icon = SubscriptionIcon.fromString(iconName);
    final svgPath = icon?.svgPath ?? Assets.svgs.liveStream.path;

    return SvgPicture.asset(
      svgPath,
      width: 32,
      height: 32,
    );
  }

  static SvgPicture getIconFromEnum(SubscriptionIcon icon) {
    return SvgPicture.asset(
      icon.svgPath,
      width: 32,
      height: 32,
    );
  }
}
