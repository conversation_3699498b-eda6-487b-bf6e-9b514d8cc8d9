enum PurchaseStatus {
  pending,
  purchased,
  error,
  restored,
  canceled;

  static PurchaseStatus? fromString(String value) {
    final normalizedValue = value.toLowerCase().trim();

    for (final status in PurchaseStatus.values) {
      if (status.name.toLowerCase() == normalizedValue) {
        return status;
      }
    }
    return null;
  }

  // Helper methods
  bool get isPending => this == PurchaseStatus.pending;
  bool get isPurchased => this == PurchaseStatus.purchased;
  bool get isError => this == PurchaseStatus.error;
  bool get isRestored => this == PurchaseStatus.restored;
  bool get isCanceled => this == PurchaseStatus.canceled;

  bool get isSuccessful => isPurchased || isRestored;
  bool get isFailure => isError || isCanceled;
}

enum StoreType {
  appStore,
  googlePlay,
  unknown;

  static StoreType? fromString(String value) {
    final normalizedValue = value.toLowerCase().trim().replaceAll('_', '');

    for (final type in StoreType.values) {
      final enumName = type.name.toLowerCase();
      if (enumName == normalizedValue ||
          enumName.replaceAll('_', '') == normalizedValue) {
        return type;
      }
    }

    if (normalizedValue.contains('app') && normalizedValue.contains('store')) {
      return StoreType.appStore;
    }
    if (normalizedValue.contains('google') &&
        normalizedValue.contains('play')) {
      return StoreType.googlePlay;
    }

    return null;
  }

  bool get isAppStore => this == StoreType.appStore;
  bool get isGooglePlay => this == StoreType.googlePlay;
  bool get isUnknown => this == StoreType.unknown;
}

enum PaymentSystem {
  storeKit1,
  storeKit2,
  googlePlayBilling;

  static PaymentSystem? fromString(String value) {
    final normalizedValue = value.toLowerCase().trim().replaceAll('_', '');

    for (final system in PaymentSystem.values) {
      final enumName = system.name.toLowerCase();
      if (enumName == normalizedValue ||
          enumName.replaceAll('_', '') == normalizedValue) {
        return system;
      }
    }

    if (normalizedValue.contains('storekit') && normalizedValue.contains('1')) {
      return PaymentSystem.storeKit1;
    }
    if (normalizedValue.contains('storekit') && normalizedValue.contains('2')) {
      return PaymentSystem.storeKit2;
    }
    if (normalizedValue.contains('google') &&
        normalizedValue.contains('play')) {
      return PaymentSystem.googlePlayBilling;
    }

    return null;
  }

  bool get isStoreKit1 => this == PaymentSystem.storeKit1;
  bool get isStoreKit2 => this == PaymentSystem.storeKit2;
  bool get isGooglePlayBilling => this == PaymentSystem.googlePlayBilling;
  bool get isStoreKit => isStoreKit1 || isStoreKit2;
}
