enum SubscriptionTier {
  free,
  habits,
  summit,
  clinical;

  static SubscriptionTier? fromString(String value) {
    final normalizedValue = value.toLowerCase().trim();

    for (final tier in SubscriptionTier.values) {
      if (tier.name.toLowerCase() == normalizedValue) {
        return tier;
      }
    }
    return null;
  }

  bool get isFree => this == SubscriptionTier.free;
  bool get isPaid => this != SubscriptionTier.free;
  bool get isHabits => this == SubscriptionTier.habits;
  bool get isSummit => this == SubscriptionTier.summit;
  bool get isClinical => this == SubscriptionTier.clinical;

  // Tier hierarchy (for comparison)
  int get tierLevel {
    switch (this) {
      case SubscriptionTier.free:
        return 0;
      case SubscriptionTier.habits:
        return 1;
      case SubscriptionTier.summit:
        return 2;
      case SubscriptionTier.clinical:
        return 3;
    }
  }

  // Check if this tier is higher than another
  bool isHigherThan(SubscriptionTier other) => tierLevel > other.tierLevel;

  // Check if this tier is lower than another
  bool isLowerThan(SubscriptionTier other) => tierLevel < other.tierLevel;

  // Get all tiers above this one
  List<SubscriptionTier> get higherTiers {
    return SubscriptionTier.values
        .where((tier) => tier.tierLevel > tierLevel)
        .toList();
  }

  // Get all tiers below this one
  List<SubscriptionTier> get lowerTiers {
    return SubscriptionTier.values
        .where((tier) => tier.tierLevel < tierLevel)
        .toList();
  }

  // Validation methods
  static bool isValid(String? value) {
    if (value == null) return false;
    return fromString(value) != null;
  }

  static SubscriptionTier get defaultTier => SubscriptionTier.free;
}
