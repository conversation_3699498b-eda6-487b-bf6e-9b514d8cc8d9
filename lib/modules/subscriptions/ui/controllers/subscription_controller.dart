import 'package:neuroworld/modules/auth/data/models/active_subscription.dart';
import 'package:neuroworld/modules/auth/data/sources/auth_shared_prefs.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/subscriptions/ui/utils/subscription_helpers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscription_controller.g.dart';

@riverpod
class SubscriptionController extends _$SubscriptionController {
  @override
  FutureOr<bool?> build() => null;

  /// Updates the user's subscription status after successful purchase
  /// Implements simplified subscription logic:
  /// 1. If user doesn't have hasActiveSubscription and ActiveSubscription is null → create sample object and set hasActiveSubscription to true
  /// 2. If user has hasActiveSubscription and ActiveSubscription is not null → don't update these objects
  /// 3. Purchase should always complete regardless of existing subscription status
  Future<void> updateSubscriptionStatus(bool hasActiveSubscription,
      {String? productId}) async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(() async {
      // Get current user
      final currentUser = ref.read(authStateProvider)?.user;
      if (currentUser == null) {
        throw Exception('No user found');
      }

      // Case 1: User doesn't have active subscription OR ActiveSubscription is null
      // → Create sample object and set hasActiveSubscription to true
      if (!currentUser.hasActiveSubscription ||
          currentUser.activeSubscription == null) {
        ActiveSubscription? activeSubscription;

        if (productId != null) {
          try {
            activeSubscription =
                SubscriptionHelpers.createActiveSubscriptionFromProductId(
                    productId);
          } catch (e, stackTrace) {
            print("❌ Error creating ActiveSubscription: $e");
            print("Stack trace: $stackTrace");
            // Continue without activeSubscription - user will still get hasActiveSubscription: true
          }
        } else {
          print("ProductId is null, not creating ActiveSubscription");
        }

        final updatedUser = currentUser.copyWith(
          hasActiveSubscription: true,
          activeSubscription: activeSubscription,
        );

        // Update the auth state with the new user data (in-memory)
        ref.read(authStateProvider.notifier).updateUser(updatedUser);

        final currentAuth = ref.read(authStateProvider);
        if (currentAuth != null) {
          final updatedAuth = currentAuth.copyWith(user: updatedUser);
          try {
            await ref.read(authSharedPrefsProvider).setAuthData(updatedAuth);
          } catch (e) {
            print("❌ Error persisting to local storage: $e");
          }
        } else {
          print("❌ Current auth is null, cannot persist to local storage");
        }
      }

      return currentUser.hasActiveSubscription || hasActiveSubscription;
    });
  }

  /// Checks if user is trying to purchase an already purchased subscription
  /// Returns true if the subscription is already purchased, false otherwise
  bool isSubscriptionAlreadyPurchased(String productId) {
    final currentUser = ref.read(authStateProvider)?.user;
    if (currentUser == null ||
        !currentUser.hasActiveSubscription ||
        currentUser.activeSubscription == null) {
      return false;
    }

    final activeSubscription = currentUser.activeSubscription!;
    return activeSubscription.plan.productId == productId;
  }
}
