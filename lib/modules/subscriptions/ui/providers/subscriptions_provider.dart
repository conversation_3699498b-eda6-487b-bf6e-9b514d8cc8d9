import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/modules/subscriptions/data/models/response/subscription_plan.dart';
import 'package:neuroworld/modules/subscriptions/data/services/subscriptions_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscriptions_provider.g.dart';

@riverpod
Future<List<SubscriptionPlan>> subscriptions(Ref ref) async {
  final subscriptionsService = ref.watch(subscriptionsServiceProvider);
  return subscriptionsService.getSubscriptions();
}
