import 'package:neuroworld/modules/subscriptions/data/models/subscription_success_state.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscription_success_provider.g.dart';

@riverpod
class SubscriptionSuccess extends _$SubscriptionSuccess {
  @override
  SubscriptionSuccessState? build() => null;

  void showSuccess({
    required String tierName,
    required String productId,
  }) {
    state = SubscriptionSuccessState(
      tierName: tierName,
      productId: productId,
      purchaseTime: DateTime.now(),
    );
  }

  void clearSuccess() {
    state = null;
  }
}
