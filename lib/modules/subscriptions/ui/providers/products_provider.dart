import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:neuroworld/modules/subscriptions/data/services/purchase_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'products_provider.g.dart';

@Riverpod(keepAlive: true) // Keep alive to maintain cache
Future<List<ProductDetails>> availableProducts(Ref ref) async {
  final purchaseService = ref.read(purchaseServiceProvider);
  final products = await purchaseService.loadProducts();
  return products;
}

@riverpod
Future<ProductDetails?> productDetails(Ref ref, String productId) async {
  final products = await ref.watch(availableProductsProvider.future);
  final product = products.where((p) => p.id == productId).firstOrNull;
  return product;
}

@riverpod
bool areProductsLoaded(Ref ref) {
  final productsAsync = ref.watch(availableProductsProvider);
  return productsAsync.hasValue;
}

@riverpod
bool areProductsLoading(Ref ref) {
  final productsAsync = ref.watch(availableProductsProvider);
  return productsAsync.isLoading;
}
