import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class Feature {
  final SvgPicture icon;
  final String text;

  Feature(this.icon, this.text);
}

class SubscriptionPlanCard extends StatelessWidget {
  final String title;
  final String price;
  final String duration;
  final List<Feature> features;
  final Function(String? productId) onUpgrade;
  final bool isSelected;
  final bool isCurrent;
  final List<Color>? gradientColors;
  final String? productId;

  const SubscriptionPlanCard({
    super.key,
    required this.title,
    required this.price,
    required this.duration,
    required this.features,
    required this.onUpgrade,
    this.isSelected = false,
    this.isCurrent = false,
    this.gradientColors,
    this.productId,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: isSelected ? 1.0 : 1.0,
      child: Center(
        child: SizedBox(
          width: 300,
          height: MediaQuery.of(context).size.height - 260,
          child: Container(
            margin: const EdgeInsets.only(right: 16, bottom: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: gradientColors ??
                    [
                      AppColors.habitGradientStart,
                      AppColors.habitGradientEnd,
                    ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.stroke),
            ),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 20, 16, 16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text(
                          title,
                          style: TextStyles.heading4.copyWith(
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ),
                      Container(
                        height: 40,
                        width: 0.5,
                        color: AppColors.stroke,
                        margin: const EdgeInsets.symmetric(horizontal: 12),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            price,
                            style: TextStyles.heading4.copyWith(
                              color: AppColors.textPrimary,
                            ),
                          ),
                          Text(
                            duration,
                            style: TextStyles.subtitle3.copyWith(
                              color: AppColors.textTertiary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: Divider(height: 1, color: AppColors.stroke),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: features
                          .map(
                            (f) => Padding(
                              padding: const EdgeInsets.only(bottom: 14),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  f.icon,
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      f.text,
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ),
                ),
                const Divider(height: 1, color: AppColors.stroke),
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.all(16),
                  child: isCurrent
                      ? OutlinedButton.icon(
                          onPressed: null,
                          icon: const Icon(Icons.check,
                              size: 18, color: AppColors.textPrimary),
                          label: Text(
                            context.L.currentPlanCardLabel,
                          ),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: AppColors.stroke),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        )
                      : ElevatedButton(
                          onPressed: () => onUpgrade(productId),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.boldOrange,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: Text(context.L.upgrade),
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
