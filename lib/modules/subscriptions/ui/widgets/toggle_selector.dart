import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

class ToggleSelector extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int> onSelected;

  const ToggleSelector({
    super.key,
    required this.selectedIndex,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20), // Added padding here
      child: Container(
        height: 45,
        decoration: BoxDecoration(
          color: AppColors.goalCardDivider,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => onSelected(0),
                child: Container(
                  alignment: Alignment.center,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                  child: Container(
                    decoration: BoxDecoration(
                      color: selectedIndex == 0
                          ? Colors.white
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: selectedIndex == 0
                          ? [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : [],
                    ),
                    width: double.infinity,
                    alignment: Alignment.center,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                    child: Text(
                      context.L.monthly,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: selectedIndex == 0
                            ? AppColors.textPrimary
                            : AppColors.textTertiary,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: GestureDetector(
                onTap: () => onSelected(1),
                child: Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.fromLTRB(4, 6, 8, 6),
                  child: Container(
                    decoration: BoxDecoration(
                      color: selectedIndex == 1
                          ? Colors.white
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: selectedIndex == 1
                          ? [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : [],
                    ),
                    padding: const EdgeInsets.fromLTRB(4, 6, 8, 6),
                    width: double.infinity,
                    alignment: Alignment.center,
                    child: Text(
                      context.L.yearly,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: selectedIndex == 1
                            ? AppColors.textPrimary
                            : AppColors.textTertiary,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
