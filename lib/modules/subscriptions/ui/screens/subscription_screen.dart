import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/auth/data/models/user.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/subscriptions/data/models/response/subscription_plan.dart';
import 'package:neuroworld/modules/subscriptions/data/models/subscription_color_mapper.dart';
import 'package:neuroworld/modules/subscriptions/data/models/subscription_color_scheme.dart';
import 'package:neuroworld/modules/subscriptions/data/models/subscription_icon_mapper.dart';
import 'package:neuroworld/modules/subscriptions/data/models/subscription_success_state.dart';
import 'package:neuroworld/modules/subscriptions/data/models/subscription_tier.dart';
import 'package:neuroworld/modules/subscriptions/data/services/purchase_service.dart';
import 'package:neuroworld/modules/subscriptions/ui/controllers/subscription_controller.dart';
import 'package:neuroworld/modules/subscriptions/ui/providers/products_provider.dart';
import 'package:neuroworld/modules/subscriptions/ui/providers/subscription_success_provider.dart';
import 'package:neuroworld/modules/subscriptions/ui/providers/subscriptions_provider.dart';
import 'package:neuroworld/modules/subscriptions/ui/widgets/subscription_plan_card.dart';
import 'package:neuroworld/modules/subscriptions/ui/widgets/subscription_success_dialog.dart';
import 'package:neuroworld/modules/subscriptions/ui/widgets/toggle_selector.dart';

class SubscriptionScreen extends HookConsumerWidget {
  const SubscriptionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = useState(0); // 0 = Monthly, 1 = Yearly
    final pageController = usePageController(viewportFraction: 0.78);
    final currentIndex = useState(0);
    final user = ref.watch(authStateProvider)?.user;

    ref.watch(availableProductsProvider);

    final subscriptionsAsync = ref.watch(subscriptionsProvider);

    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      builder: (context) =>
          SubscriptionSuccessDialog(tierName: 'next.tierName'),
    ).then((_) {
      // Clear the success state after dialog is dismissed
      ref.read(subscriptionSuccessProvider.notifier).clearSuccess();
    });

    // Listen for subscription success state and show dialog
    ref.listen<SubscriptionSuccessState?>(
      subscriptionSuccessProvider,
      (previous, next) {
        if (next != null && context.mounted) {
          // Show success dialog directly without wrapper
          showDialog(
            context: context,
            barrierDismissible: true,
            barrierColor: Colors.black.withValues(alpha: 0.5),
            builder: (context) =>
                SubscriptionSuccessDialog(tierName: next.tierName),
          ).then((_) {
            // Clear the success state after dialog is dismissed
            ref.read(subscriptionSuccessProvider.notifier).clearSuccess();
          });
        }
      },
    );

    return subscriptionsAsync.when(
      data: (subscriptionPlans) {
        // Filter subscription plans based on active subscription
        final filteredPlans = _filterSubscriptionPlans(subscriptionPlans, user);

        final plans = filteredPlans.map((plan) {
          return _buildSubscriptionPlanCard(
              plan, selectedIndex.value, context, user, ref);
        }).toList();

        return _buildScaffold(
            context, selectedIndex, pageController, currentIndex, plans);
      },
      loading: () => _buildLoadingScaffold(context),
      error: (error, stack) => _buildErrorScaffold(context, error),
    );
  }

  Widget _buildScaffold(
    BuildContext context,
    ValueNotifier<int> selectedIndex,
    PageController pageController,
    ValueNotifier<int> currentIndex,
    List<Widget> plans,
  ) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(context),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(0, 16, 0, 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ToggleSelector(
              selectedIndex: selectedIndex.value,
              onSelected: (index) => selectedIndex.value = index,
            ),
            const SizedBox(height: 20),
            Expanded(
              child: PageView.builder(
                controller: pageController,
                itemCount: plans.length,
                onPageChanged: (index) => currentIndex.value = index,
                itemBuilder: (context, index) {
                  final plan = plans[index];
                  return plan;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingScaffold(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(context),
      body: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorScaffold(BuildContext context, Object error) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(context),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Failed to load subscription plans',
              style: TextStyles.heading4,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: TextStyles.body3,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.pop(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return PreferredSize(
      preferredSize: const Size(0, 80),
      child: Padding(
        padding: const EdgeInsets.only(top: 20),
        child: AppBar(
          scrolledUnderElevation: 0,
          title: Text(
            context.L.subscriptionPlans,
            style: TextStyles.subheading1,
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(0.5),
            child: Container(
              color: AppColors.stroke,
              height: 0.5,
            ),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: Assets.svgs.backArrow.svg(),
            onPressed: () => context.pop(),
          ),
          backgroundColor: Colors.white,
        ),
      ),
    );
  }

  /// Filters subscription plans based on user's active subscription
  List<SubscriptionPlan> _filterSubscriptionPlans(
      List<SubscriptionPlan> plans, User? user) {
    // If user has an active subscription, remove the free tier from the list
    if (user?.activeSubscription != null) {
      return plans.where((plan) {
        final tier = SubscriptionTier.fromString(plan.tier);
        return tier != SubscriptionTier.free;
      }).toList();
    }

    // If no active subscription, show all plans (including free)
    return plans;
  }

  /// Determines if the current plan should be marked as current
  bool _isPlanCurrent(SubscriptionPlan plan, User? user, int selectedPeriod) {
    final activeSubscription = user?.activeSubscription;

    if (activeSubscription != null) {
      // User has active subscription - check if this plan matches both tier and billing period
      final planTier = SubscriptionTier.fromString(plan.tier);
      final activeTier =
          SubscriptionTier.fromString(activeSubscription.plan.tier);

      // Check if tiers match
      if (planTier != activeTier) {
        return false;
      }

      // Check if billing period matches the selected period
      final activeBillingPeriod =
          activeSubscription.plan.billingPeriod.toLowerCase();

      // selectedPeriod: 0 = Monthly, 1 = Yearly
      if (selectedPeriod == 0) {
        // Monthly selected - check if active subscription is monthly
        return activeBillingPeriod == 'monthly';
      } else {
        // Yearly selected - check if active subscription is yearly
        return activeBillingPeriod == 'yearly' ||
            activeBillingPeriod == 'annual';
      }
    } else {
      // No active subscription - mark free tier as current
      final planTier = SubscriptionTier.fromString(plan.tier);
      return planTier == SubscriptionTier.free;
    }
  }

  Widget _buildSubscriptionPlanCard(SubscriptionPlan plan, int selectedPeriod,
      BuildContext context, User? user, WidgetRef ref) {
    // Determine price, duration, and product ID based on selected period
    String price;
    String duration;
    String? productId;

    if (selectedPeriod == 0) {
      // Monthly
      if (plan.monthly != null) {
        price = '\$${plan.monthly!.price}';
        duration = context.L.perMonth;
        productId = plan.monthly!.productId;
      } else {
        price = '\$${0}';
        duration = context.L.perMonth;
        productId = null;
      }
    } else {
      // Yearly
      if (plan.yearly != null) {
        price = '\$${plan.yearly!.price}';
        duration = context.L.perYear;
        productId = plan.yearly!.productId;
      } else {
        price = '\$${0}';
        duration = context.L.perYear;
        productId = null;
      }
    }

    // Convert features to the format expected by SubscriptionPlanCard
    final features = plan.features.map((feature) {
      return Feature(
        SubscriptionIconMapper.getIconFromName(feature.icon),
        feature.description,
      );
    }).toList();

    // Get gradient colors using the new enum approach
    final colorScheme = SubscriptionColorScheme.fromColorNames(plan.colors);
    final gradientColors = colorScheme?.gradientColors ??
        SubscriptionColorMapper.getGradientColors(plan.colors);

    // Determine if this plan is the user's current plan
    final isCurrent = _isPlanCurrent(plan, user, selectedPeriod);

    return SubscriptionPlanCard(
      title: _formatTierName(plan.tier, context),
      price: price,
      duration: duration,
      features: features,
      gradientColors: gradientColors,
      productId: productId,
      isCurrent: isCurrent,
      onUpgrade: (String? productId) {
        _handleUpgrade(productId, plan.tier, context, ref);
      },
    );
  }

  String _formatTierName(String tier, BuildContext context) {
    final subscriptionTier = SubscriptionTier.fromString(tier);
    if (subscriptionTier != null) {
      switch (subscriptionTier) {
        case SubscriptionTier.free:
          return context.L.freeTier;
        case SubscriptionTier.habits:
          return context.L.habitsTier;
        case SubscriptionTier.summit:
          return context.L.summitTier;
        case SubscriptionTier.clinical:
          return context.L.clinicalTier;
      }
    }
    return _capitalizeString(tier);
  }

  String _capitalizeString(String text) {
    return text
        .split(' ')
        .map((word) =>
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : word)
        .join(' ');
  }

  void _handleUpgrade(
      String? productId, String tier, BuildContext context, WidgetRef ref) {
    if (productId != null) {
      _handlePaidSubscription(productId, tier, context, ref);
    }
  }

  void _handlePaidSubscription(String productId, String tier,
      BuildContext context, WidgetRef ref) async {
    final user = ref.read(authStateProvider)?.user;
    final purchaseService = ref.read(purchaseServiceProvider);

    try {
      // Case 3: Check if user is trying to purchase an already purchased subscription
      final subscriptionController =
          ref.read(subscriptionControllerProvider.notifier);
      if (subscriptionController.isSubscriptionAlreadyPurchased(productId)) {
        if (context.mounted) {
          _showErrorDialog(
              context, 'This subscription is already purchased and active.');
        }
        return;
      }

      // Get user ID for applicationUserName (helps with purchase tracking)
      final applicationUserName = user?.id ?? 'anonymous_user';

      // Directly initiate purchase - this will show native dialog
      // Purchase updates will be handled by the purchase stream listener
      await purchaseService.purchaseProduct(
        productId,
        applicationUserName: applicationUserName,
      );
    } catch (e) {
      if (context.mounted) {
        _showErrorDialog(
            context, 'Failed to initiate purchase. Please try again.');
      }
    }
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.L.error),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.L.ok),
          ),
        ],
      ),
    );
  }
}
