import 'dart:math';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/auth/data/models/active_subscription.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';

class SubscriptionHelpers {
  SubscriptionHelpers._();

  /// Capitalizes tier name for display
  static String capitalizeTierName(String tier) {
    switch (tier.toLowerCase()) {
      case 'habits':
        return 'Habits';
      case 'summit':
        return 'Summit';
      case 'clinical':
        return 'Clinical';
      case 'free':
        return 'Free';
      default:
        return tier[0].toUpperCase() + tier.substring(1);
    }
  }

  /// Gets the subscription tier from user data
  static String getUserSubscriptionTier(WidgetRef ref) {
    final user = ref.watch(authStateProvider)?.user;

    if (user?.hasActiveSubscription == true &&
        user?.activeSubscription != null) {
      return user!.activeSubscription!.plan.tier;
    }

    return 'free';
  }

  /// Gets the display text for subscription tier
  static String getSubscriptionDisplayText(WidgetRef ref) {
    final tier = getUserSubscriptionTier(ref);
    return capitalizeTierName(tier);
  }

  /// Builds a subscription badge widget
  static Widget buildSubscriptionBadge(WidgetRef ref) {
    final tier = getUserSubscriptionTier(ref);
    final displayText = capitalizeTierName(tier);
    final backgroundColor = AppColors.subscriptionFree;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(
        displayText,
        style: TextStyles.buttonSmall.copyWith(
          fontWeight: FontWeight.w600,
          letterSpacing: -0.3,
          color: Colors.white,
        ),
      ),
    );
  }

  /// Checks if user has active subscription
  static bool hasActiveSubscription(WidgetRef ref) {
    final user = ref.watch(authStateProvider)?.user;
    return user?.hasActiveSubscription == true &&
        user?.activeSubscription != null;
  }

  /// Checks if user has specific tier subscription
  static bool hasSubscriptionTier(WidgetRef ref, String tier) {
    if (!hasActiveSubscription(ref)) return false;
    final userTier = getUserSubscriptionTier(ref);
    return userTier.toLowerCase() == tier.toLowerCase();
  }

  /// Gets subscription expiry date
  static DateTime? getSubscriptionExpiryDate(WidgetRef ref) {
    final user = ref.watch(authStateProvider)?.user;

    if (user?.hasActiveSubscription == true &&
        user?.activeSubscription != null) {
      return user!.activeSubscription!.expiryTime;
    }

    return null;
  }

  /// Checks if subscription is expired
  static bool isSubscriptionExpired(WidgetRef ref) {
    final expiryDate = getSubscriptionExpiryDate(ref);
    if (expiryDate == null) return true;

    return DateTime.now().isAfter(expiryDate);
  }

  /// Gets days remaining in subscription
  static int? getDaysRemaining(WidgetRef ref) {
    final expiryDate = getSubscriptionExpiryDate(ref);
    if (expiryDate == null) return null;

    final now = DateTime.now();
    if (now.isAfter(expiryDate)) return 0;

    return expiryDate.difference(now).inDays;
  }

  // ========== SUBSCRIPTION CREATION HELPERS ==========

  /// Creates an ActiveSubscription object from a product ID
  static ActiveSubscription createActiveSubscriptionFromProductId(
      String productId) {
    final now = DateTime.now();
    final subscriptionInfo = parseProductId(productId);

    // Generate random IDs (in a real app, these would come from the server)
    final subscriptionId = generateRandomId();
    final planId = generateRandomId();

    // Calculate expiry time based on billing period
    final expiryTime = subscriptionInfo.billingPeriod == 'monthly'
        ? now.add(const Duration(days: 30))
        : now.add(const Duration(days: 365));

    final plan = Plan(
      id: planId,
      tier: subscriptionInfo.tier,
      name: subscriptionInfo.name,
      billingPeriod: subscriptionInfo.billingPeriod,
      productId: productId,
      durationDays: subscriptionInfo.durationDays,
    );

    return ActiveSubscription(
      id: subscriptionId,
      plan: plan,
      status: 'active',
      startTime: now,
      expiryTime: expiryTime,
    );
  }

  /// Parses product ID to extract subscription information
  static ({String tier, String name, String billingPeriod, int durationDays})
      parseProductId(String productId) {
    // Product ID format: com.neuroworld.dev.{tier}.{period}
    // Examples:
    // - com.neuroworld.dev.habit.monthly
    // - com.neuroworld.dev.habit.yearly
    // - com.neuroworld.dev.summit.monthly
    // - com.neuroworld.dev.clinical.yearly

    final parts = productId.split('.');
    if (parts.length < 5) {
      throw Exception('Invalid product ID format: $productId');
    }

    final tier = parts[3]; // habit, summit, clinical, academy
    final period = parts[4]; // monthly, yearly

    // Map academy and habit to habits (based on the product IDs in purchase_service.dart)
    final normalizedTier =
        (tier == 'academy' || tier == 'habit') ? 'habits' : tier;

    // Create human-readable name
    final tierName = capitalizeTierName(normalizedTier);
    final periodName = period == 'monthly' ? 'Monthly' : 'Yearly';
    final name = '$tierName $periodName';

    // Calculate duration days
    final durationDays = period == 'monthly' ? 30 : 365;

    return (
      tier: normalizedTier,
      name: name,
      billingPeriod: period,
      durationDays: durationDays,
    );
  }

  /// Generates a random ID (UUID-like)
  static String generateRandomId() {
    final random = Random();
    const chars = 'abcdef0123456789';

    String generateSegment(int length) {
      return List.generate(
          length, (index) => chars[random.nextInt(chars.length)]).join();
    }

    return '${generateSegment(8)}-${generateSegment(4)}-${generateSegment(4)}-${generateSegment(4)}-${generateSegment(12)}';
  }
}
