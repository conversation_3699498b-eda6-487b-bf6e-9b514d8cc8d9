import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/fonts.gen.dart';
import 'package:neuroworld/core/l10n/app_localizations.dart';

enum AppLocale {
  english('en', FontFamily.sFProText);

  const AppLocale(this.code, this.fontFamily);

  final String code;
  final String fontFamily;

  String getLanguageName(BuildContext context) {
    return switch (this) {
      AppLocale.english => context.L.english,
    };
  }
}

extension ContextLocale on BuildContext {
  AppLocalizations get L => AppLocalizations.of(this)!;
}
