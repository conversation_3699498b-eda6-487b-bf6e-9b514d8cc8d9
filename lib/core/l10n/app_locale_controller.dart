import 'package:intl/date_symbol_data_local.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'app_locale_controller.g.dart';

@Riverpod(keepAlive: true)
class AppLocaleController extends _$AppLocaleController {
  bool _firstBuild = true;

  @override
  FutureOr<AppLocale> build() {
    if (_firstBuild) {
      _initDateFormatting();
      _firstBuild = false;
    }
    // returning english by default
    // in future if we have diff locales, can store & fetch this from shared pref
    return AppLocale.english;
  }

  Future<void> _initDateFormatting() async {
    await initializeDateFormatting();
  }
}
