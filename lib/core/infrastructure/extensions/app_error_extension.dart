import 'package:flutter/material.dart';
import 'package:neuroworld/core/infrastructure/error/app_exceptions.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

extension AppErrorExtension on Object {
  String errorMessage(BuildContext context) {
    final error = this;
    return error is AppException
        ? switch (error) {
            ServerException() => error.serverErrorMessage(context),
            CacheException() => error.cacheErrorMessage(context),
            AuthException() => error.authErrorMessage(context),
            ChatException() => error.chatErrorMessage(context),
          }
        : context.L.unknownError;
  }
}

extension _ServerErrorMessageExtension on ServerException {
  String serverErrorMessage(BuildContext context) {
    return switch (type) {
      ServerExceptionType.general =>
        message, //Business logic error message from the backend
      ServerExceptionType.unauthorized =>
        message.isNotEmpty ? message : context.L.unauthorizedError,
      ServerExceptionType.forbidden =>
        message.isNotEmpty ? message : context.L.forbiddenError,
      ServerExceptionType.notFound => context.L.notFoundError,
      ServerExceptionType.conflict => context.L.conflictError,
      ServerExceptionType.internal => context.L.internalError,
      ServerExceptionType.serviceUnavailable =>
        context.L.serviceUnavailableError,
      ServerExceptionType.timeOut => context.L.timeoutError,
      ServerExceptionType.noInternet => context.L.noInternetError,
      ServerExceptionType.unknown => context.L.unknownError,
    };
  }
}

extension _CacheErrorMessageExtension on CacheException {
  String cacheErrorMessage(BuildContext context) {
    return switch (type) {
      CacheExceptionType.general => message,
      _ => context.L.unknownError,
    };
  }
}

extension _AuthErrorMessageExtension on AuthException {
  String authErrorMessage(BuildContext context) {
    return switch (type) {
      AuthExceptionType.googleSSO => context.L.googleSSOError,
      AuthExceptionType.appleSSO => context.L.appleSSOErrorUnknown,
      AuthExceptionType.canceled => context.L.ssoErrorCanceled,
      _ => context.L.unknownError,
    };
  }
}

extension _ChatErrorMessageExtension on ChatException {
  String chatErrorMessage(BuildContext context) {
    return switch (type) {
      ChatExceptionType.invalidFormat => context.L.chatErrorGeneric,
      _ => context.L.chatErrorGeneric,
    };
  }
}
