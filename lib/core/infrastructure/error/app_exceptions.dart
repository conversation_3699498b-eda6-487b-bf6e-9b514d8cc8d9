import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_exceptions.freezed.dart';
part 'auth_exception.dart';
part 'cache_exception_type.dart';
part 'chat_exception_type.dart';
part 'server_exception_type.dart';

@freezed
sealed class AppException with _$AppException implements Exception {
  const factory AppException.serverException({
    required ServerExceptionType type,
    required String message,
    int? code,
  }) = ServerException;

  const factory AppException.cacheException({
    required CacheExceptionType type,
    required String message,
    int? code,
  }) = CacheException;

  const factory AppException.authException({
    required AuthExceptionType type,
    String? message,
    int? code,
  }) = AuthException;

  const factory AppException.chatException({
    required ChatExceptionType type,
    String? message,
    int? code,
  }) = ChatException;
}
