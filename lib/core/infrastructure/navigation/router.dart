import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/infrastructure/navigation/transitions.dart';
import 'package:neuroworld/core/ui/screens/route_error_screen.dart';
import 'package:neuroworld/core/ui/screens/splash_error_screen.dart';
import 'package:neuroworld/core/ui/screens/splash_screen.dart';
import 'package:neuroworld/modules/auth/data/models/response/auth.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/auth/ui/screens/forgot_password_screen.dart';
import 'package:neuroworld/modules/auth/ui/screens/landing_screen.dart';
import 'package:neuroworld/modules/auth/ui/screens/landing_signup_screen.dart';
import 'package:neuroworld/modules/auth/ui/screens/login_screen.dart';
import 'package:neuroworld/modules/auth/ui/screens/onboarding_screen.dart';
import 'package:neuroworld/modules/auth/ui/screens/privacy_policy_screen.dart';
import 'package:neuroworld/modules/auth/ui/screens/reset_password_screen.dart';
import 'package:neuroworld/modules/auth/ui/screens/signup_screen.dart';
import 'package:neuroworld/modules/auth/ui/screens/terms_and_conditions_screen.dart';
import 'package:neuroworld/modules/badges/ui/screens/badges_screen.dart';
import 'package:neuroworld/modules/chat/ui/screens/chat_screen.dart';
import 'package:neuroworld/modules/goals/ui/screens/add_goal.dart';
import 'package:neuroworld/modules/goals/ui/screens/goals_screen.dart';
import 'package:neuroworld/modules/home/<USER>/screens/delete_account/delete_account_screen.dart';
import 'package:neuroworld/modules/home/<USER>/screens/delete_account/delete_account_success_screen.dart';
import 'package:neuroworld/modules/home/<USER>/screens/feedback/feedback_screen.dart';
import 'package:neuroworld/modules/home/<USER>/screens/home_screen.dart';
import 'package:neuroworld/modules/home/<USER>/screens/profile/complete_profile_screen.dart';
import 'package:neuroworld/modules/home/<USER>/screens/profile/edit_profile_screen.dart';
import 'package:neuroworld/modules/home/<USER>/screens/profile/profile_screen.dart';
import 'package:neuroworld/modules/home_shell/ui/screens/home_shell_screen.dart';
import 'package:neuroworld/modules/map/ui/screens/map_screen.dart';
import 'package:neuroworld/modules/notification/ui/screens/notification_screen.dart';
import 'package:neuroworld/modules/onboarding/ui/screens/add_goal_screen.dart';
import 'package:neuroworld/modules/subscriptions/ui/screens/subscription_screen.dart';
import 'package:neuroworld/modules/summit/ui/screens/summit_screen.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

// navigation infrastructure
part 'nav_extension.dart';
part 'nav_permissions.dart';
// gen
part 'router.g.dart';
// routes
part 'routes/auth_routes.dart';
part 'routes/core_routes.dart';
part 'routes/goals_routes.dart';
part 'routes/home_routes.dart';
part 'routes/home_shell_route.dart';
part 'routes/map_routes.dart';
part 'routes/rewards_routes.dart';
part 'routes/summit_routes.dart';

final _rootNavigatorKey = GlobalKey<NavigatorState>();

@riverpod
GoRouter goRouter(Ref ref) {
  // construct listenable for GoRouter auth state refreshes
  final listenable = ValueNotifier<bool?>(null);

  ref.listen<bool>(
    authStateProvider.select((auth) => auth == null),
    (_, isAuthenticated) => listenable.value = isAuthenticated,
  );

  final router = GoRouter(
    debugLogDiagnostics: true,
    restorationScopeId: 'router',
    navigatorKey: _rootNavigatorKey,
    initialLocation: const SplashRoute().location,
    routes: $appRoutes,
    redirect: (BuildContext context, GoRouterState state) {
      final authState = ref.read(authStateProvider);
      final navPermissions = state.navPermissions;
      // return null (no redirecting) if the user is at or heading to a valid route.
      if (navPermissions.contains(NavPermissions.fromAuthState(authState))) {
        return null;
      }
      return authState == null
          ? const LandingRoute().location
          : !authState.user.profile.isOnboardingCompleted
              ? const OnboardingRoute().location
              : const HomeRoute().location;
    },
    refreshListenable: listenable,
    errorBuilder: (_, state) => RouteErrorScreen(state.error),
  );

  ref.onDispose(() {
    listenable.dispose();
    router.dispose();
  });

  return router;
}
