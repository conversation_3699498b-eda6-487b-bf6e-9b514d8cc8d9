part of '../router.dart';

class GoalsRoute extends GoRouteData {
  const GoalsRoute();

  static const routes = [
    TypedGoRoute<GoalsRoute>(
      path: '/goals',
      routes: [],
    ),
  ];

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const GoalsScreen();
}

@TypedGoRoute<AddGoalRoute>(path: '/home-add-goal')
class AddGoalRoute extends GoRouteData {
  const AddGoalRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return AddGoal();
  }
}
