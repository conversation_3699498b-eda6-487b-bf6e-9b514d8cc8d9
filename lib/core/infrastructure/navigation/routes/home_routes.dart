part of '../router.dart';

class HomeRoute extends GoRouteData {
  const HomeRoute();

  static const routes = [
    TypedGoRoute<HomeRoute>(
      path: '/home',
      routes: [],
    ),
  ];

  @override
  Widget build(BuildContext context, GoRouterState state) => const HomeScreen();
}

@TypedGoRoute<ChatRoute>(path: '/chat')
class ChatRoute extends GoRouteData {
  const ChatRoute();

  // explicitly push on root navigator so we don't use
  // home shell's app bar and other scaffold styling
  static final GlobalKey<NavigatorState> $parentNavigatorKey =
      _rootNavigatorKey;

  @override
  Widget build(BuildContext context, GoRouterState state) => const ChatScreen();
}

@TypedGoRoute<ProfileRoute>(path: '/profile')
class ProfileRoute extends GoRouteData {
  const ProfileRoute();

  // explicitly push on root navigator so we don't use
  // home shell's app bar and other scaffold styling
  static final GlobalKey<NavigatorState> $parentNavigatorKey =
      _rootNavigatorKey;

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const ProfileScreen();
}

@TypedGoRoute<EditProfileRoute>(path: '/edit-profile')
class EditProfileRoute extends GoRouteData {
  const EditProfileRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey =
      _rootNavigatorKey;

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const EditProfileScreen();
}

@TypedGoRoute<CompleteProfileRoute>(path: '/complete-profile')
class CompleteProfileRoute extends GoRouteData {
  const CompleteProfileRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey =
      _rootNavigatorKey;

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const CompleteProfileScreen();
}

@TypedGoRoute<FeedbackRoute>(path: '/feedback')
class FeedbackRoute extends GoRouteData {
  const FeedbackRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey =
      _rootNavigatorKey;

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const FeedbackScreen();
}

@TypedGoRoute<NotificationRoute>(path: '/notification')
class NotificationRoute extends GoRouteData {
  const NotificationRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey =
      _rootNavigatorKey;

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const NotificationScreen();
}

@TypedGoRoute<DeleteAccountRoute>(path: '/delete-account')
class DeleteAccountRoute extends GoRouteData {
  const DeleteAccountRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey =
      _rootNavigatorKey;

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const DeleteAccountScreen();
}

@TypedGoRoute<DeleteAccountSuccessRoute>(path: '/delete-account-success')
class DeleteAccountSuccessRoute extends GoRouteData {
  const DeleteAccountSuccessRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey =
      _rootNavigatorKey;

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const DeleteAccountSuccessScreen();
}

@TypedGoRoute<SubscriptionRoute>(path: '/subscription')
class SubscriptionRoute extends GoRouteData {
  const SubscriptionRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey =
      _rootNavigatorKey;

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const SubscriptionScreen();
}
