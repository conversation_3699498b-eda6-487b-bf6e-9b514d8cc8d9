part of '../router.dart';

@TypedGoRoute<SplashRoute>(path: '/splash')
class SplashRoute extends GoRouteData {
  const SplashRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const SplashScreen();
}

@TypedGoRoute<SplashErrorRoute>(path: '/splash_error')
class SplashErrorRoute extends GoRouteData {
  const SplashErrorRoute({required this.message});
  final String message;

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      SplashErrorScreen(message: message);
}
