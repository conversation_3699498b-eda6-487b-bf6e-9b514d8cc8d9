import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:intl/intl.dart';

final requestDateFormat = DateFormat('yyyy-MM-dd');

/// converts date string in format yyyy-MM-dd to DateTime, and vice-versa
class DateConverter implements JsonConverter<DateTime?, String?> {
  const DateConverter();

  @override
  DateTime? fromJson(String? s) => s == null ? null : DateTime.parse(s);

  @override
  String? toJson(DateTime? dt) =>
      dt == null ? null : requestDateFormat.format(dt);
}
