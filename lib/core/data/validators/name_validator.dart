import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

enum NameValidationError {
  empty(),
  invalid(),
  maxLength();

  const NameValidationError();

  String message(BuildContext context) {
    return switch (this) {
      NameValidationError.empty => context.L.validationNameRequired,
      NameValidationError.invalid => context.L.validationNamePattern,
      NameValidationError.maxLength => context.L.nameLengthValidation,
    };
  }
}

class NameInput extends FormzInput<String, NameValidationError> {
  const NameInput.pure([super.value = '']) : super.pure();

  const NameInput.dirty([super.value = '']) : super.dirty();

  static final _nameRegExp = RegExp(r'^[a-zA-Z,.\-\s]+$');

  @override
  NameValidationError? validator(String value) {
    if (value.isEmpty) return NameValidationError.empty;
    if (!_nameRegExp.hasMatch(value)) return NameValidationError.invalid;
    if (value.length > 30) return NameValidationError.maxLength;
    return null;
  }
}
