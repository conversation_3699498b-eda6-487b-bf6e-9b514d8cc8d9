import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

enum EmailValidationError {
  empty(),
  maxLength(),
  invalid();

  const EmailValidationError();

  String message(BuildContext context) {
    return switch (this) {
      EmailValidationError.empty => context.L.validationEmailRequired,
      EmailValidationError.invalid => context.L.validationEmailPattern,
      EmailValidationError.maxLength => context.L.validationEmailMaxLength,
    };
  }
}

class EmailInput extends FormzInput<String, EmailValidationError>
    with FormzInputErrorCacheMixin {
  EmailInput.pure([super.value = '']) : super.pure();

  EmailInput.dirty([super.value = '']) : super.dirty();

  static final _emailRegExp = RegExp(
    r'^[a-zA-Z\d_.+-]+@[a-zA-Z\d-]+(?:\.[a-zA-Z\d-]+)*$',
  );

  @override
  EmailValidationError? validator(String value) {
    if (value.isEmpty) return EmailValidationError.empty;
    if (!_emailRegExp.hasMatch(value)) return EmailValidationError.invalid;
    if (value.length > 50) return EmailValidationError.maxLength;
    return null;
  }
}
