import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

enum PasswordValidationError {
  empty(),
  maxLength(),
  small();

  const PasswordValidationError();

  String message(BuildContext context) {
    return switch (this) {
      PasswordValidationError.empty => context.L.validationPasswordRequired,
      PasswordValidationError.small => context.L.validationPasswordLength,
      PasswordValidationError.maxLength =>
        context.L.validationPasswordMaxLength,
    };
  }
}

class PasswordInput extends FormzInput<String, PasswordValidationError> {
  const PasswordInput.pure() : super.pure('');
  const PasswordInput.dirty([super.value = '']) : super.dirty();

  @override
  PasswordValidationError? validator(String value) {
    if (value.isEmpty) return PasswordValidationError.empty;
    if (value.length < 8) return PasswordValidationError.small;
    if (value.length > 50) return PasswordValidationError.maxLength;
    return null;
  }
}
