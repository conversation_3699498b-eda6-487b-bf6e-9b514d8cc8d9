import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/shared_prefs_keys.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'shared_preferences.g.dart';

@Riverpod(keepAlive: true)
Future<SharedPreferencesWithCache> sharedPreferences(Ref ref) =>
    SharedPreferencesWithCache.create(
      cacheOptions: const SharedPreferencesWithCacheOptions(
        // When an allowlist is included, any keys that aren't included cannot be used.
        allowList: <String>{
          SharedPrefsKeys.auth,
          SharedPrefsKeys.readNotifications,
          SharedPrefsKeys.markAllReadNotifications
        },
      ),
    );
