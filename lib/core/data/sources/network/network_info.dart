import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'network_info.g.dart';

@Riverpod(keepAlive: true)
NetworkInfo networkInfo(Ref ref) => NetworkInfo(
      Connectivity(),
    );

class NetworkInfo {
  NetworkInfo(this.connectivity);

  final Connectivity connectivity;

  Future<bool> get hasInternetConnection async =>
      !(await connectivity.checkConnectivity())
          .any((result) => result == ConnectivityResult.none);

  Future<List<ConnectivityResult>> get hasNetworkConnectivity =>
      connectivity.checkConnectivity();
}
