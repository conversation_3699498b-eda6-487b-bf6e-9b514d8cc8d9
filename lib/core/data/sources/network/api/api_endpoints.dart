/// This class contains all the root endpoints for the NeuroWorld API.
/// Each endpoint is documented with the possible HTTP methods and their descriptions.
class ApiEndpoints {
  /// apple/token/
  ///
  /// **POST:** Used to create a new Apple token by sending the required data in the body.
  ///
  /// **Response:** Returns a 201 Created status with the token details on successful creation.
  static final appleToken = 'apple/token/';

  /// google/token/
  ///
  /// **POST:** Used to create a new Google token by sending the required data in the body.
  ///
  /// **Response:** Returns a 201 Created status with the token details on successful creation.
  static final googleToken = 'google/token/';

  /// token/
  ///
  /// **POST:** Used to obtain a JSON Web Token (JWT) pair (access and refresh tokens) using user credentials.
  ///
  /// **Response:** Returns a 201 Created status with the access and refresh tokens.
  static final token = 'token/';

  /// token/refresh/
  ///
  /// **POST:** Used to refresh an access token by providing a valid refresh token.
  ///
  /// **Response:** Returns a 201 Created status with the new access token.
  static final tokenRefresh = 'token/refresh/';

  /// logout/
  ///
  /// **POST:** Used to logout, can send fcm_token in body
  ///
  static final logout = 'logout/';

  /// me/
  ///
  /// **GET:** Retrieve details about the authenticated user, their profile, and selected goals.
  static final me = 'me/';

  /// user/profile/
  ///
  /// **GET:** Retrieve a list of UserProfile objects. Supports filtering by id and user using query parameters.
  ///
  /// **POST:** Create a new UserProfile object by sending the required data in the body.
  ///
  /// **Response:**
  /// - GET returns a 200 OK status with a list of UserProfile objects.
  /// - POST returns a 201 Created status with the created UserProfile object.
  static final userProfile = 'user/profile/';

  /// user/profile/{id}/
  ///
  /// **GET:** Retrieve a UserProfile object by its ID.
  ///
  /// **PUT:** Update a UserProfile object by its ID. Requires sending updated data in the body.
  ///
  /// **PATCH:** Partially update a UserProfile object by its ID. Requires sending partial updated data in the body.
  ///
  /// **DELETE:** Delete a UserProfile object by its ID.
  ///
  /// **Response:**
  /// - GET returns a 200 OK status with the UserProfile object.
  /// - PUT and PATCH return a 200 OK status with the updated UserProfile object.
  /// - DELETE returns a 204 No Content status on successful deletion.
  static String userProfileById(String id) => 'user/profile/$id/';

  /// users/
  ///
  /// **GET:** Retrieve a list of User objects. Supports filtering by id, username, and auth_provider using query parameters.
  ///
  /// **POST:** Create a new User object by sending the required data in the body.
  ///
  /// **Response:**
  /// - GET returns a 200 OK status with a list of User objects.
  /// - POST returns a 201 Created status with the created User object.
  static final users = 'users/';

  /// users/{id}/
  ///
  /// **GET:** Retrieve a User object by its ID.
  ///
  /// **PUT:** Update a User object by its ID. Requires sending updated data in the body.
  ///
  /// **PATCH:** Partially update a User object by its ID. Requires sending partial updated data in the body.
  ///
  /// **DELETE:** Delete a User object by its ID.
  ///
  /// **Response:**
  /// - GET returns a 200 OK status with the User object.
  /// - PUT and PATCH return a 200 OK status with the updated User object.
  /// - DELETE returns a 204 No Content status on successful deletion.
  static String userById(String id) => 'users/$id/';

  /// goal/{id}/
  ///
  /// **GET:** Retrieve details for a specific goal.
  ///
  /// **PUT:** Update a specific goal.
  ///
  /// **PATCH:** Partially update a goal.
  ///
  /// **DELETE:** Delete a specific goal.
  static String goalById(String id) => 'goal/$id/';

  /// goal/
  ///
  /// **GET:** Retrieve a list of goals.
  ///
  /// **POST:** Create a new goal.
  static final goals = 'goal/';

  /// goals/tracking/
  ///
  /// **GET:** Retrieve tracking information for goals within a specified date range.
  ///
  /// **POST:** Track a goal (mark it complete) for a specific date.
  static final tracking = 'goals/tracking/';

  /// subscriptions/
  ///
  /// **GET:** Retrieve available subscription plans with features and pricing.
  static final subscriptions = 'subscriptions/';

  /// user/goal/
  ///
  /// **POST:** Create a new user goal
  ///
  /// **Response:** Returns a 201 Created status with the selected goal details.
  static final userGoal = 'user/goal/';

  /// user/goal/{id}/
  ///
  /// **PATCH:** Edit Goal
  static String userGoalByid(String id) => 'user/goal/$id/';

  /// user/goal/restore/
  ///
  /// **POST:** Restore a user's goal by providing `selected_goal_id` and `restore_date` in YYYY-MM-DD format.
  ///
  /// **Response:** Returns a 201 Created status with details of the restored goal.
  static final userGoalRestore = 'user/goal/restore/';

  /// selected-goals/{id}/
  ///
  /// **GET:** Retrieve a specific selected goal.
  ///
  /// **PUT:** Update a selected goal.
  ///
  /// **PATCH:** Partially update a selected goal.
  ///
  /// **DELETE:** Delete a selected goal.
  static String selectedGoalById(String id) => 'selected-goals/$id/';

  /// selected-goals/
  ///
  /// **GET:** Retrieve a list of selected goals.
  ///
  /// **POST:** Create a new selected goal.
  static final selectedGoals = 'selected-goals/';

  /// stream/
  ///
  /// **POST:** Stream chat messages.
  ///
  /// **Response:** Returns a real-time stream of chat messages.
  static final stream = 'stream/';

  /// chats/?page={page}&page_size={pageSize}
  ///
  /// **GET:** Retrieve a paginated list of chat messages.
  ///
  /// **Query Parameters:**
  /// - `page`: The page number to retrieve. Starts from 1.
  /// - `page_size`: The number of chat messages per page.
  ///
  /// **Response:** Returns a 200 OK status with a paginated list of chat messages, including metadata like total count, next, and previous links.
  static final chats = 'chats/';

  /// chats/{id}/
  ///
  /// **GET:** Retrieve chat details for a specific chat ID.
  ///
  /// **PUT:** Update chat messages for a specific chat ID.
  ///
  /// **PATCH:** Partially update chat messages for a specific chat ID.
  ///
  /// **DELETE:** Delete a specific chat.
  static String chatById(String id) => 'chats/$id/';

  /// user/upload-profile-picture/
  ///
  /// **POST:** Used to upload a new profile picture for the user. The image file should be sent as form-data in the request body.
  ///
  /// **Response:** Returns a 200 OK status with the updated user profile details if the profile picture is successfully updated.
  static final uploadProfilePicture = 'user/upload-profile-picture/';

  /// feedback/
  ///
  /// **POST:** Used to submit feedback by sending a comment and score in the request body.
  ///
  /// **Response:** Returns a 201 Created status with the feedback details upon successful submission.
  static final feedback = 'feedback/';

  /// notifications/
  ///
  /// **GET:** Get notifications
  ///
  /// **Response:** Returns a 200 if notification retrieved successfully.
  static final notification = 'notifications/';

  /// users/{id}
  ///
  /// **DELETE:** Permanently deletes a User object by the specified `id`.
  ///
  /// **Response:**
  /// - DELETE returns a 200 OK status if the user is successfully deleted.
  /// - Returns a 404 Not Found status if the user with the provided `id` does not exist.
  static String deleteUser(String id) => 'users/$id/';

  /// forgot-password/
  ///
  /// **POST:** Sends a request to initiate the password recovery process for a user.
  ///
  /// **Response:**
  /// - POST returns a 200 OK status if the password recovery email is successfully sent.
  /// - Returns a 404 Not Found status if the user with the provided email does not exist.
  static String forgotPassword = 'forgot-password/';

  /// The API endpoint for resetting a user's password.
  /// This endpoint is used to initiate or complete the password reset process.
  static String resetPassword = 'reset-password/';
}
