import 'package:dio/dio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/config/api_config.dart';
import 'package:neuroworld/core/data/sources/network/api/error_extensions.dart';
import 'package:neuroworld/core/data/sources/network/api/interceptors/auth_interceptor.dart';
import 'package:neuroworld/core/data/sources/network/api/interceptors/content_type_interceptor.dart';
import 'package:neuroworld/core/data/sources/network/api/interceptors/error_interceptor.dart';
import 'package:neuroworld/core/data/sources/network/api/interceptors/refresh_token_interceptor.dart';
import 'package:neuroworld/core/data/sources/network/api/interceptors/session_expiry_interceptor.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'api.g.dart';

@Riverpod(keepAlive: true)
Dio dio(Ref ref) => Dio()
  ..options = BaseOptions(
    baseUrl: ApiConfig.baseUrl,
    connectTimeout: ApiConfig.connectTimeout,
    receiveTimeout: ApiConfig.receiveTimeout,
  )
  ..interceptors.addAll([
    // order is important
    ContentTypeInterceptor(),
    AuthInterceptor(ref),
    RefreshTokenInterceptor(ref),
    ErrorInterceptor(),
    SessionExpiryInterceptor(ref),
  ]);

@Riverpod(keepAlive: true)
Api api(Ref ref) {
  return Api(
    dio: ref.watch(dioProvider),
  );
}

class Api {
  Api({required this.dio});

  final Dio dio;

  Future<Response<T>> get<T>({
    required String path,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return _errorHandler(
      () async {
        return dio.get(
          path,
          queryParameters: queryParameters,
          //Every request can pass an Options object which will be merged with Dio.options
          options: options,
          cancelToken: cancelToken,
        );
      },
    );
  }

  Future<Response<T>> post<T>({
    required String path,
    Map<String, dynamic>? queryParameters,
    dynamic data,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return _errorHandler(
      () async {
        return dio.post(
          path,
          queryParameters: queryParameters,
          data: data,
          options: options,
          cancelToken: cancelToken,
        );
      },
    );
  }

  Future<Response<T>> patch<T>({
    required String path,
    dynamic data,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return _errorHandler(
      () async {
        return dio.patch(
          path,
          data: data,
          options: options,
          cancelToken: cancelToken,
        );
      },
    );
  }

  Future<Response<T>> put<T>({
    required String path,
    dynamic data,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return _errorHandler(
      () async {
        return dio.put(
          path,
          data: data,
          options: options,
          cancelToken: cancelToken,
        );
      },
    );
  }

  Future<Response<T>> delete<T>({
    required String path,
    dynamic data,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return _errorHandler(
      () async {
        return dio.delete(
          path,
          data: data,
          options: options,
          cancelToken: cancelToken,
        );
      },
    );
  }

  Future<T> _errorHandler<T>(Future<T> Function() request) async {
    try {
      return await request();
    } catch (e, st) {
      throw Error.throwWithStackTrace(e.apiErrorToServerException(), st);
    }
  }
}
