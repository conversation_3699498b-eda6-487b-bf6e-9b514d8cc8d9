import 'dart:async';

import 'package:dio/dio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/config/api_config.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';

class RefreshTokenInterceptor extends QueuedInterceptorsWrapper {
  final Ref ref;
  RefreshTokenInterceptor(this.ref);

  bool _isRefreshing = false;
  Completer<void>? _refreshCompleter;

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode != 401) return handler.next(err);

    final auth = ref.read(authStateProvider);
    if (auth == null) {
      // refresh not possible, throw 511 session expired
      err.response?.statusCode = 511;
      return handler.next(err);
    }
    // no other 401 refresh handlers in queue, start completer and begin refresh token flow
    if (!_isRefreshing) {
      _isRefreshing = true;
      _refreshCompleter = Completer<void>();
      try {
        final newAuth =
            await ref.read(authServiceProvider).refreshToken(auth, err);
        ref.read(authStateProvider.notifier).login(newAuth);
        _refreshCompleter?.complete();
      } catch (e) {
        _refreshCompleter?.completeError(e);
      } finally {
        _isRefreshing = false;
      }
    }
    // refresh handler is already in queue, wait for new access token (or error)
    try {
      await _refreshCompleter?.future;
      // use new Dio to avoid deadlock with main Dio interceptors
      Dio retryDio = Dio(BaseOptions(baseUrl: ApiConfig.baseUrl));
      // user state should have new access token, retry request
      err.requestOptions.headers['Authorization'] =
          'Bearer ${ref.read(authStateProvider)!.access}';

      final response = await retryDio.fetch(err.requestOptions);
      return handler.resolve(response);
    } catch (e) {
      // catch 511 session expired errors
      return handler.next(err);
    }
  }
}
