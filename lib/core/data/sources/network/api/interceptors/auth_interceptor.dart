import 'package:dio/dio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';

class AuthInterceptor extends Interceptor {
  final Ref ref;
  AuthInterceptor(this.ref);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final auth = ref.read(authStateProvider);

    if (auth != null) {
      options.headers['Authorization'] = 'Bearer ${auth.access}';
    }

    return handler.next(options);
  }
}
