// ignore_for_file: avoid_dynamic_calls

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

@visibleForTesting
class RejectError implements Exception {}

class ErrorInterceptor extends Interceptor {
  // this is needed if the api uses status code 200 for business errors
  // @override
  // void onResponse(Response<dynamic> response, ResponseInterceptorHandler handler) {
  //   final data = response.data as Map<dynamic, dynamic>;

  //   if (data['status'] != 'OK') {
  //     final error = DioException(
  //       response: response,
  //       requestOptions: response.requestOptions,
  //       error: RejectError(),
  //     );
  //     return handler.reject(error, true);
  //   }

  //   return handler.next(response);
  // }

  @override
  Future<void> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    if (err.response?.statusCode == 400 ||
        err.response?.statusCode == 403 ||
        err.error is RejectError) {
      final response = err.response!;
      final data = response.data as Map<dynamic, dynamic>;
      final error = data['detail'] ?? data['error'] ?? data['status'];
      return handler.reject(
        err.copyWith(
          response: Response(
            statusCode: err.response?.statusCode,
            data: data,
            requestOptions: response.requestOptions,
            statusMessage: response.statusMessage,
            isRedirect: response.isRedirect,
            redirects: response.redirects,
            extra: response.extra,
            headers: response.headers,
          ),
          error: error,
          type: DioExceptionType.badResponse,
        ),
      );
    }

    return handler.next(err);
  }
}
