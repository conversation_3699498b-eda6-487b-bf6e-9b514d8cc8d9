import 'package:dio/dio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/modules/auth/ui/providers/sign_out_provider.dart';

class SessionExpiryInterceptor extends QueuedInterceptorsWrapper {
  final Ref ref;
  SessionExpiryInterceptor(this.ref);

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode != 511) return handler.next(err);
    ref.read(signOutProvider.notifier).signOut();
  }
}
