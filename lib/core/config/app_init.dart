part of '../../../main.dart';

Future<ProviderContainer> _mainInitializer() async {
  debugPrint("ENV: ${const String.fromEnvironment('ENV')}");
  debugPrint("API URL: ${ApiConfig.baseUrl}");

  final widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  await _initializeFirebase();

  final container = ProviderContainer(observers: []);
  // need shared prefs to be warmed up before splash
  // as it is dependency for other startup providers (checkAuthProvider, authTokensProvider)
  await container.read(sharedPreferencesProvider.future).suppressError();
  // This Prevent closing native splash screen until we finish warming-up custom splash images.
  // App layout will be built but not displayed.
  widgetsBinding.deferFirstFrame();
  widgetsBinding.addPostFrameCallback((_) async {
    // Run any function you want to wait for before showing app layout.
    final BuildContext context = widgetsBinding.rootElement!;
    await _precacheAssets(context);
    // When the native splash screen is fullscreen, iOS will not automatically show the notification
    // bar when the app loads. To show it, setEnabledSystemUIMode has to be explicitly set:
    // https://github.com/flutter/flutter/issues/105714
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
    // only portrait mode
    await SystemChrome.setPreferredOrientations(
      [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown],
    );
    // Closes splash screen, and show the app layout.
    widgetsBinding.allowFirstFrame();
  });
  return container;
}

Future<void> _precacheAssets(BuildContext context) async {
  await <Future<void>>[
    SplashScreen.precacheAssets(context),
  ].wait.suppressError();
}

Future<void> _initializeFirebase() async {
  //determine which Firebase options to use based on the env
  final firebaseOptions = switch (const String.fromEnvironment('ENV')) {
    'dev' => dev.DefaultFirebaseOptions.currentPlatform,
    'qa' => qa.DefaultFirebaseOptions.currentPlatform,
    'staging' => staging.DefaultFirebaseOptions.currentPlatform,
    'prod' => prod.DefaultFirebaseOptions.currentPlatform,
    _ => throw UnsupportedError('Invalid Config'),
  };
  await Firebase.initializeApp(options: firebaseOptions);
  FirebaseMessaging.onBackgroundMessage(_fcmBackgroundHandler);
}

/// This provided handler must be a top-level function.
/// It works outside the scope of the app in its own isolate.
/// More details: https://firebase.google.com/docs/cloud-messaging/flutter/receive#background_messages
@pragma('vm:entry-point')
Future<void> _fcmBackgroundHandler(RemoteMessage message) async {
  print('Handling a background message ${message.messageId}');
}
