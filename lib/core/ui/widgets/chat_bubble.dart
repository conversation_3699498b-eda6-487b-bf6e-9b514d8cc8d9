import 'dart:math';

import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/modules/auth/ui/widgets/brain_assistant.dart';

class ChatBubble extends StatelessWidget {
  const ChatBubble({
    super.key,
    this.toolTipAlignment, // hidden by default
    this.backgroundColor,
    required this.child,
  });

  final Alignment? toolTipAlignment;
  final Widget child;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: toolTipAlignment ?? Alignment.center,
      children: [
        Container(
          decoration: BoxDecoration(
            color: backgroundColor ?? AppColors.chatBubble,
            borderRadius: BorderRadius.circular(8),
          ),
          margin: toolTipAlignment == null
              ? EdgeInsets.zero
              : toolTipAlignment!.x == -1.0
                  ? const EdgeInsets.only(left: 5)
                  : const EdgeInsets.only(right: 5),
          padding: const EdgeInsets.all(14),
          child: child,
        ),
        if (toolTipAlignment != null)
          Transform.rotate(
            angle: -pi / 4,
            child: Container(
              width: 12,
              height: 12,
              margin: const EdgeInsets.symmetric(vertical: 20),
              decoration: BoxDecoration(
                color: backgroundColor ?? AppColors.chatBubble,
                borderRadius: BorderRadius.circular(0.5),
              ),
            ),
          )
      ],
    );
  }
}

class ChatBubbleWithAvatar extends StatelessWidget {
  const ChatBubbleWithAvatar({
    super.key,
    required this.child,
    this.avatar,
    this.toolTipAlignment,
    this.avatarPosition = AvatarPosition.leftBottom,
    this.avatarSize = 60,
  });

  final Widget? avatar;
  final Widget child;
  final Alignment? toolTipAlignment;
  final AvatarPosition avatarPosition;
  final double avatarSize;

  CrossAxisAlignment get _crossAxisAlignment {
    switch (avatarPosition) {
      case AvatarPosition.leftTop:
        return CrossAxisAlignment.start;
      case AvatarPosition.leftCenter:
        return CrossAxisAlignment.center;
      case AvatarPosition.leftBottom:
        return CrossAxisAlignment.end;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: _crossAxisAlignment,
      children: [
        // Avatar container (can be empty, but space will still be reserved)
        SizedBox(
          width: avatarSize,
          height: avatarSize,
          child: avatar ?? const SizedBox.shrink(),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ChatBubble(
            toolTipAlignment: toolTipAlignment,
            child: child,
          ),
        ),
      ],
    );
  }
}
