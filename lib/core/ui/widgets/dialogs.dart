import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/loader.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/secondary_button.dart';

abstract class Dialogs {
  static Future<T?> showCustomDialog<T>(
    BuildContext context, {
    required Widget content,
  }) {
    return showDialog<T>(
      context: context,
      builder: (context) => Dialog(
        elevation: 5,
        insetPadding: const EdgeInsets.symmetric(horizontal: 24),
        child: content,
      ),
    );
  }

  static void showLoadingDialog(
    BuildContext context, {
    required String message,
  }) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) => PopScope(
        canPop: false,
        child: AlertDialog(
          elevation: 5,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          backgroundColor: AppColors.surfaceSecondary,
          insetPadding: EdgeInsets.symmetric(
            horizontal: MediaQuery.of(context).size.width / 4,
          ),
          content: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 16),
              SizedBox(
                width: 120,
                height: 120,
                child: Loader(),
              ),
              const SizedBox(height: 24),
              Container(
                margin: EdgeInsets.only(left: 7),
                child: Text(
                  message,
                  textAlign: TextAlign.center,
                  style: TextStyles.inputLabel.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Future<bool?> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String description,
    Widget? icon,
    String? cancelButtonText,
    String? acceptButtonText,
  }) =>
      showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          elevation: 5,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          backgroundColor: AppColors.surfaceSecondary,
          insetPadding: EdgeInsets.symmetric(horizontal: 32),
          contentPadding: const EdgeInsets.all(20),
          content: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) icon,
              if (icon != null) const SizedBox(height: 12),
              Text(
                title,
                style: TextStyles.heading4,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Text(
                description,
                style: TextStyles.body1,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: SecondaryButton(
                      onPressed: () =>
                          NavService.popDialog(context, result: false),
                      child: Text(
                        cancelButtonText ??
                            context.L.confirmationDialogDeclineButton,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: PrimaryButton(
                      onPressed: () =>
                          NavService.popDialog(context, result: true),
                      child: Text(
                        acceptButtonText ??
                            context.L.confirmationDialogAcceptButton,
                      ),
                    ),
                  )
                ],
              )
            ],
          ),
        ),
      );
  static Future<T?> showFullScreenDialog<T>(
    BuildContext context, {
    required Widget content,
    Duration transitionDuration = const Duration(milliseconds: 300),
    Color barrierColor = Colors.black54,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      useSafeArea: false,
      builder: (BuildContext context) {
        final Animation<double> animation = ModalRoute.of(context)!.animation!;
        return FadeTransition(
          opacity: CurvedAnimation(
            parent: animation,
            curve: Interval(0.0, 1.0, curve: Curves.easeInOut),
            reverseCurve: Interval(0.0, 1.0, curve: Curves.easeInOut),
          ),
          child: Dialog(
            insetPadding: EdgeInsets.zero, // Full screen
            backgroundColor: Colors.transparent,
            child: Material(
              type: MaterialType.canvas,
              color: Colors.black.withAlpha(150),
              child: content,
            ),
          ),
        );
      },
    );
  }
}
