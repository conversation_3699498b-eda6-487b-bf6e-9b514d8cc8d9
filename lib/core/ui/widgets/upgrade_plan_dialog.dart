import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/ui/widgets/toasts.dart';
import 'package:neuroworld/core/constants/enums/subscription_tier.dart';

class UpgradePlanDialog extends StatelessWidget {
  const UpgradePlanDialog({super.key});

  List<Widget> _buildTiers(BuildContext context) {
    return SubscriptionTier.values
        .map(
          (tier) => Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: AppColors.unwindBlue,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              spacing: 8,
              children: [
                Assets.svgs.badgeCheck.svg(),
                Text(tier.getLabel(context), style: TextStyles.subtitle3),
              ],
            ),
          ),
        )
        .toList();
  }

  Widget _buildHeaderStack(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: 150,
          margin: const EdgeInsets.only(bottom: 20, top: 15),
          width: double.infinity,
          child: Assets.svgs.diamondWithRays.svg(fit: BoxFit.contain),
        ),
        Positioned(
          top: 5,
          right: 5,
          child: IconButton(
            padding: EdgeInsets.zero,
            onPressed: () => NavService.popDialog(context),
            icon: const Icon(
              Icons.close,
              size: 24,
              weight: 900,
              color: AppColors.surfacePrimary,
            ),
          ),
        ),
        Positioned.fill(
          bottom: 20,
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Text(
                      context.L.upgradeYourPlan,
                      style: TextStyles.heading3
                          .copyWith(color: AppColors.surfacePrimary),
                    ),
                    Positioned(
                      top: -10,
                      left: -10,
                      child: Assets.svgs.star.svg(height: 15),
                    ),
                    Positioned(
                      top: -10,
                      right: -10,
                      child: Assets.svgs.star.svg(height: 18),
                    ),
                    Positioned(
                      bottom: -10,
                      right: 52,
                      child: Assets.svgs.star.svg(height: 11),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: const LinearGradient(
          colors: [
            AppColors.upgradePlanGradientStart,
            AppColors.upgradePlanGradientMid,
            AppColors.upgradePlanGradientEnd,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeaderStack(context),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 20, left: 12, right: 12),
            decoration: BoxDecoration(
              color: AppColors.surfacePrimary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              spacing: 12,
              children: [
                ..._buildTiers(context),
                Text(
                  context.L.currentlyOnFreeTier,
                  style: TextStyles.body2,
                  textAlign: TextAlign.center,
                ),
                PrimaryButton(
                  height: 36,
                  onPressed: () => Toasts.showComingSoonToast(context),
                  child: Text(
                    context.L.upgradeNow,
                    style: TextStyles.buttonMedium.copyWith(
                      color: AppColors.surfacePrimary,
                      height: 0.9,
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
