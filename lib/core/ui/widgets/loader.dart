import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';

class Loader extends StatelessWidget {
  const Loader({super.key, this.width, this.height});

  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) => Assets.lottie.loading
      .lottie(frameRate: FrameRate.max, width: width, height: height);
}
