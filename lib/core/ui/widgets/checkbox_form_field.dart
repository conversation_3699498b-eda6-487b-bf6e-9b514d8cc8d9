import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class CheckboxFormField extends FormField<bool> {
  CheckboxFormField({
    super.key,
    Widget? title,
    super.onSaved,
    super.validator,
    bool initialValue = false,
    bool autovalidate = false,
    required value,
    required ValueChanged<bool?> onChanged,
    Widget? label,
  }) : super(
            initialValue: initialValue,
            builder: (FormFieldState<bool> state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      SizedBox(
                        width: 24,
                        height: 24,
                        child: Checkbox(
                          value: value,
                          activeColor: AppColors.boldOrange,
                          onChanged: onChanged,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                          side: BorderSide(
                            width: 2,
                            color: state.hasError
                                ? AppColors.error
                                : AppColors.checkboxBorder,
                          ),
                        ),
                      ),
                      if (label != null) label,
                    ],
                  ),
                  if (state.hasError) const SizedBox(height: 5),
                  if (state.hasError)
                    Text(
                      state.errorText!,
                      style: TextStyles.inputHelper
                          .copyWith(color: AppColors.error),
                    )
                ],
              );
            });
}
