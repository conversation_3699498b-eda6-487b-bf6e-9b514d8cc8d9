import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/data/services/cache_service.dart';
import 'package:neuroworld/core/ui/widgets/loader.dart';

class CachedProfilePicture extends ConsumerWidget {
  const CachedProfilePicture({
    required this.imageUrl,
    this.radius = 100,
    this.spareImageUrl =
        'https://firebasestorage.googleapis.com/v0/b/deliverzler.appspot.com/o/public%2Fplaceholder.png?alt=media',
    this.maxHeightDiskCache = 400,
    this.maxWidthDiskCache = 400,
    super.key,
  });
  final String imageUrl;
  final String spareImageUrl;
  final double radius;
  final int? maxHeightDiskCache;
  final int? maxWidthDiskCache;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cacheService = ref.watch(cacheServiceProvider);

    return CachedNetworkImage(
      cacheManager: cacheService.customCacheManager,
      errorListener: (error) {
        // TODO: handle error and retry logic
      },
      imageUrl: imageUrl,
      imageBuilder: (context, imageProvider) => CircleAvatar(
        radius: radius,
        backgroundImage: imageProvider,
      ),
      placeholder: (context, url) => Loader(),
      errorWidget: (context, url, error) => CircleAvatar(
        radius: radius,
        backgroundImage: Assets.images.logoIos.provider(),
      ),
    );
  }
}
