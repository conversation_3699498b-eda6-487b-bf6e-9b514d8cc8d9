import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class SecondaryButton extends StatelessWidget {
  const SecondaryButton({
    super.key,
    required this.child,
    required this.onPressed,
    this.isGradient = false,
    this.disabled = false,
    this.padding,
    this.textStyle,
    this.height,
    this.backgroundColor,
  });

  final void Function()? onPressed;
  final Widget child;
  final bool isGradient;
  final bool disabled;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final double? height;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border(
          bottom: BorderSide(color: AppColors.stroke, width: 3),
          top: BorderSide(color: AppColors.stroke),
          left: BorderSide(color: AppColors.stroke),
          right: BorderSide(color: AppColors.stroke),
        ),
      ),
      child: IntrinsicHeight(
        child: TextButton(
          onPressed: onPressed,
          style: TextButton.styleFrom(
            foregroundColor: AppColors.textSecondary,
            backgroundColor: backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            elevation: 0,
            textStyle:
                TextStyles.buttonLarge.copyWith(height: 0, fontSize: 17.5),
            padding: padding ?? EdgeInsets.only(top: 2),
          ),
          child: child,
        ),
      ),
    );
  }
}
