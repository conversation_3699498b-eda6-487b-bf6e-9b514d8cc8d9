import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class FrequencySlider extends StatelessWidget {
  const FrequencySlider({
    required this.selectedValue,
    required this.onUpdate,
    super.key,
  });

  final int selectedValue;
  final Function(int) onUpdate;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 8,
            activeTrackColor: AppColors.secondaryBlue,
            inactiveTrackColor: AppColors.stroke.withAlpha(128),
            thumbColor: AppColors.secondaryBlue,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 10),
            activeTickMarkColor: AppColors.secondaryBlue,
            inactiveTickMarkColor: AppColors.secondaryBlue,
          ),
          child: Slider(
            min: 1,
            max: 7,
            divisions: 6,
            value: selectedValue.toDouble(),
            onChanged: (val) => onUpdate(val.toInt()),
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(25, 0, 25, 14),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(7, (index) {
              final isSelected = (index + 1) == selectedValue;
              return Text(
                "${index + 1}",
                style: TextStyles.tooltipHeading.copyWith(
                  color: isSelected
                      ? AppColors.secondaryBlue
                      : AppColors.textTertiary,
                  fontWeight: isSelected ? FontWeight.w800 : FontWeight.w700,
                  fontSize: isSelected ? 16.5 : 15,
                ),
              );
            }),
          ),
        ),
      ],
    );
  }
}
