import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/widgets/custom_toast.dart';

abstract class Toasts {
  static void showErrorToast(
    BuildContext context, {
    required String message,
  }) {
    CustomToast.showToast(
      context,
      message,
      color: AppColors.error,
      backgroundColor: AppColors.errorToastBg,
      icon: Icon(
        Icons.close_rounded,
        color: Colors.white,
        size: 14,
      ),
    );
  }

  static void showComingSoonToast(BuildContext context) {
    CustomToast.showToast(
      context,
      context.L.comingSoon,
      color: AppColors.secondaryBlue,
      backgroundColor: AppColors.unwindBlue,
      icon: Assets.svgs.info.svg(
        colorFilter: ColorFilter.mode(
          Colors.white,
          BlendMode.srcIn,
        ),
      ),
      margin: EdgeInsets.only(bottom: 70),
    );
  }

  static void showSuccessToast(BuildContext context, String message) {
    CustomToast.showToast(
      context,
      message,
      color: AppColors.successToastBorder,
      backgroundColor: AppColors.successToastBg,
      icon: Assets.svgs.success.svg(),
      margin: EdgeInsets.only(bottom: 70),
    );
  }

  static Future<void> showBackgroundMessageToast(
    BuildContext context, {
    required String message,
  }) async {
    await CustomToast.showBackgroundToast(
      context,
      msg: message,
    );
  }
}
