import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class LabeledInputField extends StatelessWidget {
  final String label;
  final Widget inputField; // Keeps the inputField for flexibility
  final String? errorText;

  const LabeledInputField({
    super.key,
    required this.label,
    required this.inputField,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyles.inputLabel.copyWith(color: AppColors.textSecondary),
        ),
        const SizedBox(height: 8),
        inputField,
        if (errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 6),
            child: Text(
              errorText!,
              style: TextStyles.inputHelper.copyWith(color: AppColors.error),
            ),
          ),
      ],
    );
  }
}
