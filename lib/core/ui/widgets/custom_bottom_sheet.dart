import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

abstract class CustomBottomSheet {
  static Future<T?> showBottomSheet<T>(
    BuildContext context, {
    required Widget child,
    bool useRootNavigator = true,
    Color? barrierColor,
    double? height,
    EdgeInsets? padding,
  }) async {
    return showModalBottomSheet<T>(
      context: context,
      useRootNavigator: useRootNavigator,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppColors.surfacePrimary,
          borderRadius: BorderRadius.circular(16),
        ),
        height: height,
        width: double.infinity,
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 24),
        child: Safe<PERSON>rea(
          child: Wrap(
            alignment: WrapAlignment.center,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.bottomSheetHandle,
                  borderRadius: BorderRadius.circular(20),
                ),
                height: 7,
                width: 45,
              ),
              child,
            ],
          ),
        ),
      ),
    );
  }
}
