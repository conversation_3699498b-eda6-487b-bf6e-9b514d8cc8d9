import 'package:flutter/material.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

class RouteErrorWidget extends StatelessWidget {
  const RouteErrorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        ElevatedButton(
          onPressed: () {
            const HomeRoute().go(context);
          },
          child: Text(
            context.L.returnToHome,
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
