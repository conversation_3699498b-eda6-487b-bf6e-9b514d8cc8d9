import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

class RoundedIcon extends StatelessWidget {
  const RoundedIcon({
    super.key,
    required this.child,
    this.size,
    this.height = 30,
    this.width = 30,
    this.borderRadius,
  });

  final Widget child;

  /// size takes precedent over width/height
  final double? size;

  /// size takes precedent over width/height
  final double height;

  /// size takes precedent over width/height
  final double width;

  final double? borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      width: size ?? width,
      height: size ?? height,
      decoration: BoxDecoration(
        color: AppColors.iconBackground,
        borderRadius: BorderRadius.circular(borderRadius ?? 8),
      ),
      padding: const EdgeInsets.all(7),
      child: child,
    );
  }
}
