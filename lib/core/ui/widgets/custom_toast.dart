import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

abstract class CustomToast {
  static void showToast(
    BuildContext context,
    String message, {
    Color color = AppColors.boldOrange,
    Widget? icon,
    Color? backgroundColor,
    Gradient? backgroundGradient,
    double? borderRadius,
    EdgeInsets? margin,
    EdgeInsets? padding,
    ToastGravity toastGravity = ToastGravity.BOTTOM,
    Duration toastDuration = const Duration(seconds: 3),
    Duration fadeDuration = const Duration(milliseconds: 350),
    PositionedToastBuilder? positionedToastBuilder,
  }) {
    FToast().init(context);

    final toast = Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        gradient: backgroundGradient,
        borderRadius: BorderRadius.circular(borderRadius ?? 8),
        border: Border.all(
          color: color,
          width: 1.25,
        ),
      ),
      margin: margin ?? EdgeInsets.zero,
      padding: padding ?? const EdgeInsets.all(14),
      child: SizedBox(
        width: double.infinity,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (icon != null)
              Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: icon,
              ),
            if (icon != null) const SizedBox(width: 15),
            Expanded(
              child: Text(
                message,
                style: TextStyles.body3.copyWith(
                  fontSize: 15.5,
                  color: AppColors.toastText,
                ),
              ),
            ),
            IconButton(
              alignment: Alignment.topRight,
              padding: EdgeInsets.zero,
              onPressed: () => FToast().removeCustomToast(),
              icon: Icon(
                Icons.close,
                size: 20,
                color: AppColors.textSecondary,
              ),
            )
          ],
        ),
      ),
    );

    FToast().removeCustomToast();
    FToast().showToast(
      child: toast,
      gravity: toastGravity,
      toastDuration: toastDuration,
      fadeDuration: fadeDuration,
      positionedToastBuilder: positionedToastBuilder,
    );
  }

  //using Toast without context will be shown even if the app is in background
  static Future<bool?> showBackgroundToast(
    BuildContext context, {
    String msg = '',
    Color? backgroundColor,
    Toast toastLength = Toast.LENGTH_LONG,
    ToastGravity toastGravity = ToastGravity.BOTTOM,
  }) async {
    return Fluttertoast.showToast(
      msg: msg,
      fontSize: 16,
      textColor: AppColors.textPrimary,
      backgroundColor: backgroundColor ?? AppColors.surfacePrimary,
      toastLength: toastLength,
      gravity: toastGravity,
    );
  }
}
