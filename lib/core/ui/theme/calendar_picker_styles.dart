import 'package:cupertino_calendar_picker/cupertino_calendar_picker.dart';
import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class CalendarPickerStyles {
  static final containerDecoration = PickerContainerDecoration(
    backgroundColor: AppColors.surfacePrimary,
    backgroundType: PickerBackgroundType.plainColor,
    boxShadow: [
      BoxShadow(
        color: AppColors.stroke,
        spreadRadius: 0.75,
        blurRadius: 0,
        offset: Offset(0, 0),
      ),
      BoxShadow(
        color: AppColors.mylaFabDropShadow,
        spreadRadius: 1,
        blurRadius: 12,
        offset: Offset(0, 3),
      ),
    ],
  );

  static final headerDecoration = CalendarHeaderDecoration(
    monthDateStyle: TextStyles.heading5,
    monthDateArrowColor: AppColors.boldOrange,
  );
}
