import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/constants/gen/fonts.gen.dart';

abstract class TextStyles {
  // Base style with SFProText font family
  static final TextStyle _baseStyle =
      TextStyle(fontFamily: FontFamily.sFProText, color: AppColors.textPrimary);

  // Heading Styles
  static final TextStyle heading1 = _baseStyle.copyWith(
    fontSize: 32,
    fontWeight: FontWeight.w700,
    height: 40 / 32,
  );

  static final TextStyle heading2 = _baseStyle.copyWith(
    fontSize: 28,
    fontWeight: FontWeight.w700,
    height: 32 / 28,
  );

  static final TextStyle heading3 = _baseStyle.copyWith(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    height: 28 / 24,
  );

  static final TextStyle heading4 = _baseStyle.copyWith(
    fontSize: 20,
    fontWeight: FontWeight.w700,
    height: 24 / 20,
  );

  static final TextStyle heading5 = _baseStyle.copyWith(
    fontSize: 18,
    fontWeight: FontWeight.w700,
    height: 22 / 18,
  );

  static final TextStyle subheading1 = _baseStyle.copyWith(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    height: 22 / 20,
  );

  static final TextStyle subheading2 = _baseStyle.copyWith(
    fontSize: 17,
    fontWeight: FontWeight.w600,
    height: 22 / 17,
  );

  // Subtitle Styles
  static final TextStyle subtitle1 = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 20 / 16,
  );

  static final TextStyle subtitle2 = _baseStyle.copyWith(
    fontSize: 15,
    fontWeight: FontWeight.w500,
    height: 20 / 15,
  );

  static final TextStyle subtitle3 = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 18 / 14,
  );

  // Body Text Styles
  static final TextStyle body1 = _baseStyle.copyWith(
    fontSize: 17,
    fontWeight: FontWeight.w400,
    height: 24 / 17,
  );

  static final TextStyle body2 = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 22 / 16,
  );

  static final TextStyle body3 = _baseStyle.copyWith(
    fontSize: 15,
    fontWeight: FontWeight.w400,
    height: 20 / 15,
  );

  static final TextStyle body4 = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 18 / 14,
  );

  // Button Text Styles
  static final TextStyle buttonLarge = _baseStyle.copyWith(
    fontSize: 17,
    fontWeight: FontWeight.w500,
    height: 26 / 17,
  );

  static final TextStyle buttonMedium = _baseStyle.copyWith(
    fontSize: 15,
    fontWeight: FontWeight.w500,
    height: 24 / 14,
  );

  static final TextStyle buttonSmall = _baseStyle.copyWith(
    fontSize: 13,
    fontWeight: FontWeight.w500,
    height: 22 / 13,
  );

  // Input Text Styles
  static final TextStyle inputLabel = _baseStyle.copyWith(
    fontSize: 15,
    fontWeight: FontWeight.w600,
    height: 15 / 14,
  );

  static final TextStyle inputValue = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 22 / 16,
  );

  static final TextStyle inputHelper = _baseStyle.copyWith(
    fontSize: 13,
    fontWeight: FontWeight.w400,
    height: 18 / 13,
  );

  // Tooltip Text Styles
  static final TextStyle tooltipHeading = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w700,
    height: 19 / 14,
  );

  static final TextStyle tooltipBody = _baseStyle.copyWith(
    fontSize: 15,
    fontWeight: FontWeight.w400,
    height: 20 / 15,
  );

  // Bottom Nav Bar Text Styles
  static final TextStyle navBarLabel = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1,
  );
}
