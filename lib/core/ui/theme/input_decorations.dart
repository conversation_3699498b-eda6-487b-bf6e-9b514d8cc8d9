import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class InputDecorations {
  static final InputDecoration textFieldDecoration = InputDecoration(
    hintStyle:
        TextStyles.inputValue.copyWith(color: AppColors.inputInactiveText),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: AppColors.stroke),
    ),
    disabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: AppColors.stroke),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: AppColors.secondaryBlue, width: 1.25),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: AppColors.error, width: 1.25),
    ),
    errorStyle: TextStyles.inputHelper.copyWith(color: AppColors.error),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: AppColors.error, width: 1.25),
    ),
    contentPadding: const EdgeInsets.symmetric(vertical: 15, horizontal: 16),
    filled: true,
    fillColor: Colors.transparent,
  );

  static final InputDecoration mylaChatInputDecoration = InputDecoration(
    hintStyle:
        TextStyles.inputValue.copyWith(color: AppColors.inputInactiveText),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(50),
      borderSide: BorderSide(color: AppColors.stroke),
    ),
    disabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(50),
      borderSide: BorderSide(color: AppColors.stroke),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(50),
      borderSide: BorderSide(color: AppColors.boldOrange, width: 1.25),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(50),
      borderSide: BorderSide(color: AppColors.error, width: 1.25),
    ),
    errorStyle: TextStyles.inputHelper.copyWith(color: AppColors.error),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(50),
      borderSide: BorderSide(color: AppColors.boldOrange, width: 1.25),
    ),
    contentPadding: const EdgeInsets.symmetric(vertical: 15, horizontal: 18),
    filled: true,
    fillColor: Colors.transparent,
  );
}
