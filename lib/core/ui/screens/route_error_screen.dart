import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/ui/widgets/route_error_widget.dart';

class RouteErrorScreen extends ConsumerWidget {
  const RouteErrorScreen(this.error, {super.key});

  final Exception? error;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (_, __) => const SplashRoute().go(context),
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            SliverFillRemaining(
              hasScrollBody: false,
              child: RouteErrorWidget(),
            ),
          ],
        ),
      ),
    );
  }
}
