import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/app_error_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/ui/providers/app_init_provider.dart';
import 'package:neuroworld/core/ui/providers/initial_route_provider.dart';

class SplashScreen extends ConsumerWidget {
  const SplashScreen({super.key, this.redirect});

  static Future<void> precacheAssets(BuildContext context) async {
    await precacheImage(Assets.images.splash.provider(), context);
  }

  final String? redirect;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appInit = ref.watch(appInitProvider);

    if (!appInit.isLoading) {
      ref.listen<AsyncValue<String>>(
        initialRouteProvider(redirect),
        (prevState, newState) {
          late String nextRoute;
          newState.whenOrNull(
            data: (next) => nextRoute = next,
            error: (e, st) => nextRoute = SplashErrorRoute(
              message: e.errorMessage(context),
            ).location,
          );
          context.go(nextRoute);
        },
      );
    }

    return Scaffold(
      body: Image.asset(
        Assets.images.splash.path,
        height: double.infinity,
        width: double.infinity,
        fit: BoxFit.cover,
      ),
    );
  }
}

/// Widget to show if initialization fails
class AppStartupErrorWidget extends StatelessWidget {
  const AppStartupErrorWidget(
      {super.key, required this.message, required this.onRetry});
  final String message;
  final VoidCallback onRetry;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(message, style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}
