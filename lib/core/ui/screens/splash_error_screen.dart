import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/ui/providers/app_init_provider.dart';

class SplashErrorScreen extends ConsumerWidget {
  const SplashErrorScreen({super.key, required this.message});
  final String message;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(),
      body: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(message, style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(appInitProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}
