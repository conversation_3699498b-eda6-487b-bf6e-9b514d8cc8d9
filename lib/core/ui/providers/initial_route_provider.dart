import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/data/sources/network/network_info.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'initial_route_provider.g.dart';

@riverpod
Future<String> initialRoute(Ref ref, String? deepLinkRedirect) async {
  final hasConnection =
      await ref.watch(networkInfoProvider).hasInternetConnection;
  if (hasConnection) {
    final oldAuth = ref.read(authStateProvider);
    if (oldAuth != null) {
      final auth = await ref.read(authServiceProvider).refreshUser();
      ref.read(authStateProvider.notifier).login(auth);
    }
  } else {
    // app review rejected due to device showing as offline
    // (might be connected to ethernet or vpn)
    // return SplashErrorRoute(message: "No Internet Connection").location;
  }

  return deepLinkRedirect ?? const LandingRoute().location;
}
