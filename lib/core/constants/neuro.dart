import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

enum Neuro {
  nutrition,
  exercise,
  unwind,
  restore,
  optimize;

  const Neuro();

  Color get color => switch (this) {
        Neuro.nutrition => AppColors.nutrition,
        Neuro.exercise => AppColors.exercise,
        Neuro.unwind => AppColors.unwind,
        Neuro.restore => AppColors.restore,
        Neuro.optimize => AppColors.optimize,
      };

  Widget get characterIcon => SvgPicture.asset(
        _getCharacterIconPath(),
      );

  String _getCharacterIconPath() {
    return switch (this) {
      Neuro.nutrition => Assets.svgs.avatars.nuri.path,
      Neuro.exercise => Assets.svgs.avatars.sparkBranch.path,
      Neuro.unwind => Assets.svgs.avatars.luna.path,
      Neuro.restore => Assets.svgs.avatars.flo.path,
      Neuro.optimize => Assets.svgs.avatars.sophi.path,
    };
  }
}
