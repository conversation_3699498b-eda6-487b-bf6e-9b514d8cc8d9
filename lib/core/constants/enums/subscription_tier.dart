import 'package:flutter/material.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

enum SubscriptionTier {
  habits,
  summit,
  academy;

  const SubscriptionTier();

  String getLabel(BuildContext context) {
    return switch (this) {
      SubscriptionTier.habits => context.L.habitsTier,
      SubscriptionTier.summit => context.L.summitTier,
      SubscriptionTier.academy => context.L.academyTier,
    };
  }
}
