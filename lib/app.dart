import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/constants/gen/fonts.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/l10n/app_locale_controller.dart';
import 'package:neuroworld/core/l10n/app_localizations.dart';

class App extends ConsumerWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(goRouterProvider);

    final locale = ref.watch(
          appLocaleControllerProvider.select((data) => data.valueOrNull),
        ) ??
        AppLocale.english;

    return MaterialApp.router(
      routerConfig: router,
      restorationScopeId: 'app',
      builder: (_, child) {
        return GestureDetector(
          onTap: NavService.removeFocus,
          child: child,
        );
      },
      title: 'NEURO World',
      debugShowCheckedModeBanner: false,
      color: AppColors.boldOrange,
      theme: ThemeData(
        fontFamily: FontFamily.sFProText,
        colorScheme: ColorScheme.fromSeed(seedColor: AppColors.boldOrange),
      ),
      locale: Locale(locale.code),
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
    );
  }
}
